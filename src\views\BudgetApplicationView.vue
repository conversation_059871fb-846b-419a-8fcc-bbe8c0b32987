<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
// import ApprovalFlow from '@/views/order/ApprovalFlow.vue'
import { ElMessage } from 'element-plus'
import DepartmentPicker from '@/components/DepartmentPicker.vue'
import type { FinishData } from '@/components/DepartmentPicker.vue'
import AssetTypePicker from '@/components/AssetTypePicker.vue'
import YearPicker from '@/components/YearPicker.vue'
import DateTimePicker from '@/components/DateTimePicker.vue'
import { createBudgetOrderApproval } from '@/api/approval'
import type { PickerOption } from 'vant'
import * as ww from '@wecom/jssdk'
import NewFileUpload from '@/components/newFileUpload.vue'
const router = useRouter()

// 日期选择器相关状态
const showDatePicker = ref(false)
const currentEditingItemIndex = ref(-1)
interface Attachment {
  name: string;
  url: string;
  size: number;
  type: string;
  progress?: number;
  status: 'uploading' | 'success' | 'error';
  errorMessage?: string;
}

interface BudgetItem {
  name: string; // 预算项目名称
  category: string; // 预算类别
  unitPrice?: number; // 单价(元)
  quantity?: number; // 数量
  expectedDate?: string; // 预计使用日期
  purpose?: string; // 用途说明
  typeName?: string
}
interface ChooseRecord {
  name?: string;
  unitPrice?: number;
  quantity?: number;
  useTime?: string;
  remark?: string;
  type?: string
  typeName?: string
}
// 表单数据
const formData = ref({
  departmentName: '', // 申请部门名称
  departmentId: undefined as number | undefined, // 申请部门ID
  budgetYear: new Date().getFullYear().toString(), // 所属预算年，默认为当前年度
  details: [] as BudgetItem[], // 预算项目列表
  purpose: '', // 用途说明
  files: [], // 附件，逗号分隔
  remark: '' // 备注
})

// 打开预购记录选择页面
const openPreorderDialog = () => {
  localStorage.setItem('budgetData', JSON.stringify(formData.value))
  router.push({
    name: 'preorderRecords',
    query: {
      mode: 'select',
      redirect: '/budget-application'
    }
  })
}

// 在组件挂载时检查是否有选中的预购记录
onMounted(() => {
  if (localStorage.getItem('budgetData')) {
    let budgetData = JSON.parse(localStorage.getItem('budgetData') as string)
    formData.value = budgetData
    localStorage.removeItem('budgetData')
  }
  const selectedItems = sessionStorage.getItem('selectedPreorderItems')
  if (selectedItems) {
    const items = JSON.parse(selectedItems)
    console.log('onMountedonMounted', items);
    items.map((record: ChooseRecord) => {
      const obj = {
        name: record.name,
        category: record.type,
        typeName: record.typeName,
        unitPrice: record.unitPrice,
        quantity: record.quantity,
        expectedDate: record.useTime,
        purpose: record.remark
      }
      formData.value.details.push(obj)
    })
    // 清除缓存的选中项
    sessionStorage.removeItem('selectedPreorderItems')
  }
})

// 添加预算项目
const addBudgetItem = () => {
  formData.value.details.push({
    name: '',
    category: '',
    unitPrice: undefined,
    quantity: undefined,
    expectedDate: '',
    purpose: ''
  })
}

// 删除预算项目
const removeBudgetItem = (index: number) => {
  if (formData.value.details.length > 0) {
    formData.value.details.splice(index, 1)
  }
}

// 计算总预算金额
const totalBudgetAmount = computed(() => {
  return formData.value.details.reduce((sum, item) => {
    return sum + (Number(item.unitPrice) * Number(item.quantity) || 0)
  }, 0)
})
// 处理部门选择完成
const handleDepartmentFinish = (data: FinishData) => {
  formData.value.departmentName = data.displayText
}

// 返回上一页
const goBack = () => {
  router.push('/apps')
}

// 提示框状态
const showSuccessModal = ref(false)
const isSubmitting = ref(false)

// 提交表单
const submitForm = async () => {
  // 这里添加表单验证逻辑
  if (!formData.value.departmentId) {
    ElMessage.error('请选择预算所属部门')
    return
  }

  if (!formData.value.budgetYear) {
    ElMessage.error('请选择所属预算年')
    return
  }
  formData.value.details.map(item => delete item.typeName)
  // 验证预算项目
  const hasEmptyBudgetItem = formData.value.details.some(item => !item.name || !item.category || !item.unitPrice || !item.quantity || !item.expectedDate || !item.purpose)
  if (hasEmptyBudgetItem) {
    ElMessage.error('请填写完整的预算项内容')
    return
  }

  try {
    isSubmitting.value = true
    const startTime = Date.now()
    let files = ''
    if (formData.value.files && formData.value.files.length) {
      files = formData.value.files.join(',')
    }
    // 发起请求
    const res = await createBudgetOrderApproval({ ...formData.value, files })

    // 计算已经过去的时间
    const elapsedTime = Date.now() - startTime
    // 如果耗时小于200ms，则等待剩余时间
    if (elapsedTime < 200) {
      await new Promise(resolve => setTimeout(resolve, 200 - elapsedTime))
    }

    // 显示成功提示
    // showSuccessModal.value = true
    // 开始时间
    const url = import.meta.env.VITE_URL
    let link = `${url}#/budget-application-detail?id=${res.id}`
    let templateId = import.meta.env.VITE_ENV === 'prod' ? '9d67021570fb991fa43eff89aa170089_500036314' : 'c2c9482ab06ec7e3eb2220004a6e108a_1669874093'
    console.log( Date.now(), '开始')
    ww.thirdPartyOpenPage({
      oaType: ww.OAType.create_approval,
      templateId,
      thirdNo: 'YSSQ-' + res.id,
      extData: {
        fieldList: [
          {
            title: '申请类型',
            type: ww.OaExtDataType.text,
            value: '预算申请',
          },
          {
              'title': '预算部门',
              'type': ww.OaExtDataType.text,
              'value': formData.value.departmentName,
          },
          {
              'title': '预算总额',
              'type': ww.OaExtDataType.text,
              'value': totalBudgetAmount.value.toFixed(2) + '元',
          },
          {
              'title': '申请时间',
              'type': ww.OaExtDataType.text,
              'value': res.createdAt,
          },
          {
            'title': '备注',
            'type': ww.OaExtDataType.text,
            'value': formData.value.remark,
          },
          {
            type: ww.OaExtDataType.link,
            title: '详细内容',
            // value: 'https://work.weixin.qq.com/dddd?id=123'
            value: link
          }
        ]
      },
      success: () => {
        console.log( Date.now(), '提交成功')
        // router.push('/apps')
      },
      fail: () => {
        console.log( Date.now(), '提交失败')
      },
      complete: () => {
        console.log( Date.now(), '提交完成')
      }
    })

    // // 2秒后跳转到首页
    // setTimeout(() => {
    //   router.push('/apps')
    // }, 2000)
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}
// 处理日期选择
const onDateConfirm = (date: Date) => {
  if (currentEditingItemIndex.value >= 0) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    formData.value.details[currentEditingItemIndex.value].expectedDate = `${year}-${month}-${day}`
  }
  showDatePicker.value = false
}

// 日期选择器格式化函数
const datePickerFormatter = (type: string, option: PickerOption): PickerOption => {
  if (type === 'year') {
    option.text = option.text + '年'
  }
  if (type === 'month') {
    option.text = option.text + '月'
  }
  if (type === 'day') {
    option.text = option.text + '日'
  }
  return option
}
</script>

<template>
  <div class="budget-application-page page-container">
    <!-- 顶部导航 -->
    <header class="budget-header">
      <div class="flex items-center justify-between">
        <button @click="goBack" class="w-10 h-10 flex items-center justify-center rounded-full">
          <i class="fas fa-arrow-left text-gray-700"></i>
        </button>
        <h1 class="text-lg font-medium">预算申请</h1>
        <div class="w-10"></div>
      </div>
    </header>

    <!-- 表单内容 -->
    <main class="budget-content">
      <div class="bg-white rounded-xl p-2 space-y-4">
        <!-- 预算所属部门 -->
        <DepartmentPicker v-model="formData.departmentId" title="预算所属部门" @finish="handleDepartmentFinish" :department-name="formData.departmentName"/>

        <YearPicker v-model="formData.budgetYear" title="所属预算年" />

        <!-- 预算明细 -->
        <div class="form-group">
          <label class="label"><span class="text-red-500">*</span>预算明细</label>
          <div class="space-y-3">
            <div
              v-for="(item, index) in formData.details"
              :key="index"
              class="bg-white border border-gray-100 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <div class="flex justify-between items-center mb-4">
                <span class="text-sm font-medium text-gray-700 bg-gray-50 px-3 py-1 rounded-full">预算项 #{{ index + 1 }}</span>
                <button
                  @click="removeBudgetItem(index)"
                  class="text-gray-400 hover:text-red-500 transition-colors w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-50"
                >
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <div class="grid grid-cols-1 gap-4">
                <div class="col-span-1 tips">
                  <input
                    v-model="item.name"
                    type="text"
                    class="input focus:bg-white transition-colors"
                    placeholder="请输入名称"
                  >
                </div>
                <div class="col-span-1 tips">
                  <AssetTypePicker v-model="item.category" :type-name="item.typeName"/>
                  <!-- <select
                    v-model="item.type"
                    class="input  focus:bg-white transition-colors"
                  >
                    <option value="">请选择资产类型</option>
                    <option v-for="type in budgetTypes" :key="type" :value="type">
                      {{ type }}
                    </option>
                  </select> -->
                </div>
                <div class="grid grid-cols-2 gap-4">
                  <div class="relative tips">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span class="text-gray-500 select-none text-sm">¥</span>
                    </div>
                    <input
                      v-model="item.unitPrice"
                      type="text"
                      inputmode="decimal"
                      class="input pl-8 focus:bg-white transition-colors text-right pr-3"
                      placeholder="预计单价"
                    >
                  </div>
                  <div class="tips">
                    <input
                      v-model="item.quantity"
                      type="number"
                      min="1"
                      class="input  focus:bg-white transition-colors text-right pr-3"
                      placeholder="预购数量"
                    >
                  </div>
                </div>
                <div class="col-span-1 relative pl-[9px]">
                  <span class="tip">*</span>
                  <DateTimePicker v-model="item.expectedDate" title="预计使用日期" placeholder="请选择预计使用日期" hide-title/>
                </div>
                <div class="col-span-1 tips">
                  <input
                    v-model="item.purpose"
                    type="text"
                    class="input  focus:bg-white transition-colors"
                    placeholder="请输入用途说明"
                  >
                </div>
              </div>
              <div class="flex justify-end mt-4 border-t border-gray-100 pt-3">
                <div class="text-sm">
                  <span class="text-gray-500">小计：</span>
                  <span class="font-medium text-primary ml-1 text-lg">¥{{ (Number(item.unitPrice) * Number(item.quantity) || 0).toFixed(2) }}</span>
                </div>
              </div>
            </div>

            <!-- 添加按钮 -->
            <div class="flex gap-3 mt-4">
              <button
                @click="addBudgetItem"
                class="flex-1 flex items-center justify-center py-3 px-4 bg-white border-2 border-dashed border-gray-200 rounded-xl text-gray-500 hover:border-primary hover:text-primary hover:bg-primary/5 transition-all duration-300"
              >
                <i class="fas fa-plus mr-2"></i>
                <span class="font-medium">手动添加</span>
              </button>
              <button
                @click="openPreorderDialog"
                class="flex-1 flex items-center justify-center py-3 px-4 bg-white border-2 border-dashed border-gray-200 rounded-xl text-gray-500 hover:border-primary hover:text-primary hover:bg-primary/5 transition-all duration-300"
              >
                <i class="fas fa-list-ul mr-2"></i>
                <span class="font-medium">从预购记录选择</span>
              </button>
            </div>

            <!-- 总金额 -->
            <div class="flex justify-end items-center space-x-3 pt-4 mt-4 border-t border-gray-200">
              <span class="text-gray-500">总金额：</span>
              <span class="text-2xl font-semibold text-primary">¥{{ totalBudgetAmount.toFixed(2) }}</span>
            </div>
          </div>
        </div>

        <!-- 备注 -->
        <div class="form-group">
          <label class="label">备注</label>
          <textarea
            v-model="formData.remark"
            class="input"
            placeholder="请输入备注信息（选填）"
          ></textarea>
        </div>

        <!-- 附件上传 -->
        <div class="form-group border-b border-gray-200 pb-4">
          <label class="label">附件</label>
          <NewFileUpload v-model="formData.files" />
        </div>
        <!-- <div class="form-group border-b border-gray-200 pb-4">
          <label class="label">图片</label>
          <ImageUpload 
            v-model="imageUrls"
            :max-count="6"
            :max-size="10 * 1024 * 1024" 
            @upload-success="handleSuccess"
            @upload-error="handleError"
          />
        </div> -->
        <!-- 审批流程 -->
        <div class="form-group">
          <!-- <ApprovalFlow class="pl-2" /> -->
        </div>
      </div>
    </main>

    <!-- 日期选择弹窗 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        title="选择日期"
        :min-date="new Date()"
        :formatter="datePickerFormatter"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 底部按钮 -->
    <footer class="budget-footer">
      <van-button
        type="primary"
        block
        :loading="isSubmitting"
        loading-text="准备中..."
        :disabled="isSubmitting"
        @click="submitForm"
        size="large"
      >
        下一步
      </van-button>
    </footer>

    <!-- 成功提示弹窗 -->
    <div
      v-if="showSuccessModal"
      class="fixed inset-0 flex items-center justify-center z-50"
    >
      <!-- 背景遮罩 -->
      <div
        class="absolute inset-0 bg-black transition-opacity duration-300"
        :class="showSuccessModal ? 'bg-opacity-60' : 'bg-opacity-0'"
      ></div>

      <!-- 弹窗内容 -->
      <div
        class="bg-white rounded-2xl p-8 relative z-10 max-w-sm w-full mx-4 transform transition-all duration-500 ease-out"
        :class="showSuccessModal ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-4 scale-95'"
      >
        <div class="text-center">
          <!-- 成功图标 -->
          <div class="w-20 h-20 mx-auto mb-6 relative">
            <div class="absolute inset-0 bg-green-100 rounded-full animate-ripple"></div>
            <div class="absolute inset-0 flex items-center justify-center">
              <i class="fas fa-check text-3xl text-green-500 animate-success-icon"></i>
            </div>
          </div>

          <!-- 文本内容 -->
          <h3 class="text-xl font-semibold text-gray-900 mb-3">提交成功</h3>
          <p class="text-gray-600 mb-6">您的预算申请已成功提交，我们会尽快处理</p>

          <button
            @click="router.push('/apps')"
            class="px-6 py-2 bg-primary text-white rounded-lg font-medium hover:bg-primary-dark transition-colors"
          >
            返回首页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.budget-application-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.budget-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: white;
  padding: 1rem;
  border-bottom: 1px solid #eaeaea;
}

.budget-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  margin-top: 3.5rem;
  margin-bottom: 5rem;
}

.budget-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1rem;
  background-color: white;
  border-top: 1px solid #eaeaea;
  z-index: 5;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group.horizontal {
  display: flex;
  align-items: center;
}

.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #374151;
}

.form-group.horizontal .label {
  width: 7rem;
  margin-bottom: 0;
}

.input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.input:disabled {
  background-color: #f9fafb;
  cursor: not-allowed;
}
.tips {
  display: flex;
}
.tips::before {
  content: '* ';
  color: red;
  margin-right: 2px; 
}
.tip {
  position: absolute;
  color: red;
  left: 0px;
}
/* 动画 */
@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

@keyframes success-icon {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-ripple {
  animation: ripple 1.5s ease-out infinite;
}

.animate-success-icon {
  animation: success-icon 0.5s ease-out forwards;
}

/* 隐藏滚动条但保留功能 */
.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari, Opera */
}
</style>
