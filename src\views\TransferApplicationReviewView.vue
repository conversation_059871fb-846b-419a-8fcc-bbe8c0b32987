<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-medium">调拨申请单</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <!-- 申请表单 -->
    <div class="form-content">
      <form class="space-y-6">
        <!-- 基本信息 -->
        <BasicInfoCard
          :applicant="formData.applicant"
          :department="formData.department"
          :apply-time="formData.applyTime"
        />

        <!-- 调入部门 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4 space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                接收部门
              </label>
              <div class="w-full p-2 bg-gray-50 rounded-md">
                {{ getDepartmentName(formData.toDepartment) }}
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                接收人员
              </label>
              <div class="w-full p-2 bg-gray-50 rounded-md">
                {{ getUserName(formData.toUser) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 调拨资产列表 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <div class="flex justify-between items-center mb-4">
              <label class="block text-sm font-medium text-gray-700">
                调拨资产
              </label>
            </div>
            
            <div v-if="selectedAssets.length === 0" class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg">
              <i class="fas fa-inbox text-3xl mb-2"></i>
              <p>暂未选择资产</p>
            </div>
            
            <div v-else class="space-y-3">
              <div 
                v-for="asset in selectedAssets" 
                :key="asset.id" 
                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-900">{{ asset.name }}</div>
                  <div class="text-xs text-gray-500">资产编号：{{ asset.code }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 调拨原因 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              调拨原因
            </label>
            <div class="w-full p-2 bg-gray-50 rounded-md min-h-[100px]">
              {{ formData.reason }}
            </div>
          </div>
        </div>

        <!-- 附件列表 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <div class="flex justify-between items-center mb-4">
              <label class="text-sm font-medium text-gray-700">附件列表</label>
            </div>

            <div v-if="formData.attachments.length === 0" class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg">
              <i class="fas fa-cloud-upload-alt text-3xl mb-2"></i>
              <p>暂无附件</p>
            </div>

            <div v-else class="space-y-3">
              <div 
                v-for="(file, index) in formData.attachments" 
                :key="index"
                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div class="flex-1 min-w-0">
                  <div class="flex items-center">
                    <i class="fas fa-file text-gray-400 mr-2"></i>
                    <span class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</span>
                  </div>
                  <div class="text-xs text-gray-500 mt-1">{{ formatFileSize(file.size) }}</div>
                </div>
                <a 
                  href="#"
                  class="text-blue-600 hover:text-blue-700"
                  @click.prevent="downloadFile(file)"
                >
                  <i class="fas fa-download"></i>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- 审批流程 -->
        <div class="bg-white rounded-lg shadow-sm">
          <ApprovalFlow />
        </div>
      </form>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
      <div class="flex space-x-4">
        <button 
          type="button"
          @click="handleReject"
          class="flex-1 px-4 py-2 border border-red-600 rounded-md text-red-600 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          :disabled="isSubmitting"
        >
          {{ isSubmitting ? '处理中...' : '驳回' }}
        </button>
        <button 
          type="button"
          @click="handleApprove"
          class="flex-1 px-4 py-2 border border-transparent rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          :disabled="isSubmitting"
        >
          {{ isSubmitting ? '处理中...' : '通过' }}
        </button>
      </div>
    </div>

    <!-- 审批结论弹窗 -->
    <el-dialog
      v-model="showApprovalConclusion"
      :title="approvalType === 'approve' ? '审批通过' : '审批驳回'"
      width="95%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      class="approval-dialog"
      destroy-on-close
      top="10vh"
    >
      <ApprovalConclusion
        :type="approvalType"
        @confirm="handleApprovalConfirm"
        @cancel="handleApprovalCancel"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElDialog } from 'element-plus'
import 'element-plus/es/components/dialog/style/css'
import ApprovalFlow from '@/views/order/ApprovalFlow.vue'
import ApprovalConclusion from '@/views/order/ApprovalConclusion.vue'
import BasicInfoCard from '@/views/order/BasicInfoCard.vue'

const router = useRouter()
const route = useRoute()

interface Department {
  id: number;
  name: string;
}

interface Asset {
  id: number;
  name: string;
  code: string;
  status?: string;
}

interface User {
  id: number;
  name: string;
  departmentId: number;
}

interface Attachment {
  name: string;
  size: number;
  file: File;
  status: 'uploading' | 'success' | 'error';
  progress?: number;
}

interface ApprovalForm {
  comment: string;
  signature: string;
}

// 模拟部门数据
const departments: Department[] = [
  { id: 1, name: '技术部' },
  { id: 2, name: '财务部' },
  { id: 3, name: '人事部' },
  { id: 4, name: '行政部' }
]

// 模拟用户数据
const users: User[] = [
  { id: 1, name: '张三', departmentId: 1 },
  { id: 2, name: '李四', departmentId: 1 },
  { id: 3, name: '王五', departmentId: 2 },
  { id: 4, name: '赵六', departmentId: 2 },
  { id: 5, name: '钱七', departmentId: 3 },
  { id: 6, name: '孙八', departmentId: 3 },
  { id: 7, name: '周九', departmentId: 4 },
  { id: 8, name: '吴十', departmentId: 4 }
]

// 表单数据
const formData = reactive({
  applicant: '张三',
  department: '技术部',
  applyTime: '2024-03-14 10:00',
  toDepartment: 2, // 默认选择财务部
  toUser: 3, // 默认选择王五
  reason: '由于部门重组，需要将部分办公设备调拨至财务部使用。',
  attachments: [
    {
      name: '部门重组文件.pdf',
      size: 1024 * 1024 * 2.5, // 2.5MB
      status: 'success',
      file: new File([], '部门重组文件.pdf')
    },
    {
      name: '资产清单.xlsx',
      size: 1024 * 512, // 512KB
      status: 'success',
      file: new File([], '资产清单.xlsx')
    }
  ] as Attachment[]
})

// 选中的资产列表
const selectedAssets = ref<Asset[]>([
  {
    id: 1,
    name: 'ThinkPad X1 Carbon',
    code: 'NB001',
    status: '在用'
  },
  {
    id: 2,
    name: 'Dell P2415Q 显示器',
    code: 'DS001',
    status: '在用'
  },
  {
    id: 4,
    name: '惠普打印机',
    code: 'PT001',
    status: '在用'
  }
])

const isSubmitting = ref(false)

// 审批结论弹窗控制
const showApprovalConclusion = ref(false)
const approvalType = ref<'approve' | 'reject'>('approve')

// 获取部门名称
const getDepartmentName = (id: number) => {
  const dept = departments.find(d => d.id === id)
  return dept ? dept.name : ''
}

// 获取用户名称
const getUserName = (id: number) => {
  const user = users.find(u => u.id === id)
  return user ? user.name : ''
}

// 监听路由参数变化，获取申请单详情
onMounted(async () => {
  const orderId = route.query.id
  if (orderId) {
    try {
      // TODO: 这里应该调用API获取申请单详情
      // 目前使用模拟数据
      console.log('获取申请单详情:', orderId)
    } catch (error) {
      console.error('获取申请单详情失败:', error)
      ElMessage.error('获取申请单详情失败')
    }
  }
})

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${(bytes / Math.pow(k, i)).toFixed(1)} ${sizes[i]}`
}

// 下载文件
const downloadFile = (file: Attachment) => {
  // TODO: 实现文件下载逻辑
  ElMessage.info('文件下载功能开发中')
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 处理通过
const handleApprove = () => {
  if (isSubmitting.value) return
  approvalType.value = 'approve'
  showApprovalConclusion.value = true
}

// 处理驳回
const handleReject = () => {
  if (isSubmitting.value) return
  approvalType.value = 'reject'
  showApprovalConclusion.value = true
}

// 处理审批结论确认
const handleApprovalConfirm = async (form: ApprovalForm) => {
  console.log('审批结论确认', form.comment, form.signature);
  try {
    isSubmitting.value = true
    // TODO: 实现审批提交逻辑
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success(approvalType.value === 'approve' ? '审批通过！' : '已驳回申请！')
    showApprovalConclusion.value = false
    router.back()
  } catch (error) {
    console.error('审批失败:', error)
    ElMessage.error('审批失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

// 处理审批结论取消
const handleApprovalCancel = () => {
  showApprovalConclusion.value = false
}
</script>

<style scoped>
.page-container {
  background-color: rgb(249, 250, 251);
  min-height: 100vh;
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.page-container::-webkit-scrollbar {
  display: none;
}

.form-content {
  padding: 64px 8px 96px;
  min-height: calc(100vh - 64px);
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.form-content::-webkit-scrollbar {
  display: none;
}

form {
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

form::-webkit-scrollbar {
  display: none;
}

:deep(.approval-dialog) {
  border-radius: 8px;
  
  .el-dialog__header {
    margin: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
  }
  
  .el-dialog__title {
    font-size: 16px;
    font-weight: 500;
    color: #1f2937;
  }
  
  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
  }

  @media screen and (min-width: 768px) {
    width: 600px !important;
    margin: 0 auto;
  }
}
</style> 