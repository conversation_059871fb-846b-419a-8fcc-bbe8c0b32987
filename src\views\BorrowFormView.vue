<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import axios from 'axios'
import type { AxiosProgressEvent } from 'axios'
import ApprovalFlow from '@/views/order/ApprovalFlow.vue'  // 导入审批流程组件

const router = useRouter()
const route = useRoute()

// 文件上传配置
const FILE_SIZE_LIMIT = 10 * 1024 * 1024; // 10MB
const ALLOWED_FILE_TYPES = {
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/vnd.ms-excel': ['.xls'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
};

// 表单数据
interface BorrowItem {
  id: number;
  name: string;
  model: string;
  image: string;
  quantity: number;
  selected: boolean;
  borrowDays: number;
  warehouse: string; // 添加仓库字段
}

interface Attachment {
  name: string;
  url: string;
  size: number;
  type: string;
  progress?: number;
  status: 'uploading' | 'success' | 'error';
  errorMessage?: string;
}

const formData = ref({
  department: '', // 所属部门
  borrowItems: [] as BorrowItem[], // 借用物品列表
  returnDate: '', // 归还日期
  purpose: '', // 事由
  remark: '', // 备注
  attachments: [] as Attachment[] // 添加附件数组
})

// 部门列表
const departments = [
  '技术部',
  '财务部',
  '人力资源部',
  '市场部',
  '销售部',
  '行政部'
]

// 计算总数量
const totalQuantity = computed(() => {
  return formData.value.borrowItems.reduce((sum, item) => sum + item.quantity, 0)
})

// 返回上一页
const goBack = () => {
  router.back()
}

// 按仓库分组商品
const groupedBorrowItems = computed(() => {
  const groups = new Map<string, BorrowItem[]>();
  formData.value.borrowItems.forEach(item => {
    if (!groups.has(item.warehouse)) {
      groups.set(item.warehouse, []);
    }
    groups.get(item.warehouse)?.push(item);
  });
  return groups;
});

// 提示框状态
const showSuccessModal = ref(false)

// 提交表单
const submitForm = async () => {
  // 这里添加表单验证逻辑
  if (!formData.value.department) {
    alert('请选择所属部门')
    return
  }
  if (!formData.value.returnDate) {
    alert('请选择归还日期')
    return
  }
  if (!formData.value.purpose) {
    alert('请填写事由')
    return
  }
  if (formData.value.borrowItems.length === 0) {
    alert('请选择要借用的物品')
    return
  }
  
  try {
    // TODO: 这里添加提交到后端的逻辑
    console.log('提交的表单数据：', formData.value)
    
    // 显示成功提示
    showSuccessModal.value = true
    
    // 3秒后自动返回
    setTimeout(() => {
      showSuccessModal.value = false
      router.back()
    }, 3000)
  } catch (error) {
    console.error('提交失败：', error)
    alert('提交失败，请重试')
  }
}

// 初始化表单
const initBorrowItems = () => {
  const cartItems = localStorage.getItem('cartItems')
  console.log('从 localStorage 获取的购物车数据:', cartItems)
  if (!cartItems) {
    console.log('购物车为空')
    formData.value.borrowItems = []
    return
  }

  try {
    const items = JSON.parse(cartItems)
    console.log('解析后的购物车数据:', items)
    // 只获取勾选的商品
    const selectedItems = items.filter((item: any) => item.selected)
    console.log('勾选的商品:', selectedItems)
    
    if (selectedItems.length === 0) {
      console.log('没有勾选的商品')
      formData.value.borrowItems = []
      return
    }

    // 转换为借用明细
    formData.value.borrowItems = selectedItems.map((item: any) => {
      console.log('处理商品:', item)
      return {
        id: item.id,
        name: item.title,  // 从 title 映射到 name
        model: item.specs ? `${item.specs.color} · ${item.specs.size}` : '',  // 从 specs 映射到 model
        image: item.image,
        quantity: item.quantity,
        selected: item.selected,
        borrowDays: 7, // 默认借用7天
        warehouse: item.warehouse || '默认仓库'
      }
    })
    console.log('最终的借用明细:', formData.value.borrowItems)
  } catch (error) {
    console.error('解析购物车数据失败:', error)
    formData.value.borrowItems = []
  }
}

// 在组件挂载时初始化
onMounted(() => {
  console.log('组件挂载，开始初始化数据')
  initBorrowItems()
})

// 处理文件上传
const handleFileUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (!input.files?.length) return;

  const files = Array.from(input.files);
  
  for (const file of files) {
    // 检查文件大小
    if (file.size > FILE_SIZE_LIMIT) {
      alert(`文件 ${file.name} 超过大小限制 10MB`);
      continue;
    }

    // 检查文件类型
    if (!Object.keys(ALLOWED_FILE_TYPES).includes(file.type)) {
      alert(`文件 ${file.name} 格式不支持`);
      continue;
    }

    // 创建附件对象
    const attachment: Attachment = {
      name: file.name,
      url: URL.createObjectURL(file),
      size: file.size,
      type: file.type,
      status: 'uploading',
      progress: 0
    };

    // 添加到附件列表
    formData.value.attachments.push(attachment);

    try {
      const formDataToUpload = new FormData();
      formDataToUpload.append('file', file);

      // 使用 axios 替代 fetch 以获得更好的上传进度支持
      const response = await axios.post('/api/upload', formDataToUpload, {
        onUploadProgress: (progressEvent: AxiosProgressEvent) => {
          const index = formData.value.attachments.findIndex(item => item.name === file.name);
          if (index !== -1 && progressEvent.total) {
            formData.value.attachments[index].progress = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
          }
        }
      });

      // 更新附件状态
      const index = formData.value.attachments.findIndex(item => item.name === file.name);
      if (index !== -1) {
        formData.value.attachments[index].status = 'success';
        formData.value.attachments[index].url = response.data.url;
      }
    } catch (error: unknown) {
      const index = formData.value.attachments.findIndex(item => item.name === file.name);
      if (index !== -1) {
        formData.value.attachments[index].status = 'error';
        formData.value.attachments[index].errorMessage = error instanceof Error ? error.message : '上传失败';
      }
    }
  }

  // 清空input，允许重复上传相同文件
  input.value = '';
}

// 预览附件
const previewAttachment = (attachment: Attachment) => {
  if (attachment.type.startsWith('image/')) {
    // 图片预览
    window.open(attachment.url, '_blank');
  } else {
    // 文档预览（可以根据实际情况使用文档预览服务）
    window.open(attachment.url, '_blank');
  }
}

// 删除附件
const removeAttachment = (index: number) => {
  URL.revokeObjectURL(formData.value.attachments[index].url);
  formData.value.attachments.splice(index, 1);
}
</script>

<template>
  <div class="page-container min-h-screen bg-gray-50 overflow-y-auto">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-bold">借用申请</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="pt-14 pb-4">
      <div class="bg-white p-2.5 space-y-6 mb-4">
        <!-- 所属部门 -->
        <div class="form-group">
          <label class="label"><span class="text-red-500">*</span>所属部门</label>
          <select
            v-model="formData.department"
            class="input"
          >
            <option value="">请选择部门</option>
            <option v-for="dept in departments" :key="dept" :value="dept">
              {{ dept }}
            </option>
          </select>
        </div>

        <!-- 归还时间 -->
        <div class="form-group">
          <label class="label"><span class="text-red-500">*</span>归还日期</label>
          <div class="relative">
            <input
              v-model="formData.returnDate"
              type="date"
              class="input pr-10"
              :min="new Date().toISOString().split('T')[0]"
              placeholder="请选择归还日期"
            >
            <i class="fas fa-calendar-alt absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"></i>
          </div>
        </div>

        <!-- 事由 -->
        <div class="form-group">
          <label class="label"><span class="text-red-500">*</span>事由</label>
          <textarea
            v-model="formData.purpose"
            class="input min-h-[100px]"
            placeholder="请详细说明借用事由"
          ></textarea>
        </div>

        <!-- 借用明细 -->
        <div class="form-group">
          <label class="label">借用明细</label>
          <div v-if="formData.borrowItems.length > 0" class="space-y-4">
            <template v-for="[warehouse, items] in groupedBorrowItems" :key="warehouse">
              <!-- 仓库分类 -->
              <div class="bg-white rounded-lg overflow-hidden border border-gray-100">
                <div class="bg-gray-50 px-4 py-2.5 flex items-center justify-between">
                  <h3 class="text-sm font-medium text-gray-700">{{ warehouse }}</h3>
                  <span class="text-xs text-gray-500">{{ items.length }}件物品</span>
                </div>
                <!-- 商品列表 -->
                <div class="divide-y divide-gray-100">
                  <div 
                    v-for="(item, index) in items" 
                    :key="index"
                    class="p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div class="flex items-start space-x-4">
                      <!-- 商品图片 -->
                      <div class="flex-shrink-0">
                        <img 
                          :src="item.image" 
                          :alt="item.name"
                          class="w-20 h-20 object-cover rounded-lg border border-gray-200 bg-white"
                        >
                      </div>
                      <!-- 商品信息 -->
                      <div class="flex-1 min-w-0 py-1">
                        <div class="text-base font-medium text-gray-900 mb-2">{{ item.name || '商品名称' }}</div>
                        <div class="text-sm text-gray-500">型号：{{ item.model }}</div>
                        <div class="text-sm text-gray-500 mt-1">数量：{{ item.quantity }}件</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>

            <!-- 总数量显示 -->
            <div class="flex justify-between items-center px-2 text-sm text-gray-500">
              <span>总计</span>
              <span>{{ totalQuantity }}件物品</span>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-else class="text-center py-8 bg-gray-50 rounded-lg">
            <i class="fas fa-box-open text-gray-300 text-3xl mb-3"></i>
            <div class="text-sm text-gray-500">暂无借用物品，请先在购物车中选择要借用的物品</div>
          </div>
        </div>

        <!-- 备注 -->
        <div class="form-group">
          <label class="label">备注</label>
          <textarea
            v-model="formData.remark"
            class="input"
            placeholder="请输入备注信息（选填）"
          ></textarea>
        </div>

        <!-- 附件上传 -->
        <div class="form-group">
          <div class="flex items-center space-x-3">
            <label class="label shrink-0">附件</label>
            <div class="relative shrink-0">
              <input
                type="file"
                multiple
                class="hidden"
                @change="handleFileUpload"
                id="file-upload"
                accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
              >
              <label
                for="file-upload"
                class="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 bg-white hover:bg-gray-50"
              >
                <i class="fas fa-upload mr-2"></i>
                上传附件
              </label>
            </div>
          </div>
          <div class="text-xs text-gray-500 mt-1">
            支持以下格式：JPG、PNG、PDF、Word、Excel，单个文件不超过10MB
          </div>

          <!-- 附件列表 -->
          <div v-if="formData.attachments.length > 0" class="space-y-2 mt-3">
            <div
              v-for="(file, index) in formData.attachments"
              :key="index"
              class="relative p-3 bg-gray-50 rounded-lg"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2 flex-1 min-w-0">
                  <!-- 文件图标 -->
                  <i 
                    :class="[
                      'fas',
                      file.type.startsWith('image/') ? 'fa-image' :
                      file.type.includes('pdf') ? 'fa-file-pdf' :
                      file.type.includes('word') ? 'fa-file-word' :
                      file.type.includes('sheet') ? 'fa-file-excel' :
                      'fa-file'
                    ]"
                    class="text-gray-400"
                  ></i>
                  
                  <!-- 文件信息 -->
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center space-x-2">
                      <div class="text-sm text-gray-700 truncate">{{ file.name }}</div>
                      <span 
                        :class="[
                          'text-xs px-2 py-0.5 rounded',
                          file.status === 'success' ? 'bg-green-100 text-green-800' :
                          file.status === 'error' ? 'bg-red-100 text-red-800' :
                          'bg-blue-100 text-blue-800'
                        ]"
                      >
                        {{ 
                          file.status === 'success' ? '已上传' :
                          file.status === 'error' ? '上传失败' :
                          '上传中'
                        }}
                      </span>
                    </div>
                    <div class="text-xs text-gray-500">{{ (file.size / 1024).toFixed(1) }}KB</div>
                    
                    <!-- 错误信息 -->
                    <div v-if="file.status === 'error'" class="text-xs text-red-500 mt-1">
                      {{ file.errorMessage }}
                    </div>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center space-x-2">
                  <button
                    v-if="file.status === 'success'"
                    @click="previewAttachment(file)"
                    class="text-blue-600 hover:text-blue-800"
                    title="预览"
                  >
                    <i class="fas fa-eye"></i>
                  </button>
                  <button
                    @click="removeAttachment(index)"
                    class="text-gray-400 hover:text-red-500"
                    title="删除"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>

              <!-- 上传进度条 -->
              <div 
                v-if="file.status === 'uploading'"
                class="mt-2"
              >
                <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    class="h-full bg-blue-500 transition-all duration-300"
                    :style="{ width: `${file.progress}%` }"
                  ></div>
                </div>
                <div class="text-xs text-gray-500 mt-1">
                  {{ file.progress }}%
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 审批流程 -->
        <div class="border-t border-gray-100 mt-6 pt-4 -mx-4">
          <ApprovalFlow />
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-2.5 py-3">
      <button
        @click="submitForm"
        class="w-full py-2.5 px-4 bg-primary text-white rounded-lg"
      >
        提交申请
      </button>
    </div>

    <!-- 成功提示弹窗 -->
    <div
      v-if="showSuccessModal"
      class="fixed inset-0 flex items-center justify-center z-50"
    >
      <!-- 背景遮罩 -->
      <div 
        class="absolute inset-0 bg-black transition-opacity duration-300"
        :class="showSuccessModal ? 'bg-opacity-60' : 'bg-opacity-0'"
      ></div>
      
      <!-- 弹窗内容 -->
      <div 
        class="bg-white rounded-2xl p-8 relative z-10 max-w-sm w-full mx-4 transform transition-all duration-500 ease-out"
        :class="showSuccessModal ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-4 scale-95'"
      >
        <div class="text-center">
          <!-- 成功图标 -->
          <div class="w-20 h-20 mx-auto mb-6 relative">
            <div class="absolute inset-0 bg-green-100 rounded-full animate-ripple"></div>
            <div class="absolute inset-0 flex items-center justify-center">
              <i class="fas fa-check text-3xl text-green-500 animate-success-icon"></i>
            </div>
          </div>
          
          <!-- 文本内容 -->
          <h3 class="text-xl font-semibold text-gray-900 mb-3">提交成功</h3>
          <p class="text-gray-600 mb-6">您的借用申请已成功提交，我们会尽快处理</p>
          
          <!-- 进度条 -->
          <div class="w-full bg-gray-100 rounded-full h-1 mb-3 overflow-hidden">
            <div class="h-full bg-green-500 animate-progress"></div>
          </div>
          <div class="text-sm text-gray-500">即将返回上一页...</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.form-group {
  @apply space-y-1;
}

.label {
  @apply text-sm text-gray-600 block;
}

.input {
  @apply w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-primary;
  -webkit-appearance: none;
  appearance: none;
}

/* 日期选择器样式 */
input[type="date"] {
  position: relative;
  background-color: transparent;
  cursor: pointer;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  color: transparent;
  cursor: pointer;
}

input[type="date"]::-webkit-datetime-edit {
  padding: 0;
}

input[type="date"]::-webkit-inner-spin-button {
  display: none;
}

/* 在未选择日期时显示占位符文本颜色 */
input[type="date"]:invalid {
  color: #9ca3af;
}

.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(24 144 255 / var(--tw-bg-opacity));
}

.text-primary {
  --tw-text-opacity: 1;
  color: rgb(24 144 255 / var(--tw-text-opacity));
}

.border-primary {
  --tw-border-opacity: 1;
  border-color: rgb(24 144 255 / var(--tw-border-opacity));
}

/* 隐藏滚动条 */
.page-container {
  scrollbar-width: none;
  -ms-overflow-style: none;
  height: 100vh;
  overflow-y: auto;
}

.page-container::-webkit-scrollbar {
  width: 0;
  display: none;
}

/* 确保内容区域也不显示滚动条 */
.page-container > div::-webkit-scrollbar {
  width: 0;
  display: none;
}

html, body {
  overflow: hidden;
}

/* 添加动画效果 */
.transform {
  --tw-translate-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
}

.duration-300 {
  transition-duration: 300ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 弹窗动画 */
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

/* 动画效果 */
@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

@keyframes success-icon {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes progress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

.animate-ripple {
  animation: ripple 2s ease-out infinite;
}

.animate-success-icon {
  animation: success-icon 0.5s ease-out forwards;
}

.animate-progress {
  animation: progress 3s linear forwards;
}

/* 过渡效果 */
.transition-opacity {
  transition-property: opacity;
}

.transition-all {
  transition-property: all;
}

.duration-500 {
  transition-duration: 500ms;
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.translate-y-0 {
  transform: translateY(0);
}

.translate-y-4 {
  transform: translateY(1rem);
}

.scale-95 {
  transform: scale(0.95);
}

.scale-100 {
  transform: scale(1);
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}
</style> 