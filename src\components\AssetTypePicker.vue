<script setup lang="ts">
import { ref, watch } from 'vue'
import { hpMaterialType } from '@/api/equipment'

interface AssetTypeOption {
  id: string | number;
  name: string;
  children?: AssetTypeOption[];
}

interface Props {
  modelValue?: string | number;
  title?: string;
  typeName?: string;
}

interface EmitType {
  (e: 'update:modelValue', value: string | number): void;
  (e: 'finish', value: { selectedOptions: AssetTypeOption[] }): void;
  (e: 'change', value: string | number): void;
}

const props = withDefaults(defineProps<Props>(), {
  title: '请选择资产类型',
  modelValue: '',
  typeName: '',
  options: () => []
})

const emit = defineEmits<EmitType>()

const showPicker = ref(false)
const selectedText = ref('')
const options = ref<AssetTypeOption[]>([])

// 处理选择完成
const onFinish = ({ selectedOptions }: { selectedOptions: AssetTypeOption[] }) => {
  selectedText.value = selectedOptions.map(option => option.name).join('/')
  showPicker.value = false
  emit('finish', { selectedOptions })
}

// 处理关闭
const onClose = () => {
  showPicker.value = false
}

// 打开选择器
const openPicker = () => {
  showPicker.value = true
}

// 监听值变化
watch(() => props.modelValue, (newVal) => {
  if (!newVal) {
    selectedText.value = ''
  }
})

// 移除空的 children 数组
const removeEmptyChildren = (arr: AssetTypeOption[]): AssetTypeOption[] => {
  if (!arr || !Array.isArray(arr)) return []

  return arr.map((item) => {
    // 浅拷贝对象（避免修改原对象）
    const newItem = { ...item }

    // 递归处理子节点
    if (newItem.children && Array.isArray(newItem.children)) {
      const processedChildren = removeEmptyChildren(newItem.children)
      if (processedChildren.length > 0) {
        newItem.children = processedChildren
      } else {
        delete newItem.children // 删除空children字段
      }
    }

    return newItem
  })
}


const loadMaterialTypeData = async () => {
  const res = await hpMaterialType()
  options.value = removeEmptyChildren(res.items)
  console.log('options', options.value)
}

// 监听显示状态变化
watch(showPicker, (newVal) => {
  if (newVal) {
    loadMaterialTypeData()
  }
})

</script>

<template>
  <div class="asset-type-picker">
    <!-- 选择器触发器 -->
    <div class="flex-1">
      <div
        class="w-full px-4 py-2 border border-gray-300 rounded-lg text-base bg-white cursor-pointer"
        @click="openPicker"
      >
        <span v-if="modelValue">{{ selectedText || typeName }}</span>
        <span v-else class="text-gray-400">请选择资产类型</span>
      </div>
    </div>

    <!-- 选择器弹窗 -->
    <van-popup
      v-model:show="showPicker"
      round
      position="bottom"
    >
      <van-cascader
        :model-value="modelValue"
        :title="title"
        :options="options"
        :field-names="{ text: 'name', value: 'id' }"
        @update:model-value="(val) => emit('update:modelValue', val)"
        @close="onClose"
        @finish="onFinish"
      />
    </van-popup>
  </div>
</template>

<style scoped>
.asset-type-picker {
  width: 100%;
}
</style>
