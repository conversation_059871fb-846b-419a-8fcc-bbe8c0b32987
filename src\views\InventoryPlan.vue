<template>
  <div class="page-container min-h-screen bg-gray-50 no-scrollbar">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="onClickLeft" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-bold">{{ (route.meta.title as string) || '盘点计划' }}</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="pt-14 pb-4 px-4 overflow-y-auto h-full no-scrollbar">
      <List :request-fn="fetchData" v-slot="{ list }">
        <div class="space-y-4">
          <div 
            v-for="plan in list" 
            :key="plan.id"
            class="bg-white rounded-lg p-4 shadow-sm"
            @click="plan.status !== 3 &&handlePlanDetail(plan.id)"
          >
            <div class="flex items-start justify-between mb-4">
              <div class="flex-1 min-w-0 mr-4">
                <h3 class="text-base font-medium text-gray-900 truncate">{{ plan.name }}</h3>
              </div>
              <div class="flex items-center">
                <span 
                  :class="[
                    'px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap',
                    getStatusStyle(plan.status)
                  ]"
                >
                  {{ getStatusText(plan.status) }}
                </span>
              </div>
            </div>
            
            <div class="space-y-2">
              <div class="flex items-center">
                <span class="text-sm text-gray-500 shrink-0 w-[4.5rem]">发布人：</span>
                <span class="text-sm text-gray-900 flex-1 min-w-0">{{ plan.createdBy }}</span>
              </div>
              <div class="flex items-center">
                <span class="text-sm text-gray-500 shrink-0 w-[4.5rem]">负责人：</span>
                <span class="text-sm text-gray-900 flex-1 min-w-0">{{ plan.userName }}</span>
              </div>
              <div class="flex items-start">
                <span class="text-sm text-gray-500 shrink-0 w-[4.5rem]">盘点范围：</span>
                <span class="text-sm text-gray-900 flex-1 min-w-0 whitespace-normal">{{ plan.auditRangeIsInventory == 1 ? '公物仓' : plan.auditRangeDepartmentNames }}</span>
              </div>
            </div>
          </div>
        </div>
      </List>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { myAuditplan } from '@/api/equipment'
import List from '@/components/List.vue'

const router = useRouter()
const route = useRoute()

const onClickLeft = () => {
  router.back()
}
const fetchData = async (page: number, pageSize: number) => {
  const params = {
    page,
    pageSize,
    orderBy: JSON.stringify(["-updated_at", "-created_at"])
  }
  let res = await myAuditplan(params)
  return {
    list: res.items,
    total: res.total
  };

}
const handlePlanDetail = (id: string) => {
  router.push({
    name: 'inventoryPlanDetail',
    query: {
      id
    }
  })
}

const getStatusStyle = (status: number) => {
  const styles = {
    0: 'bg-yellow-100 text-yellow-800',
    1: 'bg-blue-100 text-blue-800',
    2: 'bg-green-100 text-green-800',
    3: 'bg-red-100 text-red-800',
  }
  return styles[status as keyof typeof styles] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: number) => {
  const statusMap = {
    0: '未开始',
    1: '盘点中',
    2: '已完成',
    3: '已取消'
  }
  return statusMap[status as keyof typeof statusMap] || status
}
</script>

<style scoped>
.page-container {
  padding-bottom: env(safe-area-inset-bottom);
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}
</style> 