<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 定义功能项类型
interface AppItem {
  id: number;
  name: string;
  icon: string;
  color: string;
  badge?: number;
  link?: string;
}

// 应用列表数据
const appItems = ref([
  { id: 1, name: '预算申请', icon: '💰', color: 'text-yellow-500', link: '/budget-application' },
 // { id: 2, name: '购置申请', icon: '🛒', color: 'text-teal-500', link: '/cart' },
  { id: 3, name: '调拨申请', icon: '🔄', color: 'text-blue-500', link: '/transfer-application' },
 // { id: 4, name: '报修申请', icon: '🔧', color: 'text-orange-500', link: '/repair-registration' },
 // { id: 5, name: '报损申请', icon: '📉', color: 'text-red-500' },
  { id: 6, name: '报废申请', icon: '🗑️', color: 'text-gray-500', link: '/scrap-application' },
  { id: 8, name: '购物车', icon: '📦', color: 'text-green-500', link: '/cart?type=apply' }
])

// 搜索关键词
const searchKeyword = ref('')

// 点击应用项
const handleAppClick = (appId: number) => {
  const app = appItems.value.find(item => item.id === appId)
  if (!app) return
  
  if (app.link) {
    console.log('正在跳转到:', app.link)
    router.push(app.link)
  } else {
    console.log('无链接的应用:', app.name)
  }
}

// 返回上一页
const goBack = () => {
  router.push('/apps')
}
</script>

<template>
  <div class="page-container applications-page bg-gray-50">
    <!-- 顶部导航栏 -->
    <div class="bg-white text-gray-800 py-3 flex items-center justify-center relative border-b w-full">
      <button 
        @click="goBack" 
        class="absolute left-4 top-1/2 transform -translate-y-1/2"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-600">
          <path d="M19 12H5M12 19l-7-7 7-7"/>
        </svg>
      </button>
      <h1 class="text-lg font-medium">发起申请</h1>
    </div>
    
    <!-- 应用网格 -->
    <div class="bg-white rounded-lg mx-4 mt-4 shadow-sm">
      <div class="grid grid-cols-3">
        <div 
          v-for="item in appItems" 
          :key="item.id"
          class="flex flex-col items-center cursor-pointer py-4 border-r border-b"
          @click="handleAppClick(item.id)"
        >
          <div class="relative mb-2">
            <div class="w-10 h-10 flex items-center justify-center">
              <span :class="['text-2xl', item.color]">{{ item.icon }}</span>
            </div>
          </div>
          <span class="text-xs text-center text-gray-700 px-1 leading-tight max-w-full">{{ item.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
/* 全局样式 - 当前页面隐藏底部导航 */
.applications-page .bottom-nav {
  display: none !important;
}
</style>

<style scoped>
.page-container {
  min-height: 100vh;
  position: relative;
  z-index: 10;
  padding: 0;
}

.border-r:nth-child(3n) {
  border-right: none;
}
</style> 