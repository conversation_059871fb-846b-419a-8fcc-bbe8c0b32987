<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import List from '@/components/List.vue'
import { hpPreOrderRecord } from '@/api/equipment'
const router = useRouter()
const route = useRoute()

// 定义预购记录类型
interface PreorderRecord {
  id: number;
  department: string;
  itemName: string;
  itemType: string;
  quantity: number;
  expectedPrice: number;
  totalAmount: number;
  purpose: string;
  expectedDate: string;
  status: 'pending' | 'approved' | 'rejected';
  createTime: string;
  submitter: string;
}

// 预购记录列表数据
const records = ref<PreorderRecord[]>([
  {
    id: 1,
    department: '技术部',
    itemName: '笔记本电脑',
    itemType: '电子设备',
    quantity: 2,
    expectedPrice: 8000,
    totalAmount: 16000,
    purpose: '新员工办公使用',
    expectedDate: '2024-06-01',
    status: 'pending',
    createTime: '2024-03-20 14:30',
    submitter: '张小明'
  },
  {
    id: 2,
    department: '市场部',
    itemName: '办公椅',
    itemType: '家具用品',
    quantity: 5,
    expectedPrice: 800,
    totalAmount: 4000,
    purpose: '部门办公环境改善',
    expectedDate: '2024-05-15',
    status: 'approved',
    createTime: '2024-03-19 09:15',
    submitter: '李小红'
  },
  {
    id: 3,
    department: '财务部',
    itemName: '打印机',
    itemType: '办公设备',
    quantity: 1,
    expectedPrice: 3000,
    totalAmount: 3000,
    purpose: '部门文件打印使用',
    expectedDate: '2024-04-30',
    status: 'rejected',
    createTime: '2024-03-18 16:45',
    submitter: '王小华'
  },
  {
    id: 4,
    department: '人力资源部',
    itemName: '投影仪',
    itemType: '电子设备',
    quantity: 1,
    expectedPrice: 5000,
    totalAmount: 5000,
    purpose: '会议室使用',
    expectedDate: '2024-05-20',
    status: 'pending',
    createTime: '2024-03-21 10:30',
    submitter: '赵小龙'
  },
  {
    id: 5,
    department: '销售部',
    itemName: '文件柜',
    itemType: '家具用品',
    quantity: 3,
    expectedPrice: 1200,
    totalAmount: 3600,
    purpose: '资料存储使用',
    expectedDate: '2024-04-25',
    status: 'approved',
    createTime: '2024-03-20 16:20',
    submitter: '陈小云'
  },
  {
    id: 6,
    department: '技术部',
    itemName: '显示器',
    itemType: '电子设备',
    quantity: 4,
    expectedPrice: 2000,
    totalAmount: 8000,
    purpose: '开发人员双屏显示',
    expectedDate: '2024-05-10',
    status: 'pending',
    createTime: '2024-03-21 09:45',
    submitter: '林小峰'
  },
  {
    id: 7,
    department: '行政部',
    itemName: '碎纸机',
    itemType: '办公设备',
    quantity: 2,
    expectedPrice: 1500,
    totalAmount: 3000,
    purpose: '文件销毁使用',
    expectedDate: '2024-04-15',
    status: 'approved',
    createTime: '2024-03-19 14:25',
    submitter: '刘小婷'
  }
])

// 是否为选择模式
const isSelectMode = computed(() => route.query.mode === 'select')
const selectedRecords = ref<PreorderRecord[]>([])

// 根据模式过滤记录
const filteredRecords = computed(() => {
  if (isSelectMode.value) {
    // 选择模式下只显示已审批通过的记录
    return records.value.filter(record => record.status === 'approved')
  }
  // 正常模式显示所有记录
  return records.value
})

// 获取状态标签样式
const getStatusStyle = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'approved':
      return 'bg-green-100 text-green-800'
    case 'rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待审批',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

// 跳转到预购登记页面
const goToPreorderRegistration = () => {
  router.push('/preorder-registration')
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 查看记录详情
const viewRecordDetail = (record) => {
  console.log('编辑编辑');
    router.push({
      path: '/preorder-registration',
      query: {
        mode: 'edit',
        data: JSON.stringify(record)
      }
    })
}

// 选择记录
const toggleRecordSelection = (record: PreorderRecord) => {
  console.log('toggleRecordSelection', record);

  // 只允许选择已审批通过的记录
  // if (record.status !== 'approved') {
  //   ElMessage.warning('只能选择已审批通过的预购记录')
  //   return
  // }
  const index = selectedRecords.value.findIndex(item => item.id === record.id)
  if (index === -1) {
    selectedRecords.value.push(record)
  } else {
    selectedRecords.value.splice(index, 1)
  }
}

// 确认选择
const confirmSelection = () => {
  if (selectedRecords.value.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    return
  }

  // 将选中的记录保存到 sessionStorage
  sessionStorage.setItem('selectedPreorderItems', JSON.stringify(selectedRecords.value))

  // 获取重定向路径
  const redirect = route.query.redirect as string

  // 跳转回预算申请页面
  if (redirect) {
    router.push(redirect)
  } else {
    router.back()
  }
}
// const fetchData = async (page) => {
//   let res = await hpPreOrderRecord()
//   return {
//     list: filteredRecords.value,
//     total: filteredRecords.value.length
//   }
// }
const fetchData = async (page: number, pageSize: number) => {
  const params = {
    page,
    pageSize,
    orderBy: JSON.stringify(["-updated_at", "-created_at"])
  }
  let res = await hpPreOrderRecord()
  console.log('resssssssssss', res);
  return {
    // list: filteredProducts.value,
    // total: filteredProducts.value.length
    list: res.items,
    total: res.total
  }
}
</script>

<template>
  <div class="page-container min-h-screen bg-gray-50 no-scrollbar">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-bold">{{ isSelectMode ? '选择预购记录' : '预购登记' }}</h1>
        <button
          v-if="!isSelectMode"
          @click="goToPreorderRegistration"
          class="w-6 h-6 rounded-full bg-primary flex items-center justify-center"
        >
          <i class="fas fa-plus text-white"></i>
        </button>
        <div v-else class="w-6"></div>
      </div>
    </div>

    <!-- 记录列表 -->
    <div class="pt-14 px-2 pb-20 h-[calc(100vh-56px)] overflow-y-auto no-scrollbar">
      <List :request-fn="fetchData" v-slot="{ list }">
        <div class="space-y-3">
          <div
            v-for="record in list"
            :key="record.id"
            class="bg-white rounded-lg p-4 shadow-sm"
            @click="isSelectMode ? toggleRecordSelection(record) : viewRecordDetail(record)"
          >
            <div class="flex items-start space-x-3">
              <!-- <input
                v-if="isSelectMode"
                type="checkbox"
                :checked="selectedRecords.some(item => item.id === record.id)"
                :disabled="record.status !== 'approved'"
                class="mt-1"
                @click.stop
              > -->
              <van-checkbox v-if="isSelectMode" shape="square" :checked="selectedRecords.some(item => item.id === record.id)" @change="toggleRecordSelection(record)" />
              <div class="flex-1">
                <div class="flex items-start justify-between mb-2">
                  <div class="flex-1">
                    <h3 class="text-base font-medium text-gray-900">{{ record.itemName }}</h3>
                  </div>
                </div>

                <div class="grid grid-cols-2 gap-2 text-sm text-gray-500">
                  <div>
                    <span class="mr-2">资产名称：</span>
                    <span class="text-gray-900">{{ record.name }}</span>
                  </div>
                  <div>
                    <span class="mr-2">资产类型：</span>
                    <span class="text-gray-900">{{ record.typeName }}</span>
                  </div>
                  <div>
                    <span class="mr-2">预购数量：</span>
                    <span class="text-gray-900">{{ record.quantity }}件</span>
                  </div>
                  <div>
                    <span class="mr-2">预计单价：</span>
                    <span class="text-gray-900">¥{{ record.unitPrice }}</span>
                  </div>
                  <div>
                    <span class="mr-2">总金额：</span>
                    <span class="text-gray-900">¥{{ record.totalPrice }}</span>
                  </div>
                  <div class="col-span-2">
                    <span class="mr-2">预计使用日期：</span>
                    <span class="text-gray-900">{{ record.useTime }}</span>
                  </div>
                  <div class="col-span-2">
                    <span class="mr-2">用途说明：</span>
                    <span class="text-gray-900">{{ record.purpose }}</span>
                  </div>
                </div>

                <div class="text-xs text-gray-400 mt-3">
                  提交时间：{{ record.createdAt }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </List>
    </div>

    <!-- 底部确认按钮 -->
    <div v-if="isSelectMode" class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm text-gray-500">已选择 {{ selectedRecords.length }} 项</span>
      </div>
      <button
        @click="confirmSelection"
        class="w-full py-2 px-4 bg-primary text-white rounded-lg disabled:opacity-50"
        :disabled="selectedRecords.length === 0"
      >
        确认选择
      </button>
    </div>
  </div>
</template>

<style>
/* .page-container {
  @apply h-screen overflow-hidden;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
} */

::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

* {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

html,
body,
.page-container,
.page-container * {
  -ms-overflow-style: none !important;  /* IE and Edge */
  scrollbar-width: none !important;  /* Firefox */
}

.bg-primary {
  background-color: #1890ff;
}

.text-primary {
  color: #1890ff;
}

/* 隐藏滚动条样式 */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
</style>
