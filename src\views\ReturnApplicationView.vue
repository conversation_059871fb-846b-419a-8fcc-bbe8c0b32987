<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-medium">资产退还申请</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <!-- 申请表单 -->
    <div class="form-content">
      <form class="space-y-6">
                <!-- 退还物品 -->
                <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <div class="flex justify-between items-center mb-4">
              <label class="block text-sm font-medium text-gray-700">
                <span class="text-red-500">*</span> 退还明细
              </label>
              <button
                type="button"
                @click="selectAssets"
                class="inline-flex items-center px-3 py-1.5 border border-blue-600 text-sm rounded-md text-blue-600 bg-white hover:bg-blue-50 focus:outline-none"
              >
                <i class="fas fa-plus mr-1.5"></i> 选择资产
              </button>
            </div>
            
            <div v-if="formData.details.length === 0" class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg">
              <i class="fas fa-inbox text-3xl mb-2"></i>
              <p>暂无退还物品</p>
            </div>
            
            <div v-else class="space-y-3">
              <div 
                v-for="item in formData.details" 
                :key="item.id" 
                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div class="flex-1 min-w-0">
                  <div class="text-sm font-medium text-gray-900 mb-1">{{ item.name }}</div>
                  <div class="text-xs text-gray-500 mb-1">资产编号：{{ item.no }}</div>
                  <div class="text-xs text-gray-500">型号规格：{{ item.model }}</div>
                </div>
                <button 
                  type="button" 
                  @click="removeReturnItem(item)"
                  class="ml-2 text-red-500 hover:text-red-700"
                >
                  <i class="fas fa-trash-alt"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        <!-- 事由 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <span class="text-red-500">*</span> 退还说明
            </label>
            <textarea
              v-model="formData.reason"
              rows="4"
              class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              placeholder="请填写退还说明"
            ></textarea>
          </div>
        </div>

        <!-- 退还状态 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <span class="text-red-500">*</span> 资产状态
            </label>
            <div class="grid grid-cols-3 gap-2">
              <button 
                type="button"
                @click="formData.material_status = 'normal'"
                :class="[
                  'py-2 rounded-md text-center border',
                  formData.material_status === 'normal'
                    ? 'border-blue-600 bg-blue-50 text-blue-600'
                    : 'border-gray-300 bg-white text-gray-700'
                ]"
              >
                <i class="fas fa-check-circle mr-1.5"></i> 完好
              </button>
              <button 
                type="button"
                @click="formData.material_status = 'damaged'"
                :class="[
                  'py-2 rounded-md text-center border',
                  formData.material_status === 'damaged'
                    ? 'border-blue-600 bg-blue-50 text-blue-600'
                    : 'border-gray-300 bg-white text-gray-700'
                ]"
              >
                <i class="fas fa-tools mr-1.5"></i> 损坏
              </button>
              <button 
                type="button"
                @click="formData.material_status = 'lost'"
                :class="[
                  'py-2 rounded-md text-center border',
                  formData.material_status === 'lost'
                    ? 'border-blue-600 bg-blue-50 text-blue-600'
                    : 'border-gray-300 bg-white text-gray-700'
                ]"
              >
                <i class="fas fa-times-circle mr-1.5"></i> 丢失
              </button>
            </div>
          </div>
        </div>

        <!-- 备注 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              备注
            </label>
            <textarea
              v-model="formData.remark"
              rows="3"
              class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入备注信息"
            ></textarea>
          </div>
        </div>

        <!-- 附件上传 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              附件
            </label>
            <NewFileUpload v-model="formData.files" />
          </div>
        </div>

      </form>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
      <button 
        type="button"
        @click="submitForm"
        class="w-full px-4 py-3 border border-transparent rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        :disabled="isSubmitting"
      >
        {{ isSubmitting ? '提交中...' : '下一步' }}
      </button>
    </div>

    <!-- 成功提示 -->
    <div v-if="showSuccessModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
      <div class="bg-white rounded-lg shadow-lg p-6 w-80 text-center">
        <div class="w-20 h-20 mx-auto bg-green-100 rounded-full flex items-center justify-center">
          <i class="fas fa-check text-3xl text-green-600"></i>
        </div>
        <h3 class="mt-4 text-xl font-medium text-gray-900">提交成功</h3>
        <p class="mt-2 text-gray-500">退还申请已提交，等待审批</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import axios from 'axios'
import type { AxiosProgressEvent } from 'axios'
import * as ww from '@wecom/jssdk'
import { hpReturnOrder } from '@/api/approval'
import NewFileUpload from '@/components/newFileUpload.vue'

const router = useRouter()
const route = useRoute()

// 表单数据
interface ReturnItem {
  id: number;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
  status: number;
  no: string;
  name: string;
  type?: string;
  brand?: string;
  model?: string;
  unitPrice?: number;
  quantity?: number;
  unit?: string;
  totalPrice?: number;
  isFromCheckAcceptOrder?: number;
  departmentId?: number;
  materialAlias?: string;
  images?: string;
}

interface Attachment {
  name: string;
  url: string;
  size: number;
  type: string;
  progress?: number;
  status: 'uploading' | 'success' | 'error';
  errorMessage?: string;
}

const formData = ref({
  details: [] as ReturnItem[], // 退还物品列表
  reason: '', // 事由
  remark: '', // 备注
  material_status: 'normal', // 资产状态：normal(完好)、damaged(损坏)、lost(丢失)
  files: []
})

// 提示框状态
const showSuccessModal = ref(false)
const isSubmitting = ref(false)

// 返回上一页
const goBack = () => {
  router.push('/apps')
}

// 移除退还物品
const removeReturnItem = (item: ReturnItem) => {
  const index = formData.value.details.findIndex(i => i.id === item.id);
  if (index !== -1) {
    formData.value.details.splice(index, 1);
  }
}

// 选择资产
const selectAssets = () => {
  // 跳转到资产选择页面
  localStorage.setItem('returnData', JSON.stringify(formData.value))
  router.push({
    path: '/asset-selection',
    query: { 
      redirect: '/return-application',
      multiple: 'true'
    }
  })
}

// 提交表单
const submitForm = async () => {
  // 表单验证
  if (!formData.value.reason) {
    alert('请填写退还说明')
    return
  }
  if (formData.value.details.length === 0) {
    alert('请选择要退还的物品')
    return
  }
  
  try {
    isSubmitting.value = true
    
    // TODO: 这里添加提交到后端的逻辑
    console.log('提交的表单数据：', formData.value)
    const startTime = Date.now()
    const res = await hpReturnOrder({ ...formData.value, files: formData.value.files && formData.value.files.length ? formData.value.files.join(',') : '' })
    // 计算已经过去的时间
    const elapsedTime = Date.now() - startTime
    // 如果耗时小于200ms，则等待剩余时间
    if (elapsedTime < 200) {
      await new Promise(resolve => setTimeout(resolve, 200 - elapsedTime))
    }

    // 显示成功提示
    // showSuccessModal.value = true
    // 开始时间
    console.log( Date.now(), '开始')
    const url = import.meta.env.VITE_URL
    const link = `${url}#/return-application-detail?id=${res.id}`
    let templateId = import.meta.env.VITE_ENV === 'prod' ? '46ea36c2645136beaf2e6257c03b6997_111604115' : 'b0ad48c36d0b88cea06dac4b9e6fae75_1315952666'
    ww.thirdPartyOpenPage({
      oaType: ww.OAType.create_approval,
      templateId,
      thirdNo: 'THSQ-' + res.id,
      extData: {
        fieldList: [
          {
            title: '申请类型',
            type: ww.OaExtDataType.text,
            value: '退还申请',
          },
          {
              'title': '申请时间',
              'type': ww.OaExtDataType.text,
              'value': res.createdAt,
          },
          {
            'title': '退还原因',
            'type': ww.OaExtDataType.text,
            'value': formData.value.reason,
          },
          {
            type: ww.OaExtDataType.link,
            title: '详细内容',
            value: link
          }
        ]
      },
      success: () => {
        console.log( Date.now(), '提交成功')
        // router.push('/apps')
      },
      fail: () => {
        console.log( Date.now(), '提交失败')
      },
      complete: () => {
        console.log( Date.now(), '提交完成')
      }
    })
  } catch (error) {
    console.error('提交失败：', error)
    alert('提交失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

// 处理文件上传
const handleFileUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (!input.files?.length) return;

  const files = Array.from(input.files);
  
  for (const file of files) {
    // 检查文件大小
    if (file.size > FILE_SIZE_LIMIT) {
      alert(`文件 ${file.name} 超过大小限制 10MB`);
      continue;
    }

    // 检查文件类型
    if (!Object.keys(ALLOWED_FILE_TYPES).includes(file.type)) {
      alert(`文件 ${file.name} 格式不支持`);
      continue;
    }

    // 创建附件对象
    const attachment: Attachment = {
      name: file.name,
      url: URL.createObjectURL(file),
      size: file.size,
      type: file.type,
      status: 'uploading',
      progress: 0
    };

    // 添加到附件列表
    formData.value.files.push(attachment);

    try {
      const formDataToUpload = new FormData();
      formDataToUpload.append('file', file);

      // 使用 axios 替代 fetch 以获得更好的上传进度支持
      const response = await axios.post('/api/upload', formDataToUpload, {
        onUploadProgress: (progressEvent: AxiosProgressEvent) => {
          const index = formData.value.files.findIndex(item => item.name === file.name);
          if (index !== -1 && progressEvent.total) {
            formData.value.files[index].progress = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
          }
        }
      });

      // 更新附件状态
      const index = formData.value.files.findIndex(item => item.name === file.name);
      if (index !== -1) {
        formData.value.files[index].status = 'success';
        formData.value.files[index].url = response.data.url || attachment.url;
      }
    } catch (error) {
      console.error('文件上传失败:', error);
      
      // 更新附件状态为错误
      const index = formData.value.files.findIndex(item => item.name === file.name);
      if (index !== -1) {
        formData.value.files[index].status = 'error';
        formData.value.files[index].errorMessage = '上传失败';
      }
    }
  }

  // 清空文件输入框，以便下次上传相同文件
  input.value = '';
}

// 移除附件
const removeAttachment = (index: number) => {
  formData.value.files.splice(index, 1);
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  }
}

// 在组件挂载时初始化
onMounted(() => {
  console.log('组件挂载，开始初始化数据')
  if (localStorage.getItem('returnData')) {
    let returnData = JSON.parse(localStorage.getItem('returnData') as string)
    formData.value.reason = returnData.reason
    formData.value.remark = returnData.remark
    formData.value.material_status = returnData.material_status
    formData.value.files = returnData.files
    localStorage.removeItem('returnData')
  }
  // 如果从选择资产页面返回，则获取选中的资产
  const selectedAssets = localStorage.getItem('selectedAssets')
  if (selectedAssets) {
    try {
      formData.value.details = JSON.parse(selectedAssets)
      // 清除本地存储
      localStorage.removeItem('selectedAssets')
    } catch (error) {
      console.error('解析选择资产数据失败:', error)
    }
  }
})
</script>

<style scoped>
.page-container {
  padding-top: 56px;
  padding-bottom: 80px;
  background-color: #f7f8fa;
  min-height: 100vh;
}

.form-content {
  padding: 1rem 0.5rem;
}
</style> 