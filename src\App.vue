<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { RouterLink, RouterView, useRoute, useRouter} from 'vue-router'
import { getUserInfo, setUserInfo } from "@/utils/userInfo";
import { getWxUrl, wxLogin } from '@/api/login'
import { isMobile } from "@/utils/tools";
import * as ww from '@wecom/jssdk'
import { getRegisterInfo, getJsSDKConfigSignature, getJsSDKAgentSignature } from '@/api/workwx'
const route = useRoute()
const router = useRouter()

// 计算当前路由
const currentRouteName = computed(() => route.name)
const isShowBottomNav = computed(() => !route.meta.hideBottomNav && !['applications', 'taskDetail', 'productDetail', 'settings', 'changePassword', 'cart', 'preorderRegistration', 'orderList'].includes(currentRouteName.value as string))
const geturl = (name: string) => {
  return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ''])[1].replace(/\+/g, '%20')) || null
}
const code = geturl('code')
const isBand = () => {
  if (import.meta.env.VITE_ENV === 'dev') {
    router.push('/')
    return
  }
  if (!getUserInfo()) {
    if (code) {
      wxLogin({ code, platform: isMobile ? 'workwx_mobile' : 'workwx_pc' }).then(res => {
        console.log('登录成功=======', res);
        setUserInfo(res)
        router.push('/')
      }).catch(err => {
        const error = err.response.data
        console.log('未绑定', err, error.metadata.bindCode)
        router.push('/login?bindCode=' + error.metadata.bindCode)
      })
    } else {
      getWxUrl({ redirect_uri: window.location.href}).then(res => {
        console.log('登录链接=======', res);
        window.location.href = res.url
      })
    }
  }
}
isBand()

onMounted(async () => {
  console.log('import.meta.env.VITE_ENV', import.meta.env);
  const res = await getRegisterInfo()
  ww.register({
      corpId: res.corpId,
      agentId: res.agentId,
      jsApiList: [
        'thirdPartyOpenPage',
      ],
      async getConfigSignature(url: string): Promise<ww.SignatureData> {
        const res = await getJsSDKConfigSignature({ url: url })
        return {
          timestamp: res.timestamp,
          nonceStr: res.nonceStr,
          signature: res.signature
        }
      },
      async getAgentConfigSignature(url: string): Promise<ww.SignatureData> {
        const res = await getJsSDKAgentSignature({ url: url })
        const obj = {
          timestamp: res.timestamp,
          nonceStr: res.nonceStr,
          signature: res.signature
        }
        return obj
      },
      onConfigSuccess: (res) => {
        console.log('onConfigSuccess', res)
      },
      onConfigFail: (res) => {
        console.error('onConfigFail', res)
      },
      onConfigComplete: (res) => {
        console.log('onConfigComplete', res)
      },
      onAgentConfigSuccess: (res) => {
        console.log('onAgentConfigSuccess', res)
      },
      onAgentConfigFail: (res) => {
        console.error('onAgentConfigFail', res)
      },
      onAgentConfigComplete: (res) => {
        console.log('onAgentConfigComplete', res)
      }
    })
})
</script>

<template>
  <div class="app-container" :style="{ paddingBottom: isShowBottomNav ? '60px' : '0px' }">
    <!-- 主内容区域 -->
    <RouterView v-slot="{ Component }">
      <component :is="Component" />
    </RouterView>

    <!-- 底部导航 -->
    <div
      v-if="isShowBottomNav"
      class="bottom-nav"
    >
      <RouterLink
        v-for="(item) in [
          { name: 'tasks', path: '/', icon: 'fa-tasks', label: '任务' },
          { name: 'mall', path: '/mall', icon: 'fa-shopping-cart', label: '公物仓' },
          { name: 'apps', path: '/apps', icon: 'fa-th-large', label: '应用' },
          { name: 'profile', path: '/profile', icon: 'fa-user', label: '我的' }
        ]"
        :key="item.name"
        :to="item.path"
        class="bottom-nav-item"
        :class="{
          'active-nav-item': currentRouteName === item.name,
          'inactive-nav-item': currentRouteName !== item.name
        }"
      >
        <i :class="['fas', item.icon, 'bottom-nav-icon']"></i>
        <span>{{ item.label }}</span>
      </RouterLink>
    </div>
  </div>
</template>

<style>
/* 全局样式在main.css中定义 */
body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  height: 100%;
}
/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 0px;
}
/*滚动槽*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.15);
}
/* 移除手机壳样式，使用更简洁的布局 */
.app-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  min-height: 100vh;
  background-color: #f9fafb;
  padding-bottom: 60px; /* 为底部导航留出空间 */
  box-sizing: border-box;
}

.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: white;
  padding: 8px 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.bottom-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #666;
  font-size: 12px;
}

.bottom-nav-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.active-nav-item {
  color: #409EFF;
}

.inactive-nav-item {
  color: #666;
}
</style>
