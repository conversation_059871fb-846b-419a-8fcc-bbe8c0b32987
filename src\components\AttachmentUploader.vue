<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { upload } from '@/api/equipment'
import { showToast } from 'vant'

export interface Attachment {
  name: string
  url: string
  size: number
  type: string
  progress?: number
  status: 'uploading' | 'success' | 'error'
  errorMessage?: string
}

// Props
const props = defineProps<{
  modelValue: Attachment[]
  maxSize?: number
  accept?: string
  maxCount?: number
  title?: string
  typeText?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: Attachment[]): void
  (e: 'upload-success', file: Attachment): void
  (e: 'remove', file: Attachment): void
}>()

// 生成唯一ID
const uniqueId = computed(() => `file-upload-${Math.random().toString(36).substr(2, 9)}`)

const FILE_SIZE_LIMIT = computed(() => props.maxSize ?? 10 * 1024 * 1024)
const ACCEPT = computed(() => props.accept ?? '.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png')
const MAX_COUNT = computed(() => props.maxCount ?? Infinity)
const TITLE = computed(() => props.title ?? '附件')
const TYPE_TEXT = computed(() => props.typeText ?? '上传附件')

const attachments = ref<Attachment[]>([...props.modelValue])

watch(
  () => props.modelValue,
  (val) => {
    if (val !== attachments.value) {
      attachments.value = [...val]
    }
  }
)

const handleFileUpload = async (event: Event) => {
  
  const input = event.target as HTMLInputElement
  if (!input.files?.length) return

  const files = Array.from(input.files)
  for (const file of files) {
    if (attachments.value.length >= MAX_COUNT.value) {
      showToast('已达最大上传数量')
      break
    }
    if (file.size > FILE_SIZE_LIMIT.value) {
      showToast(`文件 ${file.name} 超过大小限制`)
      continue
    }
    if (!ACCEPT.value.split(',').some(type => file.name.toLowerCase().endsWith(type.trim()))) {
      showToast(`文件 ${file.name} 格式不支持`)
      continue
    }
    console.log('file------', file, URL.createObjectURL(file));
    const attachment = {
      url: '',
      status: 'uploading'
    }
    attachments.value.push(attachment)
    // emit('update:modelValue', attachments.value)

    const currentIndex = attachments.value.length - 1
    try {
      const formDataToUpload = new FormData()
      formDataToUpload.append('file', file)
      const res = await upload(formDataToUpload)
      if (res && res.value) {
        attachments.value[currentIndex].status = 'success'
        attachments.value[currentIndex].url = res.value
        emit('upload-success', attachments.value[currentIndex])
      } else {
        throw new Error(res?.message || '上传失败')
      }
    } catch (error: any) {
      attachments.value[currentIndex].status = 'error'
      attachments.value[currentIndex].errorMessage = error?.message || '上传失败'
      showToast('文件上传失败')
    }
    emit('update:modelValue', attachments.value)
  }
  input.value = ''
}

const previewAttachment = (attachment: Attachment) => {
  window.open(attachment.url, '_blank')
}

const removeAttachment = (index: number) => {
  const file = attachments.value[index]
  URL.revokeObjectURL(file.url)
  attachments.value.splice(index, 1)
  emit('update:modelValue', attachments.value)
  emit('remove', file)
}
</script>

<template>
  <div class="form-group">
    <div class="flex items-center space-x-3">
      <label class="label shrink-0">{{ TITLE }}</label>
      <div class="relative shrink-0">
        <input
          type="file"
          multiple
          class="hidden"
          @change="handleFileUpload"
          :id="uniqueId"
          :accept="ACCEPT"
          :disabled="attachments.length >= MAX_COUNT"
        >
        <label
          :for="uniqueId"
          class="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 bg-white hover:bg-gray-50"
        >
          <i class="fas fa-upload mr-2"></i>
          {{ TYPE_TEXT }}
        </label>
      </div>
    </div>
    <div class="text-xs text-gray-500 mt-1">
      支持以下格式：{{ ACCEPT.split(',').map(ext => ext.replace('.', '').toUpperCase()).join('、') }}，单个文件不超过{{ (FILE_SIZE_LIMIT / 1024 / 1024).toFixed(0) }}MB
    </div>
    <div v-if="attachments.length > 0" class="space-y-2 mt-3">
      <div
        v-for="(file, index) in attachments"
        :key="file"
        class="relative p-3 bg-gray-50 rounded-lg"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2 flex-1 min-w-0">
            <i 
              :class="[
                'fas',
                TYPE_TEXT.includes('图片') ? 'fa-camera' :
                'fa-upload'
              ]"
              class="text-gray-400"
            ></i>
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-2">
                <div class="text-sm text-gray-700 truncate">{{ file.url.split('/').pop() }}</div>
                <span 
                  :class="{
                    'text-xs px-2 py-0.5 rounded': true,
                    'bg-green-100 text-green-800': file.status === 'success',
                    'bg-red-100 text-red-800': file.status === 'error',
                    'bg-blue-100 text-blue-800': file.status === 'uploading'
                  }"
                >
                  {{
                    file.status === 'success' ? '已上传' :
                    file.status === 'error' ? '上传失败' :
                    '上传中'
                  }}
                </span>
              </div>
              <div class="text-xs text-gray-500">{{ (file.size / 1024).toFixed(1) }}KB</div>
              <div v-if="file.status === 'error'" class="text-xs text-red-500 mt-1">
                {{ file.errorMessage }}
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <button
              v-if="file.status === 'success'"
              @click="previewAttachment(file)"
              class="text-blue-600 hover:text-blue-800"
              title="预览"
            >
              <i class="fas fa-eye"></i>
            </button>
            <button
              @click="removeAttachment(index)"
              class="text-gray-400 hover:text-red-500"
              title="删除"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
        <!-- <div v-if="file.status === 'uploading'" class="mt-2">
          <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
            <div
              class="h-full bg-blue-500 transition-all duration-300"
              :style="{ width: `${file.progress ?? 0}%` }"
            ></div>
          </div>
          <div class="text-xs text-gray-500 mt-1">
            {{ file.progress ?? 0 }}%
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template> 
<style scoped>
.form-group {
  @apply space-y-1;
}
</style>