import { defHttp } from "../axios";

export const getLearningTasksData = (params?: any) => defHttp.get(
  {
    url: '/wms/v1/myWmsLearningPlan',
    params,
    headers: {
      ignoreCancelToken: true,

    },
  }
);

export const getCourseTasksData = (learningPlanId: string) => defHttp.get(
  {
    url: `/wms/v1/myLearningCourse?learningPlanId=${learningPlanId}`,
    headers: {
      ignoreCancelToken: true,

    },
  }
);

export const getCoursewaresTasksData = (learningCourseId: string, learningPlanId: string) => defHttp.get(
  {
    url: `/wms/v1/myLearningCourseware?learningCourseId=${learningCourseId}&learningPlanId=${learningPlanId}`,
    headers: {
      ignoreCancelToken: true,

    },
  }
);

export const getAllCoursewaresTasksData = (courseId: string) => defHttp.get(
  {
    url: `/wms/v1/wmsLearningCourseCourseware?noPaging=true`,
    params: {
      query: JSON.stringify([
        {
          courseId
        }
      ])
    },
    headers: {
      ignoreCancelToken: true,

    },
  }
);

export const getAllCourseTasksData = (params: any) => defHttp.get(
  {
    url: '/wms/v1/wmsLearningCourse?nopaging=true',
    params,
    headers: {
      ignoreCancelToken: true,
    },
  }
);

export const getCoursewareTasksData = (learningCourseId: string) => defHttp.get(
  {
    url: `/wms/v1/wmsLearningCourseware/${learningCourseId}`,
    headers: {
      ignoreCancelToken: true,

    },
  }
);
