<template>
  <div class="approval-conclusion">
    <el-form :model="form" label-width="70px" class="approval-form">
      <el-form-item label="审批状态" class="status-item">
        <el-tag :type="getStatusType(type)" class="status-tag">
          {{ getStatusText(type) }}
        </el-tag>
      </el-form-item>
      
      <el-form-item label="审批意见" class="comment-item">
        <el-input
          v-model="form.comment"
          type="textarea"
          :rows="3"
          :autosize="{ minRows: 3, maxRows: 3 }"
          :placeholder="type === 'approve' ? '请输入审批意见（选填）' : '请输入驳回原因'"
          resize="none"
        />
      </el-form-item>
      
      <el-form-item label="签名" class="signature-item">
        <div class="signature-container">
          <div class="signature-box">
            <canvas ref="signaturePadCanvas" class="signature-pad"></canvas>
          </div>
          <div class="signature-actions">
            <el-button size="small" plain @click="clearSignature">
              <i class="fas fa-redo-alt mr-1"></i>
              重新签名
            </el-button>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <div class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleConfirm"
        :disabled="!isValid"
      >
        确认提交
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import SignaturePad from 'signature_pad'

interface Props {
  type: 'approve' | 'reject'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'approve'
})

const emit = defineEmits<{
  (e: 'confirm', form: { comment: string; signature: string }): void
  (e: 'cancel'): void
}>()

const signaturePadCanvas = ref<HTMLCanvasElement | null>(null)
const signaturePad = ref<SignaturePad | null>(null)
const hasSignature = ref(false)

const form = ref({
  comment: '',
  signature: ''
})

// 初始化签名板
const initSignaturePad = () => {
  if (signaturePadCanvas.value) {
    const canvas = signaturePadCanvas.value
    const container = canvas.parentElement
    
    // 获取容器的实际尺寸
    const { width } = container?.getBoundingClientRect() || { width: 0 }
    
    // 设置canvas的尺寸，考虑设备像素比
    const scale = window.devicePixelRatio || 1
    canvas.width = width * scale
    canvas.height = 160 * scale
    
    // 设置canvas的样式尺寸
    canvas.style.width = `${width}px`
    canvas.style.height = '160px'
    
    // 调整canvas的缩放以匹配设备像素比
    const ctx = canvas.getContext('2d')
    if (ctx) {
      ctx.scale(scale, scale)
    }

    // 如果已经存在签名板实例，先销毁
    if (signaturePad.value) {
      signaturePad.value.off()
    }

    // 创建新的签名板实例
    signaturePad.value = new SignaturePad(canvas, {
      backgroundColor: 'rgb(255, 255, 255)',
      penColor: 'rgb(0, 0, 0)',
      minWidth: 0.5,
      maxWidth: 2,
      throttle: 16,
      velocityFilterWeight: 0.2
    })

    // 监听签名变化
    signaturePad.value.addEventListener('endStroke', () => {
      if (signaturePad.value && !signaturePad.value.isEmpty()) {
        hasSignature.value = true
        form.value.signature = signaturePad.value.toDataURL()
      }
    })

    // 阻止默认的触摸行为
    const preventDefaultTouch = (e: TouchEvent) => {
      e.preventDefault()
    }

    // 移除旧的事件监听器
    canvas.removeEventListener('touchstart', preventDefaultTouch)
    canvas.removeEventListener('touchmove', preventDefaultTouch)
    canvas.removeEventListener('touchend', preventDefaultTouch)

    // 添加新的触摸事件监听器
    canvas.addEventListener('touchstart', preventDefaultTouch, { passive: false })
    canvas.addEventListener('touchmove', preventDefaultTouch, { passive: false })
    canvas.addEventListener('touchend', preventDefaultTouch, { passive: false })

    // 设置canvas样式
    canvas.style.touchAction = 'none'
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 延迟初始化以确保DOM已完全渲染
  setTimeout(() => {
    initSignaturePad()
  }, 100)
  
  // 监听窗口大小变化
  window.addEventListener('resize', resizeCanvas)
})

// 处理canvas尺寸调整
const resizeCanvas = () => {
  if (signaturePadCanvas.value && signaturePad.value) {
    const canvas = signaturePadCanvas.value
    const container = canvas.parentElement
    const { width } = container?.getBoundingClientRect() || { width: 0 }
    const scale = window.devicePixelRatio || 1
    
    // 保存当前的签名数据
    const data = signaturePad.value.toData()
    
    // 重新设置canvas尺寸
    canvas.width = width * scale
    canvas.height = 160 * scale
    canvas.style.width = `${width}px`
    canvas.style.height = '160px'
    
    // 调整canvas的缩放
    const ctx = canvas.getContext('2d')
    ctx?.scale(scale, scale)
    
    // 恢复签名数据
    signaturePad.value.fromData(data)
  }
}

// 组件卸载时清理事件监听和实例
onUnmounted(() => {
  window.removeEventListener('resize', resizeCanvas)
  if (signaturePad.value) {
    signaturePad.value.off()
  }
})

// 重置表单
const resetForm = () => {
  form.value.comment = ''
  form.value.signature = ''
  hasSignature.value = false
  if (signaturePad.value) {
    signaturePad.value.clear()
  }
  // 重新初始化签名板
  initSignaturePad()
}

const getStatusType = (status: 'approve' | 'reject') => {
  const statusMap = {
    approve: 'success',
    reject: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: 'approve' | 'reject') => {
  const statusMap = {
    approve: '通过',
    reject: '驳回'
  }
  return statusMap[status] || '未知状态'
}

// 表单验证
const isValid = computed(() => {
  if (!hasSignature.value) {
    return false
  }
  
  if (props.type === 'reject') {
    return form.value.comment.trim().length > 0
  }
  
  return true
})

// 处理确认
const handleConfirm = () => {
  if (!signaturePad.value || signaturePad.value.isEmpty()) {
    ElMessage.warning('请先签名')
    return
  }
  
  if (props.type === 'reject' && !form.value.comment.trim()) {
    ElMessage.warning('请输入驳回原因')
    return
  }

  // 获取最新的签名数据
  form.value.signature = signaturePad.value.toDataURL()
  emit('confirm', form.value)
  // 提交后重置表单
  resetForm()
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  // 取消后重置表单
  resetForm()
}

// 清除签名
const clearSignature = () => {
  if (!signaturePad.value) return
  signaturePad.value.clear()
  hasSignature.value = false
  form.value.signature = ''
}
</script>

<style scoped>
.approval-conclusion {
  padding: 16px 20px;
  background-color: #fff;
}

.approval-form {
  .status-item {
    margin-bottom: 24px;
  }

  .comment-item {
    margin-bottom: 24px;
  }

  .signature-item {
    margin-bottom: 0;
  }
}

.status-tag {
  font-size: 14px;
  padding: 6px 12px;
}

.signature-container {
  border: none;
  padding: 0;
  background: none;
  width: 100%;
}

.signature-box {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
  width: 100%;
  touch-action: none; /* 防止触摸事件引起页面滚动 */
}

.signature-pad {
  width: 100%;
  height: 160px;
  background-color: #fff;
  cursor: crosshair;
  display: block;
  touch-action: none; /* 防止触摸事件引起页面滚动 */
}

.signature-actions {
  display: flex;
  justify-content: flex-start;
  margin-top: 4px;
  margin-left: 2px;

  .el-button {
    display: inline-flex;
    align-items: center;
    font-size: 13px;
    height: 28px;
    padding: 0 12px;
    color: #606266;
    border: none;
    
    i {
      margin-right: 4px;
      font-size: 12px;
    }

    &:hover {
      background: none;
      color: #409EFF;
    }
  }
}

.dialog-footer {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
  
  .el-button + .el-button {
    margin-left: 12px;
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-textarea__inner) {
  resize: none;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  
  &::placeholder {
    color: #9ca3af;
  }
}
</style> 