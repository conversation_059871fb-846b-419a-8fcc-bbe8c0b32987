<template>
  <div class="page-container bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="handleBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-bold">验收登记</h1>
        <button 
          @click="handleAdd"
          class="w-6 h-6 rounded-full bg-primary text-white flex items-center justify-center"
        >
          <i class="fas fa-plus text-sm"></i>
        </button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area pt-16 px-2">
      <!-- 验收记录列表 -->
      <List :request-fn="fetchData" v-slot="{ list }">
        <div class="space-y-4">
          <div v-for="record in list" :key="record.id" 
            class="bg-white rounded-lg p-4 shadow-sm" 
            @click="handleEdit(record)">
            <div class="flex justify-between items-start mb-3">
              <div>
                <h3 class="text-base font-medium text-gray-900">{{ record.name }}</h3>
              </div>
              <div>
                <el-tag :type="getStatusType(record.status)" size="small">
                  {{ record.status == 1 ? '验收完成' : '待验收' }}
                </el-tag>
              </div>
            </div>
            <div class="space-y-2">
              <div class="flex text-sm">
                <span class="text-gray-500 w-20">创建人：</span>
                <span class="text-gray-900">{{ record.createdBy }}</span>
              </div>
              <!-- <div class="flex text-sm">
                <span class="text-gray-500 w-20">部门：</span>
                <span class="text-gray-900">{{ record.departmentName }}</span>
              </div> -->
              <div class="flex text-sm">
                <span class="text-gray-500 w-20">创建时间：</span>
                <span class="text-gray-900">{{ record.createdAt }}</span>
              </div>
            </div>
          </div>
        </div>
      </List>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { checkAcceptOrder } from '@/api/equipment'
import { useAcceptanceStore } from '@/stores/acceptance'
import List from '@/components/List.vue'
const router = useRouter()
const store = useAcceptanceStore()
interface AcceptanceRecord {
  id: string
  title: string
  code: string
  applicant: string
  department: string
  applyTime: string
  status: number
  statusText: string
}

// 获取状态对应的类型
const getStatusType = (status: number) => {
  const typeMap: Record<string, string> = {
    0: 'warning',
    1: 'success'
  }
  return typeMap[status] || 'info'
}

// 编辑记录
const handleEdit = (record: AcceptanceRecord) => {
  // 已验收状态只能查看
  localStorage.setItem('editData', JSON.stringify(record))
  localStorage.setItem('checkName', record.name)
  localStorage.setItem('signs', JSON.stringify(record.signs || []))
  if (record.status === 1) {
    router.push({
      path: '/acceptance-registration',
      query: {
        mode: 'view'
      }
    })
  } else {
    store.items = record.details || []
    router.push({
      path: '/acceptance-registration',
      query: {
        mode: 'edit'
      }
    })
  }
}

// 新增验收登记
const handleAdd = () => {
  localStorage.removeItem('editData')
  localStorage.removeItem('checkName')
  localStorage.removeItem('signs')
  store.items = []
  router.push('/acceptance-registration')
}
const fetchData = async (page: number, pageSize: number) => {
  const params = {
    page,
    pageSize,
    orderBy: JSON.stringify(["-updated_at", "-created_at"])
  }
  let res = await checkAcceptOrder(params)
  return {
    list: res.items,
    total: res.total
  };
}
// 返回应用中心页面
const handleBack = () => {
  router.push('/apps')
}
</script>

<style scoped>
.content-area {
  min-height: calc(100vh - 4rem);
  padding-bottom: 5rem;
}
</style> 