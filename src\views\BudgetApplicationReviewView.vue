<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-medium">预算申请单</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <!-- 基本信息 -->
      <div class="mb-6">
        <BasicInfoCard
          :applicant="formData.applicant"
          :department="formData.department"
          :apply-time="formData.applyTime"
        />
      </div>

      <form class="space-y-6">
        <!-- 基本表单信息 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4 space-y-4">
            <!-- 预算所属部门 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">预算所属部门</label>
              <div class="w-full p-2 bg-gray-50 rounded-md">
                {{ formData.department }}
              </div>
            </div>

            <!-- 所属预算年 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">所属预算年</label>
              <div class="w-full p-2 bg-gray-50 rounded-md">
                {{ formData.budgetYear }}年
              </div>
            </div>

            <!-- 用途说明 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">用途说明</label>
              <div class="w-full p-2 bg-gray-50 rounded-md min-h-[100px]">
                {{ formData.purpose }}
              </div>
            </div>

            <!-- 备注 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
              <div class="w-full p-2 bg-gray-50 rounded-md">
                {{ formData.remark || '无' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 预算明细 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <div class="flex justify-between items-center mb-4">
              <label class="block text-sm font-medium text-gray-700">
                预算明细
              </label>
            </div>
            
            <div v-if="formData.budgetItems.length === 0" class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg">
              <i class="fas fa-inbox text-3xl mb-2"></i>
              <p>暂无预算明细</p>
            </div>
            
            <div v-else class="space-y-3">
              <div 
                v-for="(item, index) in formData.budgetItems" 
                :key="index" 
                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-900">{{ item.name }}</div>
                  <div class="text-xs text-gray-500 mt-1">
                    <span>类别：{{ item.type }}</span>
                    <span class="mx-2">|</span>
                    <span>单价：¥{{ item.price }}</span>
                    <span class="mx-2">|</span>
                    <span>数量：{{ item.quantity }}</span>
                  </div>
                  <div class="text-sm text-gray-500 mt-1">
                    小计：<span class="font-medium text-primary">¥{{ (Number(item.price) * Number(item.quantity) || 0).toFixed(2) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 总金额 -->
            <div class="flex justify-end items-center space-x-3 pt-4 mt-4 border-t border-gray-200">
              <span class="text-gray-500">总金额：</span>
              <span class="text-2xl font-semibold text-primary">¥{{ totalBudgetAmount.toFixed(2) }}</span>
            </div>
          </div>
        </div>

        <!-- 附件列表 -->
        <div class="form-group">
          <label class="label">附件列表</label>
          <div v-if="formData.attachments.length === 0" class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg">
            <i class="fas fa-cloud-upload-alt text-3xl mb-2"></i>
            <p>暂无附件</p>
          </div>
          <div v-else class="space-y-3">
            <div 
              v-for="(file, index) in formData.attachments" 
              :key="index"
              class="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200"
            >
              <div class="flex-1 min-w-0">
                <div class="flex items-center">
                  <i class="fas fa-file text-gray-400 mr-2"></i>
                  <span class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</span>
                </div>
                <div class="text-xs text-gray-500 mt-1">{{ formatFileSize(file.size) }}</div>
              </div>
              <a 
                href="#"
                class="text-blue-600 hover:text-blue-700"
                @click.prevent="downloadFile(file)"
              >
                <i class="fas fa-download"></i>
              </a>
            </div>
          </div>
        </div>

        <!-- 审批流程 -->
        <div class="form-group">
          <ApprovalFlow class="-ml-4" />
        </div>
      </form>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
      <div class="flex space-x-4">
        <button 
          type="button"
          @click="handleReject"
          class="flex-1 px-4 py-2 border border-red-600 rounded-md text-red-600 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          :disabled="isSubmitting"
        >
          {{ isSubmitting ? '处理中...' : '驳回' }}
        </button>
        <button 
          type="button"
          @click="handleApprove"
          class="flex-1 px-4 py-2 border border-transparent rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          :disabled="isSubmitting"
        >
          {{ isSubmitting ? '处理中...' : '通过' }}
        </button>
      </div>
    </div>

    <!-- 审批结论弹窗 -->
    <el-dialog
      v-model="showApprovalConclusion"
      :title="approvalType === 'approve' ? '审批通过' : '审批驳回'"
      width="95%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      class="approval-dialog"
      destroy-on-close
      top="10vh"
    >
      <ApprovalConclusion
        :type="approvalType"
        @confirm="handleApprovalConfirm"
        @cancel="handleApprovalCancel"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElDialog } from 'element-plus'
import 'element-plus/es/components/dialog/style/css'
import ApprovalFlow from '@/views/order/ApprovalFlow.vue'
import ApprovalConclusion from '@/views/order/ApprovalConclusion.vue'
import BasicInfoCard from '@/views/order/BasicInfoCard.vue'

const router = useRouter()
const route = useRoute()

interface Attachment {
  name: string;
  size: number;
  file: File;
  status: 'uploading' | 'success' | 'error';
  progress?: number;
}

interface BudgetItem {
  name: string;
  type: string;
  price: string;
  quantity: string;
}

interface ApprovalForm {
  comment: string;
  signature: string;
}

// 表单数据
const formData = ref({
  applicant: '张三',
  department: '技术部',
  applyTime: '2024-03-14 10:00',
  budgetYear: new Date().getFullYear().toString(),
  budgetItems: [
    {
      name: '办公用品',
      type: '办公耗材',
      price: '100',
      quantity: '10'
    },
    {
      name: '打印机',
      type: '办公设备',
      price: '2000',
      quantity: '2'
    }
  ],
  purpose: '用于部门日常办公用品采购和设备更新',
  remark: '优先考虑性价比高的产品',
  attachments: [
    {
      name: '预算明细.xlsx',
      size: 1024 * 1024 * 2.5,
      status: 'success',
      file: new File([], '预算明细.xlsx')
    }
  ] as Attachment[]
})

const isSubmitting = ref(false)

// 审批结论弹窗控制
const showApprovalConclusion = ref(false)
const approvalType = ref<'approve' | 'reject'>('approve')

// 计算总预算金额
const totalBudgetAmount = computed(() => {
  return formData.value.budgetItems.reduce((sum, item) => {
    return sum + (Number(item.price) * Number(item.quantity) || 0)
  }, 0)
})

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${(bytes / Math.pow(k, i)).toFixed(1)} ${sizes[i]}`
}

// 下载文件
const downloadFile = (file: Attachment) => {
  // TODO: 实现文件下载逻辑
  ElMessage.info('文件下载功能开发中')
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 处理通过
const handleApprove = () => {
  if (isSubmitting.value) return
  approvalType.value = 'approve'
  showApprovalConclusion.value = true
}

// 处理驳回
const handleReject = () => {
  if (isSubmitting.value) return
  approvalType.value = 'reject'
  showApprovalConclusion.value = true
}

// 处理审批结论确认
const handleApprovalConfirm = async (form: ApprovalForm) => {
  try {
    isSubmitting.value = true
    // TODO: 实现审批提交逻辑
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success(approvalType.value === 'approve' ? '审批通过！' : '已驳回申请！')
    showApprovalConclusion.value = false
    router.back()
  } catch (error) {
    console.error('审批失败:', error)
    ElMessage.error('审批失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

// 处理审批结论取消
const handleApprovalCancel = () => {
  showApprovalConclusion.value = false
}

// 监听路由参数变化，获取申请单详情
onMounted(async () => {
  const orderId = route.query.id
  if (orderId) {
    try {
      // TODO: 这里应该调用API获取申请单详情
      // 目前使用模拟数据
      console.log('获取申请单详情:', orderId)
    } catch (error) {
      console.error('获取申请单详情失败:', error)
      ElMessage.error('获取申请单详情失败')
    }
  }
})
</script>

<style scoped>
.page-container {
  background-color: rgb(249, 250, 251);
  min-height: 100vh;
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
  padding-bottom: 80px; /* 为固定底部按钮留出空间 */
  overscroll-behavior: none; /* 防止iOS橡皮筋效果 */
}

.page-container::-webkit-scrollbar {
  display: none;
  width: 0 !important;
}

.form-content {
  padding: 64px 8px 24px;
  min-height: calc(100vh - 64px);
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
  overscroll-behavior: none;
}

.form-content::-webkit-scrollbar {
  display: none;
  width: 0 !important;
}

/* 全局样式，确保所有容器都不显示滚动条 */
:deep(*) {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

:deep(*::-webkit-scrollbar) {
  display: none;
  width: 0 !important;
}

:deep(.approval-dialog) {
  border-radius: 8px;
  
  .el-dialog__header {
    margin: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
  }
  
  .el-dialog__title {
    font-size: 16px;
    font-weight: 500;
    color: #1f2937;
  }
  
  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
  }

  @media screen and (min-width: 768px) {
    width: 600px !important;
    margin: 0 auto;
  }
}

.form-group {
  @apply space-y-1 relative;
}

.label {
  @apply text-sm text-gray-600 block;
}

.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(24 144 255 / var(--tw-bg-opacity));
}

.text-primary {
  --tw-text-opacity: 1;
  color: rgb(24 144 255 / var(--tw-text-opacity));
}
</style> 