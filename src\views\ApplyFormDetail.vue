<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import BasicInfoCard from '@/views/order/BasicInfoCard.vue'
import { claimOrderDetail } from '@/api/approval'
import NewFileUpload from '@/components/newFileUpload.vue'

const router = useRouter()
const route = useRoute()

// 表单数据
const formData = ref({
  details: [],
  files: []
})


// 返回上一页
const goBack = () => {
  router.back()
}

// 获取申请单详情
const getApplyFormDetail = async (id: string) => {
  try {
    const orderId = route.query.id
    if (orderId) {
      claimOrderDetail(orderId).then(res => {
        res.files = res.hasOwnProperty('files') && res.files ? res.files.split(',') : []
        formData.value = res
      })
    }
  } catch (error) {
    console.error('获取申请单详情失败:', error)
    ElMessage.error('获取申请单详情失败')
  }
}

onMounted(async () => {
  await getApplyFormDetail('')
})
</script>

<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-medium">申请详情</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <!-- 申请表单 -->
    <div class="form-content">
      <form class="space-y-6">
        <!-- 基本信息 -->
        <BasicInfoCard
          :applicant="formData.createdBy"
          :department="formData.createdByDepartmentName"
          :apply-time="formData.createdAt"
        />

        <!-- 事由 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              申领事由
            </label>
            <div class="w-full p-2 bg-gray-50 rounded-md min-h-[100px]">
              {{ formData.reason }}
            </div>
          </div>
        </div>

        <!-- 申领明细 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <div class="flex justify-between items-center mb-4">
              <label class="block text-sm font-medium text-gray-700">
                申领明细
              </label>
            </div>
            
            <div v-if="formData.details.length === 0" class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg">
              <i class="fas fa-inbox text-3xl mb-2"></i>
              <p>暂无申领物品</p>
            </div>
            
            <div v-else class="space-y-3">
              <div 
                v-for="item in formData.details" 
                :key="item.id" 
                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-900 mb-2">{{ item.name }}</div>
                  <div class="text-xs text-gray-500 mb-1.5 flex items-center">
                    <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                    <span>资产编号：{{ item.no }}</span>
                  </div>
                  <div class="text-xs text-gray-500 mb-1.5 flex items-center">
                    <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                    <span>规格型号：{{ item.model }}</span>
                  </div>
                  <div class="text-xs text-gray-500 flex items-center">
                    <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                    <span>数量：{{ item.quantity }}件</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 备注 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              备注
            </label>
            <div class="w-full p-2 bg-gray-50 rounded-md">
              {{ formData.remark || '无' }}
            </div>
          </div>
        </div>

        <!-- 附件列表 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <div class="mb-4">
              <label class="text-sm font-medium text-gray-700">附件列表</label>
              <NewFileUpload v-if="formData.files && formData.files.length" v-model="formData.files" :editable="false" class="mt-2"/>
              <div v-else class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg mt-2">
                <i class="fas fa-cloud-upload-alt text-3xl mb-2"></i>
                <p>暂无附件</p>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<style scoped>
.page-container {
  background-color: rgb(249, 250, 251);
  min-height: 100vh;
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.page-container::-webkit-scrollbar {
  display: none;
}

.form-content {
  padding: 64px 8px 96px;
  min-height: calc(100vh - 64px);
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.form-content::-webkit-scrollbar {
  display: none;
}

form {
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

form::-webkit-scrollbar {
  display: none;
}
</style> 