// import { isMobile } from "@/utils";
import { userInfo, loginStatus } from '@/utils/userInfo'
import qs from 'qs'

export const DEFAULT_CACHE_TIME = 60 * 60 * 24 * 7;

export const TOKEN_KEY = 'h5/userInfo';
let tempToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiJ0ZXN0MjIyIiwidXNlcklkIjoidGVzdDIyMiIsInVzZXJuYW1lIjoiaGVqaWFuIiwiZXhwIjoxNzE5OTAyNzYyLCJzb3VyY2UiOiJwYyJ9.V08iOoGaWILDuiVQB-2tb87-PxpvrAF0eCSKJiVOamo'
let once = (() => true)();
const { token } = qs.parse(window.location.href.split('?')[1], {
  ignoreQueryPrefix: true
}) || { token: '' }
export function updateTempToken(token: string) {
  tempToken = token
  if (loginStatus.userInfo)
    loginStatus.userInfo.token = token
}
export function getToken() {
  if (loginStatus.userInfo)
    return loginStatus.userInfo.token
  if (userInfo?.token)
    return userInfo.token

  // if (isMobile) {
    // handle mobile logic here
    const token = localStorage.getItem(TOKEN_KEY);
    const result = token
      ? JSON.parse(token).token
      : tempToken
    return result;
  // } 
  // else {
  //   // handle PC logic here
  //   if (once) {
  //     once = false
  //     return token
  //   }
  //   return tempToken;
  // }

}

export function clearToken() {
  loginStatus.isLogin = false
  loginStatus.userInfo = null
  localStorage.removeItem(TOKEN_KEY);
}
