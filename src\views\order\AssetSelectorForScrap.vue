<template>
  <div class="page-container min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-medium">选择报废资产</h1>
        <button
          @click="handleConfirm"
          class="text-blue-600 text-sm font-medium"
        >
          完成
        </button>
      </div>
    </div>

    <!-- 搜索框 -->
    <div class="fixed top-12 left-0 right-0 bg-white border-b border-gray-200 z-10 px-4 py-3">
      <div class="relative">
        <input
          v-model="searchKeyword"
          type="text"
          placeholder="搜索资产"
          class="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
      </div>
    </div>

    <!-- 资产列表 -->
    <div class="pt-32 px-4 pb-4">
      <div class="space-y-3">
        <List :request-fn="fetchData" v-slot="{ list }">
          <div
            v-for="asset in list"
            :key="asset.id"
            class="bg-white p-4 rounded-lg shadow-sm flex items-center mb-2"
            @click="toggleAssetSelection(asset)"
          >
            <div class="flex-1">
              <div class="text-sm font-medium text-gray-900">{{ asset.name }}</div>
              <div class="text-xs text-gray-500">资产编号：{{ asset.no }}</div>
              <div class="text-xs text-gray-500 mt-1">规格型号：{{ asset.model }}</div>
            </div>
            <div class="ml-4">
              <div
                class="w-6 h-6 rounded-full border-2 flex items-center justify-center"
                :class="isSelected(asset) ? 'border-blue-600 bg-blue-600' : 'border-gray-300'"
              >
                <i v-if="isSelected(asset)" class="fas fa-check text-white text-sm"></i>
              </div>
            </div>
          </div>
        </List>
      </div>

      <!-- 空状态 -->
      <!-- <div v-if="filteredAssets.length === 0" class="text-center py-8 text-gray-500">
        <i class="fas fa-search text-3xl mb-2"></i>
        <p>未找到相关资产</p>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { myMaterial } from '@/api/equipment'
import List from '@/components/List.vue'
interface Asset {
  id: number
  name: string
  code: string
  model?: string
}

const router = useRouter()
const route = useRoute()

// 搜索关键词
const searchKeyword = ref('')

// 当前选中的资产
const selectedAssets = ref<Asset[]>(route.query.selectedAssets ? JSON.parse(route.query.selectedAssets as string) : [])

// 检查资产是否被选中
const isSelected = (asset: Asset) => {
  return selectedAssets.value.some(item => item.id === asset.id)
}

// 切换资产选中状态
const toggleAssetSelection = (asset: Asset) => {
  const index = selectedAssets.value.findIndex(item => item.id === asset.id)
  if (index > -1) {
    selectedAssets.value.splice(index, 1)
  } else {
    selectedAssets.value.push(asset)
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 确认选择
const handleConfirm = () => {
  router.replace({
    path: '/scrap-application',
    query: {
      selectedAssets: JSON.stringify(selectedAssets.value)
    }
  })
}
const fetchData = async (page: number, pageSize: number) => {
  const params = {
    page,
    pageSize,
    query: JSON.stringify([{ status: '1' }, { name__contains: searchKeyword.value }]),
    orderBy: JSON.stringify(["-updated_at", "-created_at"])
  }
  let res = await myMaterial(params)
  if (res.items && res.items.length) {
    res.items = res.items.map(item => {
      return {
        ...item,
        materialId: item.id
      }
    })
  }
  return {
    list: res.items,
    total: res.total
  };

}
watch(
  () => searchKeyword.value,
  (newValue, oldValue) => {
    fetchData(1, 10)
  },
  { deep: true }
)
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  position: relative;
  z-index: 10;
  padding: 0;
}
</style>
