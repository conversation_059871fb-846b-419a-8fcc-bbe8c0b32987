<template>
  <div class="page-container">
    <!-- 顶部导航栏 -->
    <header class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
      <div class="text-gray-800 py-3 flex items-center justify-center relative border-b">
        <button 
          @click="goBack" 
          class="absolute left-4 top-1/2 transform -translate-y-1/2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-600">
            <path d="M19 12H5M12 19l-7-7 7-7"/>
          </svg>
        </button>
        <h1 class="text-lg font-medium">报废申请</h1>
      </div>
    </header>

    <!-- 申请表单 -->
    <main class="pt-16 pb-20 px-4 bg-gray-50 min-h-screen">
      <form @submit.prevent="handleSubmit" class="space-y-4">
        <!-- 资产信息 -->
        <div class="bg-white rounded-lg p-4 space-y-4">
          <!-- 报废资产列表 -->
          <div class="form-group">
            <div class="flex justify-between items-center mb-4">
              <label class="block text-sm font-medium text-gray-700">
                <span class="text-red-500">*</span>报废资产
              </label>
              <button 
                type="button"
                @click="goToAssetSelector"
                class="inline-flex items-center px-3 py-1.5 border border-blue-600 text-blue-600 rounded-md text-sm font-medium hover:bg-blue-50"
              >
                <i class="fas fa-plus mr-1"></i>
                添加资产
              </button>
            </div>
            
            <div v-if="selectedAssets.length === 0" class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg">
              <i class="fas fa-inbox text-3xl mb-2"></i>
              <p>暂未选择资产</p>
            </div>
            
            <div v-else class="space-y-3">
              <div 
                v-for="asset in selectedAssets" 
                :key="asset.id" 
                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-900">{{ asset.name }}</div>
                  <div class="text-xs text-gray-500">资产编号：{{ asset.no }}</div>
                </div>
                <button 
                  type="button"
                  @click="removeAsset(asset.id)"
                  class="text-red-500 hover:text-red-600 p-1"
                >
                  <i class="fas fa-trash-alt"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- 报废原因 -->
          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <span class="text-red-500">*</span>报废原因
            </label>
            <textarea 
              v-model="formData.reason"
              rows="4"
              class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入报废原因"
            ></textarea>
          </div>

          <!-- 报废说明 -->
          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-2">备注说明</label>
            <textarea 
              v-model="formData.remark"
              rows="3"
              class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入备注说明（选填）"
            ></textarea>
          </div>

          <!-- 上传附件 -->
          <div class="form-group">
            <label class="label">附件</label>
            <NewFileUpload v-model="formData.files" class="mt-2"/>
          </div>
        </div>
      </form>
    </main>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
      <button 
        type="button"
        @click="handleSubmit"
        class="w-full px-4 py-2 border border-transparent rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        :disabled="isSubmitting"
      >
        {{ isSubmitting ? '提交中...' : '下一步' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as ww from '@wecom/jssdk'
import { hpDiscardOrder } from '@/api/approval'
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import NewFileUpload from '@/components/newFileUpload.vue'

const router = useRouter()
const route = useRoute()

interface Asset {
  brand?: string;
  checkFiles?: string;
  createdAt?: string;
  departmentId?: number;
  id: string;
  images?: string;
  isFromCheckAcceptOrder?: number;
  model?: string;
  createdBy?: string;
  name?: string;
  no?: string;
  quantity?: number;
  status?: number;
  totalPrice?: number;
  type?: string;
  unit?: string;
  unitPrice?: number;
  updatedAt?: string;
  updatedBy?: string;
  userId?: string;
}

// 表单数据
const formData = reactive({
  reason: '',
  remark: '',
  files: []
})

// 选中的资产列表
const selectedAssets = ref<Asset[]>([])

// 监听路由参数变化，更新选中的资产
onMounted(() => {
  if (localStorage.getItem('scrapData')) {
    let scrapData = JSON.parse(localStorage.getItem('scrapData') as string)
    formData.reason = scrapData.reason
    formData.remark = scrapData.remark
    formData.files = scrapData.files
    localStorage.removeItem('scrapData')
  }
  if (route.query.selectedAssets) {
    selectedAssets.value = JSON.parse(route.query.selectedAssets as string)
    console.log('资产', selectedAssets.value);
    
  }
})

const isSubmitting = ref(false)

// 移除资产
const removeAsset = (assetId: number) => {
  const index = selectedAssets.value.findIndex(asset => asset.id === assetId)
  if (index > -1) {
    selectedAssets.value.splice(index, 1)
  }
}

// 返回上一页
const goBack = () => {
  router.push('/apps')
}

// 跳转到资产选择页面
const goToAssetSelector = () => {
  localStorage.setItem('scrapData', JSON.stringify(formData))
  router.push({
    path: '/asset-selector-scrap',
    query: {
      selectedAssets: JSON.stringify(selectedAssets.value)
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  if (isSubmitting.value) return

  // 表单验证
  if (selectedAssets.value.length === 0) {
    ElMessage.error('请选择需要报废的资产')
    return
  }

  if (!formData.reason) {
    ElMessage.error('请填写报废原因')
    return
  }
  
  try {
    isSubmitting.value = true
    // TODO: 实现提交逻辑
    const data = {
      ...formData,
      files: formData.files && formData.files.length ? formData.files.join(',') : '',
      details: JSON.parse(JSON.stringify(selectedAssets.value))
    }
    console.log('提交的数据', data);
    const startTime = Date.now()
    const res = await hpDiscardOrder(data)
    // 计算已经过去的时间
    const elapsedTime = Date.now() - startTime
    // 如果耗时小于200ms，则等待剩余时间
    if (elapsedTime < 200) {
      await new Promise(resolve => setTimeout(resolve, 200 - elapsedTime))
    }

    // 显示成功提示
    // showSuccessModal.value = true
    // 开始时间
    console.log( Date.now(), '开始')
    const url = import.meta.env.VITE_URL
    const link = `${url}#/scrap-application-detail?id=${res.id}`
    let templateId = import.meta.env.VITE_ENV === 'prod' ? 'f2852b5893c444a79a3dbb1958099b9e_827558317' : '6e23363a69d26c2c66ebc688da0e1c04_1768581547'
    ww.thirdPartyOpenPage({
      oaType: ww.OAType.create_approval,
      templateId,
      thirdNo: 'BFSQ-' + res.id,
      extData: {
        fieldList: [
          {
            title: '申请类型',
            type: ww.OaExtDataType.text,
            value: '报废申请',
          },
          {
              'title': '申请时间',
              'type': ww.OaExtDataType.text,
              'value': res.createdAt,
          },
          {
            'title': '报废原因',
            'type': ww.OaExtDataType.text,
            'value': formData.reason,
          },
          {
            type: ww.OaExtDataType.link,
            title: '详细内容',
            value: link
          }
        ]
      },
      success: () => {
        console.log( Date.now(), '提交成功')
        // router.push('/apps')
      },
      fail: () => {
        console.log( Date.now(), '提交失败')
      },
      complete: () => {
        console.log( Date.now(), '提交完成')
      }
    })
    
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped>
/* .page-container {
  min-height: 100vh;
  position: relative;
  z-index: 10;
  padding: 0;
}

.form-content {
  padding-bottom: 80px;
} */
</style> 