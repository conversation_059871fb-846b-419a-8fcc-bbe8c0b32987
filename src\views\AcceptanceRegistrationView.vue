<template>
  <div class="page-container bg-gray-50 min-h-screen flex flex-col">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-4">
        <button @click="handleBack" class="w-9 h-9 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition">
          <i class="fas fa-arrow-left text-gray-600"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-800">{{ getPageTitle }}</h1>
        <button v-if="isEdit && !isCompleted" 
          @click="handleDelete"
          class="w-9 h-9 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition"
        >
          <i class="fas fa-trash-alt text-sm text-gray-600"></i>
        </button>
        <div v-else class="w-9"></div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="flex-1 overflow-y-auto">
      <div class="content-area pt-16 px-2 pb-24">
        <div class="space-y-6">
          <!-- 验收描述 -->
          <div class="bg-white rounded-lg shadow-sm">
            <div class="p-4">
              <el-form ref="formRef" :model="formData" :rules="formRules">
                <el-form-item label="主题" prop="name">
                  <el-input 
                    v-model="formData.name" 
                    placeholder="请输入主题"
                    :disabled="isCompleted"
                  />
                </el-form-item>
              </el-form>
            </div>
          </div>

          <!-- 验收物品 -->
          <div class="bg-white rounded-lg shadow-sm">
            <div class="p-4">
              <div class="flex justify-between items-center mb-4">
                <label class="block text-sm font-medium text-gray-700">
                  验收物品
                </label>
                <el-button 
                  v-if="!isCompleted"
                  type="primary" 
                  @click="handleAddItem" 
                  plain 
                  size="small"
                >
                  <i class="fas fa-plus mr-1"></i>添加物品
                </el-button>
              </div>
              
              <div v-if="formData.details.length === 0" class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg">
                <i class="fas fa-inbox text-3xl mb-2"></i>
                <p>暂无验收物品</p>
              </div>
              
              <div v-else class="space-y-3">
                <div 
                  v-for="(item, index) in formData.details" 
                  :key="index" 
                  class="flex justify-between items-start p-3 bg-gray-50 rounded-lg border border-gray-200"
                >
                  <div class="flex-1" @click="handleEditItem(item)">
                    <div class="flex justify-between items-start">
                      <div class="text-base font-medium text-gray-900 mb-2">{{ item.name }}</div>
                      <el-button 
                        v-if="!isCompleted"
                        type="danger" 
                        @click="removeItem(index)" 
                        link
                      >
                        <i class="fas fa-trash-alt"></i>
                      </el-button>
                    </div>
                    <div class="mt-2">
                      <div class="text-xs text-gray-500 mb-1.5 flex items-center">
                        <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                        <span>资产类型：{{ item.type === 'fixed' ? '固定资产' : '无形资产' }}</span>
                      </div>
                      <template v-if="item.type === 'fixed'">
                        <div class="text-xs text-gray-500 mb-1.5 flex items-center">
                          <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                          <span>品牌型号：{{ item.brand }} {{ item.model }}</span>
                        </div>
                        <div class="text-xs text-gray-500 mb-1.5 flex items-center">
                          <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                          <span>存放地点：{{ item.address }}</span>
                        </div>
                      </template>
                      <div class="text-xs text-gray-500 mb-1.5 flex items-center">
                        <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                        <span>单价：¥{{ item.unitPrice }} / 数量：{{ item.quantity }}{{ item.unit }}</span>
                      </div>
                      <div class="text-xs text-gray-500 mb-1.5 flex items-center">
                        <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                        <span>取得方式：{{ item.sourceName }}</span>
                      </div>
                      <div v-if="item.departmentName || item.userName" class="text-xs text-gray-500 mb-1.5 flex items-center">
                        <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                        <span v-if="item.departmentName">管理部门：{{ item.departmentName }} / </span>
                        <span v-if="item.userName">管理人：{{ item.userName }}</span>
                      </div>
                      <div class="text-xs text-gray-500 flex items-center">
                        <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                        <!-- <span>总金额：<span class="text-primary font-medium">¥{{ item.totalAmount.toFixed(2) }}</span></span> -->
                        <span>总金额：<span class="text-primary font-medium">¥{{ item.totalPrice }}</span></span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 总金额 -->
              <div v-if="formData.details.length > 0" class="flex justify-end items-center space-x-3 pt-4 mt-4 border-t border-gray-200">
                <span class="text-gray-500">总金额：</span>
                <span class="text-xl font-semibold text-primary">¥{{ totalAmount.toFixed(2) }}</span>
              </div>
            </div>
          </div>

          <!-- 验收记录 -->
          <div class="bg-white rounded-lg shadow-sm">
            <div class="p-4">
              <div class="flex justify-between items-center mb-4">
                <label class="block text-sm font-medium text-gray-700">验收记录</label>
                <el-button 
                  v-if="!isCompleted"
                  type="primary" 
                  @click="showSignatureDialog = true" 
                  plain 
                  size="small"
                >
                  <i class="fas fa-plus mr-1"></i>验收签字
                </el-button>
              </div>

              <div v-if="formData.signs.length === 0" 
                class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg"
              >
                <i class="fas fa-clipboard-list text-3xl mb-2"></i>
                <p>暂无验收记录</p>
              </div>

              <div v-else class="space-y-3">
                <div v-for="(record, index) in formData.signs" 
                  :key="index"
                  class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-sm transition-shadow duration-200"
                >
                  <div class="p-4">
                    <div class="flex justify-between items-start">
                      <div class="flex items-center space-x-2">
                        <div class="flex items-center justify-center w-8 h-8 rounded-full bg-blue-50 text-blue-600">
                          <i class="fas" :class="record.type === '1' ? 'fa-user-cog' : 'fa-user-check'"></i>
                        </div>
                        <div>
                          <div class="text-sm font-medium text-gray-900">{{ record.type === '1' ? '经办人' : '验收人' }}</div>
                          <div class="text-xs text-gray-500 mt-0.5">
                            <span class="mr-2">{{ record.createdBy }}</span>
                            <span>{{ record.createdAt }}</span>
                          </div>
                        </div>
                      </div>
                      <el-button 
                        v-if="!isCompleted"
                        type="danger" 
                        @click="removeRecord(index)" 
                        link 
                        class="!p-1"
                      >
                        <i class="fas fa-trash-alt text-sm"></i>
                      </el-button>
                    </div>
                    
                    <div class="mt-3 text-sm">
                      <div class="text-gray-500 mb-1">{{ record.type === '1' ? '经办说明：' : '验收说明：' }}</div>
                      <div class="text-gray-700 bg-gray-50 rounded p-2 min-h-[2.5rem]">{{ record.remark || '无' }}</div>
                    </div>

                    <div class="mt-3">
                      <div class="text-sm text-gray-500 mb-2">签名：</div>
                      <div class="bg-gray-50 rounded-lg p-3 flex items-center justify-center">
                        <el-image 
                          v-if="record.signUrl" 
                          :src="record.signUrl" 
                          alt="签名" 
                          class="h-14 object-contain cursor-pointer hover:opacity-90"
                          :preview-src-list="[record.signUrl]"
                          :initial-index="0"
                          fit="contain"
                          preview-teleported
                          hide-on-click-modal
                        />
                        <span v-else class="text-gray-400 text-sm">暂无签名</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-4 py-3">
      <div class="max-w-7xl mx-auto">
        <template v-if="!isCompleted">
          <div class="grid grid-cols-2 gap-3">
            <el-button class="!h-10 w-full" @click="handleSubmit(1)">验收完成</el-button>
            <el-button type="primary" class="!h-10 w-full" @click="handleSubmit(0)" :loading="submitting">
              保存
            </el-button>
          </div>
        </template>
        <!-- <template v-else>
          <div class="flex justify-center">
            <el-button type="primary" class="!h-10 w-48" @click="handleDownload">
              <i class="fas fa-download mr-1"></i>下载图片
            </el-button>
          </div>
        </template> -->
      </div>
    </div>

    <!-- 签名验收弹框 -->
    <el-dialog
      v-model="showSignatureDialog"
      title="签名验收"
      width="85%"
      :close-on-click-modal="false"
      :show-close="true"
      @close="handleSignatureDialogClose"
      class="signature-dialog"
    >
      <AcceptanceSignature
        ref="signatureRef"
        @confirm="handleSignatureConfirm"
        @cancel="showSignatureDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import type { FormInstance } from 'element-plus'
import { ElMessageBox, ElMessage } from 'element-plus'
import AcceptanceSignature from './order/AcceptanceSignature.vue'
import { useAcceptanceStore } from '@/stores/acceptance'
import { getUserInfo } from "@/utils/userInfo";
import { addCheckAcceptOrder, editCheckAcceptOrder, delCheckAcceptOrder } from '@/api/equipment'
import { showToast } from 'vant'

const router = useRouter()
const route = useRoute()
const store = useAcceptanceStore()
const formRef = ref<FormInstance>()
const submitting = ref(false)
const isEdit = ref(false)
const isView = ref(false)
const isCompleted = ref(false)

// 表单数据
interface FormItem {
  name: string
  quantity: number
  unit: string
  type: 'fixed' | 'intangible'  // 资产类型
  brand?: string                     // 品牌
  model?: string                     // 规格型号
  unitPrice: number                  // 单价
  totalAmount: number                // 总金额
  totalPrice: number                // 总金额
  sourceName: string          // 取得方式
  departmentName: string       // 管理/使用部门
  userName: string                    // 管理/使用人
  address?: string                  // 存放地点
  photos: string[]                  // 照片
}

interface AcceptanceRecord {
  createdAt: string
  createdBy: undefined | string
  type: '1' | '2'
  // name: string
  signUrl: string
  remark: string
}

interface FormData {
  details: FormItem[]
  name: string
  signs: AcceptanceRecord[]
}

const formData = reactive<FormData>({
  details: [],
  name: '',
  signs: []
})
// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入主题', trigger: 'blur' }]
}

// 监听store中的物品列表变化
watch(() => store.items, 
  (newItems) => {
    formData.details = [...newItems]
  },
  { immediate: true }
)

// 删除物品
const removeItem = (index: number) => {
  ElMessageBox.confirm(
    '确定要删除该物品吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    store.removeItem(index)
    formData.details = store.items
    ElMessage.success('删除成功')
  }).catch(() => {})
}
const handleEditItem = (item) => {
  localStorage.setItem('checkName', formData.name)
  localStorage.setItem('signs', JSON.stringify(formData.signs))
  router.push({
    path: '/acceptance-item',
    query: {
      fromMode: route.query.mode,
      mode: isView.value ? 'view' : 'edit',
      data: JSON.stringify(item)
    }
  })
}
// 提交表单
const handleSubmit = async (status:number) => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    let api = isEdit.value ? editCheckAcceptOrder : addCheckAcceptOrder
    api({ ...formData, status }).then(res => {
      showToast('提交成功')
      router.push('/acceptance-records')
    })
    // submitting.value = true
    // // TODO: 调用API保存数据
    // console.log('提交的数据:', formData)
    // setTimeout(() => {
    //   submitting.value = false
    //   ElMessage.success('保存成功')
    //   router.push('/acceptance-records')
    // }, 1000)
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否完整')
  }
}

// 删除记录
const removeRecord = (index: number) => {
  ElMessageBox.confirm(
    '确定要删除该验收记录吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    formData.signs.splice(index, 1)
    ElMessage.success('删除成功')
  }).catch(() => {})
}

// 初始化编辑数据
const initEditData = () => {
  Object.assign(formData, JSON.parse(localStorage.getItem('editData') as string))
}

// 计算页面标题
const getPageTitle = computed(() => {
  if (isView.value) return '验收登记详情'
  return isEdit.value ? '编辑验收登记' : '新增验收登记'
})

// 删除记录
const handleDelete = () => {
  ElMessageBox.confirm(
    '确定要删除该验收登记记录吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // TODO: 调用API删除数据
    delCheckAcceptOrder(formData.id).then(res => {
      ElMessage.success('删除成功')
      router.back()
    })
  }).catch(() => {})
}

// 计算总金额
const totalAmount = computed(() => {
  return formData.details.reduce((sum, item) => {
    return sum + item.totalPrice
  }, 0)
})

// // 计算单个物品总金额
// const calculateTotalAmount = (item: FormItem) => {
//   item.totalAmount = item.unitPrice * item.quantity
// }

// // 监听单价和数量变化
// watch(() => formData.details, (items) => {
//   items.forEach(item => {
//     calculateTotalAmount(item)
//   })
// }, { deep: true })

// 签名验收相关
const showSignatureDialog = ref(false)
const signatureRef = ref()

const handleSignatureConfirm = (data: { type: string; remark: string; signUrl: string; time: string }) => {
  formData.signs.push({
    type: data.type as '1' | '2',
    signUrl: data.signUrl,
    remark: data.remark,
    createdBy: getUserInfo()?.nickname,
    createdAt: data.time, // 这里应该是从用户信息中获取的当前用户姓名
  })
  showSignatureDialog.value = false
  ElMessage.success('验收签字完成')
}

const handleSignatureDialogClose = () => {
  showSignatureDialog.value = false
}

// 添加物品
const handleAddItem = () => {
  localStorage.setItem('checkName', formData.name)
  localStorage.setItem('signs', JSON.stringify(formData.signs))
  router.push({
    path: '/acceptance-item',
    query: {
      fromMode: isEdit.value ? 'edit' : 'add'
    }
  })
}

// 返回列表页
const handleBack = () => {
  router.push('/acceptance-records')
}

onMounted(() => {
  isEdit.value = route.query.mode === 'edit'
  isView.value = route.query.mode === 'view'
  // 如果是查看模式，自动设置为只读状态
  if (isView.value) {
    isCompleted.value = true
  }
  if (route.query.mode === 'view') {
    initEditData()
  } else {
    if (route.query.mode === 'edit') {
      initEditData()
    }
    formData.details = [...store.items]
    formData.name = localStorage.getItem('checkName') || '',
    formData.signs = localStorage.getItem('signs') ? JSON.parse(localStorage.getItem('signs')) : []
    // localStorage.removeItem('checkName')
    // localStorage.removeItem('signs')
  }
})
</script>

<style scoped>
.content-area {
  min-height: calc(100vh - 5rem);
  max-width: 700px;
  margin: 0 auto;
}

/* 隐藏滚动条但保持滚动功能 */
.content-area::-webkit-scrollbar {
  display: none;
}

.page-container {
  height: 100vh;
  overflow: hidden;
}

/* 隐藏滚动条但保持滚动功能 */
.flex-1::-webkit-scrollbar {
  display: none;
}

.flex-1 {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.form-section {
  @apply border-b border-gray-100 pb-8 mb-8 last:border-0 last:pb-0 last:mb-0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  flex-wrap: nowrap;
}

:deep(.el-form-item__label) {
  @apply text-gray-700 font-medium whitespace-nowrap;
  font-size: 14px;
  padding: 0 8px 8px 0;
  line-height: 1.5;
  height: 32px;
  line-height: 32px;
}

:deep(.el-form-item__content) {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__inner) {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

:deep(.el-input__wrapper:hover),
:deep(.el-textarea__inner:hover) {
  border-color: #d1d5db;
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-textarea__inner:focus) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

:deep(.el-radio) {
  margin-right: 20px;
}

:deep(.el-radio__label) {
  font-size: 14px;
  padding-left: 8px;
}

:deep(.el-upload--picture-card) {
  --el-upload-picture-card-size: 100px;
  border-radius: 8px;
  border: 1px dashed #d1d5db;
  background-color: #f9fafb;
}

:deep(.el-upload--picture-card:hover) {
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}

/* 物品卡片样式 */
.bg-white.border {
  @apply transition duration-200;
}

.bg-white.border:hover {
  @apply shadow-md;
}

.text-primary {
  --tw-text-opacity: 1;
  color: rgb(24 144 255 / var(--tw-text-opacity));
}

/* 添加弹框相关样式 */
:deep(.el-dialog) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__headerbtn) {
  top: 18px;
}

:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
}

/* 签名验收弹框样式 */
:deep(.signature-dialog) {
  .el-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: 90vh;
    width: 85% !important;
    max-width: 420px;
    box-sizing: border-box;
    border-radius: 8px;
  }

  .el-dialog__body {
    flex: 1;
    overflow: hidden;
    padding: 0;
    width: 100%;
    box-sizing: border-box;
  }
  
  .el-dialog__header {
    margin: 0;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;
    width: 100%;
    box-sizing: border-box;
  }

  .el-dialog__headerbtn {
    top: 14px;
    right: 12px;
  }

  .el-dialog__title {
    font-size: 16px;
    font-weight: 500;
    color: #374151;
    padding-right: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* 自定义滚动条样式 */
:deep(.signature-dialog .signature-form::-webkit-scrollbar) {
  width: 4px;
}

:deep(.signature-dialog .signature-form::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 2px;
}

:deep(.signature-dialog .signature-form::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 2px;
}

:deep(.signature-dialog .signature-form::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

/* 确保表单项之间有合适的间距 */
:deep(.signature-dialog) {
  .el-form-item {
    margin-bottom: 16px;
    width: 100%;
    box-sizing: border-box;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* 验收记录卡片样式 */
:deep(.fa-user-cog),
:deep(.fa-user-check) {
  font-size: 14px;
}

.bg-white.border {
  @apply transition duration-200;
}

.bg-white.border:hover {
  @apply shadow-md;
}

/* 签名图片预览样式 */
:deep(.el-image-viewer__wrapper) {
  background-color: rgba(0, 0, 0, 0.8);
}

:deep(.el-image-viewer__close) {
  color: #fff;
  font-size: 24px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  line-height: 36px;
  margin: 16px;
}

:deep(.el-image-viewer__actions) {
  padding: 16px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 20px;
  margin-bottom: 16px;
}

:deep(.el-image-viewer__actions__inner) {
  gap: 20px;
}

:deep(.el-image-viewer__btn) {
  color: #fff;
  font-size: 18px;
  
  &:hover {
    color: var(--el-color-primary);
  }
}

:deep(.el-image-viewer__canvas) {
  img {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* 只读状态样式 */
:deep(.el-input.is-disabled .el-input__wrapper),
:deep(.el-textarea.is-disabled .el-textarea__inner) {
  background-color: #f9fafb;
  border-color: #e5e7eb;
  color: #374151;
  cursor: not-allowed;
}

:deep(.el-input.is-disabled .el-input__inner),
:deep(.el-textarea.is-disabled .el-textarea__inner) {
  color: #374151;
  -webkit-text-fill-color: #374151;
}

:deep(.el-button.is-disabled) {
  background-color: #f3f4f6;
  border-color: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
}
</style> 