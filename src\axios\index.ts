// eslint-disable-next-line

// eslint-ignore
import type { AxiosTransform, CreateAxiosOptions } from './axiosTransform';
import { VAxios } from './Axios';
import { ContentTypeEnum } from './httpEnum';
import { deepMerge } from './utils';
import axios from 'axios';
import type { AxiosInstance, AxiosResponse } from 'axios';
import { getToken } from './token';
// import { login } from '../src/pages/login';
import { clearUserInfo } from '@/utils/userInfo'
import { getWxUrl } from '@/api/login';
import { showToast } from 'vant'

let relogin_times = 0

const transform: AxiosTransform = {
  /**
   * @description: 处理响应数据.如果数据不是预期格式,可直接抛出错误
   */
  transformResponseHook: (res: AxiosResponse<any>, options: any) => {
    const { isTransformResponse, isReturnNativeResponse } = options;
    // 是否返回原生响应头 比如:需要获取响应头时使用该属性
    if (isReturnNativeResponse) {
      return res;
    }
    // 不进行任何处理,直接返回
    // 用于页面代码可能需要直接获取code,data,message这些信息时开启
    if (!isTransformResponse) {
      return res.data;
    }
    // 错误的时候返回

    const { data } = res;
    if (!data) {
      // return '[HTTP] Request has no return value';
      // throw new Error(t('sys.api.apiRequestFailed'));
    }
    //  这里 code,result,message为 后台统一的字段,需要在 types.ts内修改为项目自己的接口返回格式

    // 这里逻辑可以根据项目进行修改
    const hasSuccess = data && res.status === 200;
    if (hasSuccess) {
      if (data.result) return data.result;
      // let successMsg = options.successMessage;
      // console.log({data,options})

      // 消息提示
      return data;
    }

    // 在此处根据自己项目的实际情况对不同的code执行不同的操作
    // 如果不希望中断当前请求,请return数据,否则直接抛出异常即可
    const timeoutMsg = '';
    // switch (code) {
    //   case ResultEnum.TIMEOUT:
    //     timeoutMsg = t('sys.api.timeoutMessage');
    //     const userStore = useUserStoreWithOut();
    //     userStore.logout(true);
    //     break;
    //   default:
    //     if (message) {
    //       timeoutMsg = message;
    //     }
    // }

    // errorMessageMode='modal'的时候会显示modal错误弹窗,而不是消息提示,用于一些比较重要的错误
    // errorMessageMode='none' 一般是调用时明确表示不希望自动弹出错误提示
    // if (options.errorMessageMode === 'modal') {
    //   createErrorModal({ title: t('sys.api.errorTip'), content: timeoutMsg });
    // } else if (options.errorMessageMode === 'message') {
    //   createMessage.error(timeoutMsg);
    // }

    throw new Error(timeoutMsg);
  },

  /**
   * @description: 请求拦截器处理
   */
  requestInterceptors: (config, _) => {
    // 请求之前处理config
    const token = getToken();
    config.headers['Authorization'] = `Bearer ${token}`;
    // if (token && (config as Recordable)?.requestOptions?.withToken !== false) {
    //   // jwt token
    //   (config as Recordable).headers.Authorization = options.authenticationScheme
    //     ? `${options.authenticationScheme} ${token}`
    //     : token;
    // }
    // if (loginStatus.isLogin && !userInfo && !config.url?.endsWith('getUserInfo')) {
    //   onGetUserInfo().then(res => {
    //     setUserInfo(res)
    //   })
    // }

    return config;
  },

  /**
   * @description: 响应拦截器处理
   */
  responseInterceptors: (res: AxiosResponse<any>) => {
    console.log('%c🤪 ~ file: index.ts:91 [transform/responseInterceptors] -> res : ', 'color: #84cd16', res);

    return res;
  },

  /**
   * @description: 响应错误处理
   */
  responseInterceptorsCatch: (_: AxiosInstance, error: any) => {
    const response = error.response
    if (!response) {
      return Promise.reject(error.message)
    }
    const status = response?.status
    if (status === 401) {
      console.log('接口400000000000000001');
      // onGetUserInfo().then(res => {
      //   // setUserInfo(res)
      //   console.log('ressssssssss', res);

      // })
      // token 过期
      // 重新登录
      clearUserInfo()
      // setUserInfo(null)
      // localStorage.removeItem(TOKEN_KEY)

      // const redirect = window.location.hash.slice(1)
      // if (!redirect.includes('login'))
      //   window.location.href = `/#/login?redirect=${encodeURIComponent(redirect)}`
      showToast({
        message: '登录已过期',
        duration: 1000
      })
      getWxUrl({ redirect_uri: window.location.href }).then(res => {
        window.location.href = res.url
      })

      // 如果是 pc 则不重试
      // if (isMobile) {
      //   if (relogin_times > 2) {
      //     return Promise.reject(error)
      //   }
      //   if (import.meta.env.PROD)
      //     // login().then(() => {
      //     //   // 重新登录成功
      //     //   relogin_times = 0
      //     // })
      //   relogin_times++
      // }
    }
    // const { t } = useI18n();
    // const errorLogStore = useErrorLogStoreWithOut();
    // errorLogStore.addAjaxErrorInfo(error);
    // const { response, code, message, config } = error || {};
    // const errorMessageMode = config?.requestOptions?.errorMessageMode || 'none';
    // const msg: string = response?.data?.message ?? '';
    // const err: string = error?.toString?.() ?? '';
    const errMessage = response.data.message || response.data.reason;
    if (axios.isCancel(error)) {
      return Promise.reject(error);
    }
    try {
      // console.log(code, message, response.data);

      // if (response?.data?.code == 400 && response?.data?.message == '用户未登录') {
      //   const userStore = useUserStoreWithOut();
      //   userStore.logout(true);
      //   createMessage.error(t('sys.api.timeoutMessage'));
      //   return;
      // }

      // if (code === 'ECONNABORTED' && message.indexOf('timeout') !== -1) {
      //   errMessage = t('sys.api.apiTimeoutMessage');
      // }
      // if (err?.includes('Network Error')) {
      //   errMessage = t('sys.api.networkExceptionMsg');
      // }

      if (errMessage) {
        // if (errorMessageMode === 'modal') {
        //   createErrorModal({ title: t('sys.api.errorTip'), content: errMessage });
        // } else if (errorMessageMode === 'message') {
        //   createMessage.error(errMessage);
        // }
        showToast({
          message: errMessage,
          duration: 1000
        })
        return Promise.reject(error);
      }
    } catch (error) {
      throw new Error(error as unknown as string);
    }

    // checkStatus(error?.response?.status, msg, errorMessageMode);

    // 如果是对象,就是服务器返回的错误信息,不用再重试
    if (typeof error?.response?.data === 'object') {
      return Promise.reject(error);
    }

    // 添加自动重试机制 保险起见 只针对GET请求
    // const retryRequest = new AxiosRetry();
    // const { isOpenRetry } = config.requestOptions.retryRequest;
    // config.method?.toUpperCase() === RequestEnum.GET &&
    //   isOpenRetry &&
    //   // @ts-ignore
    //   retryRequest.retry(axiosInstance, error);
    return Promise.reject(error);
  },
};

function createAxios(opt?: Partial<CreateAxiosOptions>) {
  return new VAxios(
    // 深度合并
    deepMerge(
      {
        // See https://developer.mozilla.org/en-US/docs/Web/HTTP/Authentication#authentication_schemes
        // authentication schemes,e.g: Bearer
        timeout: 10 * 1000,
        // 基础接口地址
        // baseURL: globSetting.apiUrl,
        baseURL: '/basic-api',
        transform,
        headers: { 'Content-Type': ContentTypeEnum.JSON, 'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiJuaWpsIiwidXNlcklkIjoibmlqbCIsInVzZXJuYW1lIjoibmlqbCIsImV4cCI6MTcxMTk0MzA4MX0.raaqGyObC0bXFXbCiDHhurqlyWk0kKBXFe_Xw3qWZNA', },
        // 如果是form-data格式
        // headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
        // 配置项,下面的选项都可以在独立的接口请求中覆盖
        requestOptions: {
          // 默认将prefix 添加到url
          joinPrefix: true,
          // 是否返回原生响应头 比如:需要获取响应头时使用该属性
          isReturnNativeResponse: false,
          // 需要对返回数据进行处理
          isTransformResponse: true,
          // post请求的时候添加参数到url
          joinParamsToUrl: false,
          // 格式化提交参数时间
          formatDate: true,
          // 消息提示类型
          errorMessageMode: 'message',
          //  是否加入时间戳
          joinTime: true,
          // 忽略重复请求
          ignoreCancelToken: true,
          // 是否携带token
          withToken: true,
          retryRequest: {
            isOpenRetry: true,
            count: 2,
            waitTime: 500,
          },
        },
      },
      opt || {},
    ),
  );
}
export const defHttp = createAxios();
