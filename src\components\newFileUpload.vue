<template>
  <div class="file-uploader">
    <van-uploader
      v-model="internalFileList"
      :after-read="handleUpload"
      :before-read="beforeRead"
      :max-count="maxCount"
      :deletable="editable"
      :multiple="multiple"
      @delete="handleDelete"
      class="file-list"
      :accept="acceptTypes"
    >
      <!-- 自定义文件项 -->
      <!-- <template #preview-cover="{ index }">
        <div class="file-item" @click.stop="handlePreview(index)">
          <div class="file-info">
            <van-icon :name="getFileIcon(internalFileList[index]?.url || '')" class="file-icon" />
            <span class="file-name">{{ getFileName(internalFileList[index]?.url || '') }}</span>
          </div>
          <div class="file-actions">
            <van-icon name="eye-o" class="preview-icon" />
            <van-icon v-if="editable" name="delete-o" class="delete-icon" @click.stop="handleDelete(internalFileList[index])" />
          </div>
        </div>
      </template> -->
      
      <!-- 自定义上传按钮 -->
      <template #default>
        <div class="upload-button" v-if="editable">
          <van-button icon="plus" type="primary">上传附件</van-button>
        </div>
      </template>
    </van-uploader>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { showToast, showImagePreview } from 'vant';
import { upload } from '@/api/equipment';
import type { UploaderAfterRead, UploaderFileListItem } from 'vant';

interface FileItem extends UploaderFileListItem {
  url: string;
  name: string;
  status: 'uploading' | 'done' | 'failed';
  message?: string;
  type?: string;
  size?: number;
}

const props = defineProps({
  modelValue: {
    type: Array as () => string[],
    default: () => []
  },
  maxCount: {
    type: Number,
    default: 9
  },
  editable: {
    type: Boolean,
    default: true
  },
  multiple: {
    type: Boolean,
    default: true
  },
  maxSize: {
    type: Number,
    default: 10 * 1024 * 1024 // 默认10MB
  },
  accept: {
    type: String,
    default: '.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png'
  }
});

const emit = defineEmits(['update:modelValue', 'upload-success', 'upload-error']);

// 内部管理的文件列表
const internalFileList = ref<FileItem[]>([]);

// 转换文件类型为 MIME 类型
const acceptTypes = computed(() => {
  const types = props.accept.split(',').map(ext => {
    const cleanExt = ext.replace('.', '').toLowerCase();
    switch (cleanExt) {
      case 'pdf':
        return 'application/pdf';
      case 'doc':
      case 'docx':
        return 'application/msword';
      case 'xls':
      case 'xlsx':
        return 'application/vnd.ms-excel';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      default:
        return '';
    }
  }).filter(Boolean);
  
  return types.join(',');
});

// 获取当前有效URL列表
const getCurrentUrls = () => {
  return internalFileList.value
    .filter(item => item.status === 'done')
    .map(item => item.url);
};

// 获取文件名
const getFileName = (file: string) => {
  console.log('获取文件名', file);
  
  if (!file) return '未知文件';
  return file.split('/').pop() || '未知文件';
};

// 获取文件图标
const getFileIcon = (fileName: string) => {
  if (!fileName) return 'file-o';
  const ext = fileName.split('.').pop()?.toLowerCase() || '';
  
  if (['jpg', 'jpeg', 'png'].includes(ext)) {
    return 'photo-o';
  } else if (ext === 'pdf') {
    return 'description';
  } else if (['doc', 'docx'].includes(ext)) {
    return 'notes-o';
  } else if (['xls', 'xlsx'].includes(ext)) {
    return 'chart-trending-o';
  }
  return 'file-o';
};

// 同步外部数据到内部列表
const syncFileList = (urls: string[] = []) => {
  internalFileList.value = urls.map(url => {
    const name = url.split('/').pop() || '未知文件';
    const ext = name.split('.').pop()?.toLowerCase() || '';
    let type = 'application/octet-stream';
    
    // 根据文件扩展名设置文件类型
    if (['jpg', 'jpeg', 'png'].includes(ext)) {
      type = 'image/jpeg';
    } else if (ext === 'pdf') {
      type = 'application/pdf';
    } else if (['doc', 'docx'].includes(ext)) {
      type = 'application/msword';
    } else if (['xls', 'xlsx'].includes(ext)) {
      type = 'application/vnd.ms-excel';
    }

    return {
      url,
      name,
      status: 'done' as const,
      message: '上传成功',
      type
    };
  });
  console.log('之后', internalFileList.value);
  
};

// 初始化同步数据
watch(() => props.modelValue, (newVal) => {
  if (JSON.stringify(newVal) !== JSON.stringify(getCurrentUrls())) {
    syncFileList(newVal);
  }
}, { immediate: true });

// 文件校验
const beforeRead = (file: File | File[]) => {
  const validateFile = (file: File) => {
    const ext = file.name.split('.').pop()?.toLowerCase() || '';
    const allowedExts = props.accept.split(',').map(e => e.replace('.', '').toLowerCase());
    
    if (!allowedExts.includes(ext)) {
      showToast(`请选择${props.accept}格式的文件`);
      return false;
    }
    
    if (file.size > props.maxSize) {
      showToast(`文件大小不能超过${props.maxSize / 1024 / 1024}MB`);
      return false;
    }
    
    return true;
  };

  if (Array.isArray(file)) {
    return file.every(validateFile);
  }
  return validateFile(file);
};

// 处理文件上传
const handleUpload: UploaderAfterRead = async (file) => {
  try {
    if (Array.isArray(file)) {
      for (const fileItem of file) {
        await uploadFile(fileItem as FileItem);
      }
    } else {
      await uploadFile(file as FileItem);
    }
  } catch (error) {
    console.error('Upload error:', error);
    showToast('上传失败，请重试');
    emit('upload-error', error);
  }
};

// 单个文件上传
const uploadFile = async (fileItem: FileItem) => {
  try {
    const formData = new FormData();
    if (fileItem.file) {
      formData.append('file', fileItem.file);
    }
    
    fileItem.status = 'uploading';
    fileItem.message = '上传中...';
    
    const res = await upload(formData);
    
    fileItem.status = 'done';
    fileItem.url = res.value;
    fileItem.message = '上传成功';
    
    emitUpdate();
    emit('upload-success', res.value);
  } catch (error) {
    fileItem.status = 'failed';
    fileItem.message = '上传失败';
    throw error;
  }
};

// 删除文件
const handleDelete = (fileItem: FileItem) => {
  internalFileList.value = internalFileList.value.filter(item => item !== fileItem);
  emitUpdate();
};

// 文件预览
const handlePreview = (index: number) => {
  console.log('查看');
  
  const file = internalFileList.value[index];
  if (!file) {
    showToast('文件不存在');
    return;
  }

  // 获取文件扩展名
  const getFileExt = (url: string) => {
    if (!url) return '';
    return url.split('.').pop()?.toLowerCase() || '';
  };

  const ext = getFileExt(file.url || '');
  
  if (['jpg', 'jpeg', 'png'].includes(ext)) {
    // 图片预览
    const previewList = internalFileList.value
      .filter(item => item.status === 'done' && item.url)
      .map(item => item.url);
    
    if (previewList.length === 0) {
      showToast('没有可预览的图片');
      return;
    }

    showImagePreview({
      images: previewList,
      startPosition: index
    });
  } else if (['pdf', 'doc', 'docx', 'xls', 'xlsx'].includes(ext)) {
    // 文档预览
    if (!file.url) {
      showToast('文件地址不存在');
      return;
    }
    window.open(file.url, '_blank');
  } else {
    showToast('不支持预览该类型文件');
  }
};

// 触发数据更新
const emitUpdate = () => {
  const urls = getCurrentUrls();
  emit('update:modelValue', urls);
};
</script>

<style scoped>
.file-uploader {
  width: 100%;
}

.file-list {
  width: 100%;
}
:deep(.van-uploader__file) {
  width: 100% !important;
}
:deep(.van-uploader__wrapper) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.van-uploader__preview) {
  width: 100% !important;
  height: auto !important;
  margin: 0 !important;
}

:deep(.van-uploader__preview-image) {
  width: 100% !important;
  height: auto !important;
  margin: 0 !important;
  border-radius: 8px;
  overflow: hidden;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: #f7f8fa;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.file-item:hover {
  background: #f2f3f5;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.file-icon {
  font-size: 20px;
  color: #646566;
}

.file-name {
  font-size: 14px;
  color: #323233;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-icon,
.delete-icon {
  font-size: 18px;
  color: #969799;
  cursor: pointer;
  transition: color 0.3s;
}

.preview-icon:hover {
  color: #1989fa;
}

.delete-icon:hover {
  color: #ee0a24;
}

.upload-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  /* padding: 6px; */
  background: var(--van-button-primary-background);
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

/* .upload-button:hover {
  background: #f2f3f5;
} */

.upload-button .van-icon {
  font-size: 16px;
  color: #969799;
}

.upload-button span {
  font-size: 14px;
  color: #969799;
}
</style>
