# 创建部门选择器组件

<template>
  <div class="form-group" :style="{marginBottom: !hideTitle ? '1rem' : ''}">
    <label v-if="!hideTitle" class="label" :id="`${id}-label`"><span class="text-red-500" aria-hidden="true">*</span>{{ title }}</label>
    <div class="flex-1">
      <div
        class="w-full px-4 py-2 border border-gray-300 rounded-lg text-base bg-white"
        @click="showUserHandler"
        role="button"
        tabindex="0"
        :aria-labelledby="`${id}-label`"
        :aria-expanded="showUser"
        @keydown.enter="showUser = true"
        @keydown.space.prevent="showUser = true"
      >
        <span v-if="modelValue">{{ displayText || userName }}</span>
        <span v-else class="text-gray-400">{{ placeholder }}</span>
      </div>
    </div>
  </div>
  <!-- 部门选择弹窗 -->
  <van-popup
    v-model:show="showUser"
    round
    position="bottom"
    :aria-labelledby="`${id}-label`"
    @closed="onPopupClosed"
  >
    <!-- <van-cascader
      v-model="cascaderValue"
      :title="title"
      :options="userOptions"
      @close="onClose"
      @finish="onFinish"
      role="dialog"
      :aria-label="title"
    /> -->
    <van-picker
      title="人员"
      :columns="userOptions"
      :columnsFieldNames="{text: 'name', value: 'userId'}"
      @confirm="onFinish"
      @cancel="onClose"
    />
  </van-popup>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { hpWxworkUser } from '@/api/equipment'
import { showToast } from 'vant'

// 生成唯一ID
const id = `user-picker-${Math.random().toString(36).substr(2, 9)}`

// 定义部门数据类型
interface HpWxworkDepartment {
  id: string
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  updatedBy?: string
  remark?: string
  departmentId: number
  name: string
  parentId: number
  children?: HpWxworkDepartment[]
  wxworkOrder: number
  hasChildren?: boolean
}


// 自定义字段名类型
interface columnsFieldNames {
  text: string
  value: string
}

// 定义组件的属性
const props = withDefaults(
  defineProps<{
    modelValue?: number
    departmentId?: number
    placeholder?: string
    disabled?: boolean
    disabledText?: string
    title?: string
    hideTitle: boolean
    userName?: string
    columnsFieldNames?: columnsFieldNames
  }>(),
  {
    modelValue: undefined,
    userName: undefined,
    hideTitle: false,
    disabled: false,
    placeholder: '请选择人员',
    title: '请选择人员',
    columnsFieldNames: () => ({
      text: 'name',
      value: 'userId'
    }),
  },
)
// 定义组件的事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: string | number): void
  (e: 'finish', data: FinishData): void
  (e: 'close'): void
  (e: 'change', value: string | number): void
}>()

// 内部状态
const showUser = ref(false)
const userOptions = ref([])
const isLoading = ref(false)
const displayText = ref('')

// 处理数据加载
const loadUserData = async () => {
  if (userOptions.value.length > 0 && !props.departmentId) return

  isLoading.value = true
  try {
    let query
    if (props.departmentId) {
      query = [{ departmentId: props.departmentId.toString() }]
    }
    const params = {
      nopaging: true,
      query: JSON.stringify(query)
    }
    const res = await hpWxworkUser(params)
    // 将 API 返回的数据转换为级联选择器需要的格式
    userOptions.value = res.items
  } catch (error) {
    console.error('获取人员数据失败:', error)
    showToast('获取人员数据失败')
  } finally {
    isLoading.value = false
  }
}


// 处理弹窗关闭
const onClose = () => {
  showUser.value = false
  emit('close')
}

// 处理选择完成
const onFinish = (data) => {
displayText.value = data.selectedOptions[0].name
const finalValue = data.selectedValues[0]
  // 更新 v-model
  emit('update:modelValue', finalValue)

  // 发出 finish 事件，包含完整的选中数据
  emit('finish', {
    value: finalValue,
    displayText: displayText.value,
    selectedOptions: data.selectedOptions,
  })

  showUser.value = false
}
const showUserHandler = () => {
  if (!props.disabled) {
    showUser.value = true
  } else {
    showToast(props.disabledText)
  }
}

// 处理弹窗关闭后的清理
const onPopupClosed = () => {
  // 将焦点返回到触发器元素
  const triggerElement = document.querySelector(`[aria-labelledby="${id}-label"]`) as HTMLElement
  if (triggerElement) {
    triggerElement.focus()
  }
}

// 监听显示状态变化
watch([showUser], (newVal) => {
  if (newVal) {
    loadUserData()
  }
})
watch(() => props.departmentId, (newVal) => {
  if (newVal) {
    loadUserData()
  }
})
// 组件挂载时自动加载数据
onMounted(() => {
  loadUserData()
})
</script>

<style scoped>
.department-picker {
  width: 100%;
}
.form-group {
  /* margin-bottom: 1rem; */
}
.form-group.horizontal {
  display: flex;
  align-items: center;
}
.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #374151;
}

.form-group.horizontal .label {
  width: 7rem;
  margin-bottom: 0;
}
</style>
