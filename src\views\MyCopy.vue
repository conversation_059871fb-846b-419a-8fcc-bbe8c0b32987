<template>
  <div class="page-container bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="router.push('/apps')" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-bold">抄送我的</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 查询条件 -->
      <div class="bg-white p-4">
        <div class="flex gap-3">
          <el-select v-model="queryForm.open_sp_status" placeholder="审批状态" clearable class="flex-1">
            <el-option label="已通过" :value="2" />
            <el-option label="已驳回" :value="3" />
            <el-option label="已撤回" :value="4" />
          </el-select>
          <el-select v-model="queryForm.sp_type" placeholder="单据类型" clearable class="flex-1">
            <el-option label="预算申请单" value="预算申请" />
            <el-option label="资产申领单" value="资产申领" />
            <el-option label="调拨申请单" value="调拨申请" />
            <el-option label="报废申请单" value="报废申请" />
            <el-option label="退还入库单" value="退还入仓" />
          </el-select>
        </div>
      </div>
      <OrderList :status="queryForm.open_sp_status" :type="queryForm.sp_type" :title-type="'copy'"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { useRouter } from 'vue-router'
import OrderList from './order/OrderList.vue'

const router = useRouter()

const queryForm = reactive({
  open_sp_status: [ 2, 3, 4 ],
  sp_type: ''
})
</script>

<style scoped>
.page-container {
  padding: 0;
  min-height: 100vh;
  background-color: #f5f7fa;
  overflow: hidden;
}

.content-area {
  padding-top: 56px;
  height: 100vh;
  overflow-y: auto;
  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none;
  }
  /* Firefox */
  scrollbar-width: none;
  /* IE and Edge */
  -ms-overflow-style: none;
}

.flex {
  display: flex;
}

.gap-3 {
  gap: 0.75rem;
}

.flex-1 {
  flex: 1 1 0%;
}
</style> 