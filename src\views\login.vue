<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { postDefault } from "@/api/default"
import { getUserInfo, setUserInfo } from "@/utils/userInfo";

const route =  useRoute()
const router =  useRouter()
const query = ref(
  {
    username: '',
    password: ''
  }
)
  const onLogin = () => {
    const loginParams = {
      username: query.value.username,
      password: query.value.password,
      bindCode: route.query.bindCode,
      platform: 'workwx'
    }
    // const encryptParams = encrypt(JSON.stringify(loginParams))
      postDefault('/system/v1/sysUser/bindWxwork', {
      //  data: encryptParams
      ...loginParams
      }).then((userInfo) => {
        console.log('登录成功', userInfo);
        setUserInfo(userInfo)
        router.push('/')
      })
  }

  onMounted(() => {
  })

</script>
<template>
  <div class="w-full h-full pt-[8rem] px-4 box-border flex flex-col items-center gap-2">
    <h1 class="mb-10 text-center">绑定账号</h1>
    <van-field
      v-model="query.username"
      :rules="[{ required: true }]"
      label="用户名"
      placeholder="请输入账号或手机号"
    />
    <van-field v-model="query.password" type="password" label="密码" placeholder="请输入密码" class="mb-2"/>
    <!-- <input type="text" value={username} onChange={(e) => changeHandler(e)} placeholder="请输入账号或手机号" className="input input-bordered w-full max-w-x-start" />
    <label className="input input-bordered flex items-center gap-2 max-w-x-start w-full">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" className="w-4 h-4 opacity-70"><path fillRule="evenodd" d="M14 6a4 4 0 0 1-4.899 3.899l-1.955 1.955a.5.5 0 0 1-.353.146H5v1.5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1-.5-.5v-2.293a.5.5 0 0 1 .146-.353l3.955-3.955A4 4 0 1 1 14 6Zm-4-2a.75.75 0 0 0 0 ******* 0 0 1 .********* 0 0 0 1.5 0 2 2 0 0 0-2-2Z" clipRule="evenodd" /></svg>
        <input type="password" className="grow" value={password} placeholder="请输入密码" onChange={(e) => passwordChange(e)} />
      </label> -->
    <!-- <button className="btn btn-wide mt-10" onClick={() => onLogin()}>登录</button> -->
    <van-button type="primary" @click="onLogin" style="width: 180px;font-size: 16px;">登 录</van-button>
  </div>
</template>
<style scoped>

</style>
