<script setup lang="ts">
import { showToast } from 'vant';
import { ref } from 'vue'

interface Props {
  // 请求数据的函数，需要返回 Promise
  requestFn: (page: number, pageSize: number) => Promise<{ list: any[], total: number }>;
  // 每页数据量
  pageSize?: number;
  // 是否启用分页功能
  pagination?: boolean;
  // 是否显示加载失败的提示
  showError?: boolean;
  // 完成加载的提示文本
  finishedText?: string;
  // 加载失败的提示文本
  errorText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  pageSize: 10,
  pagination: true,
  showError: true,
  finishedText: '没有更多了',
  errorText: '请求失败，点击重试'
})
// 下拉刷新相关
const refreshing = ref(false)
// 分页相关
const page = ref(1)
const loading = ref(false)
const finished = ref(false)
const error = ref(false)
const dataList = ref<any[]>([])

// 加载数据
const onLoad = async (isRefresh = false) => {
  if (!props.requestFn) return
  try {
    loading.value = true
    error.value = false
    const { list, total } = await props.requestFn(page.value, props.pageSize)
    if (isRefresh || page.value == 1) {
      dataList.value = list
      isRefresh ? showToast('刷新成功') : null
    } else {
      dataList.value = [...dataList.value, ...list]
    }
    // 判断是否加载完成
    if (props.pagination) {
      finished.value = dataList.value.length >= total
      page.value++
    } else {
      // 无分页模式下，第一次加载后就完成
      finished.value = true
    }
  } catch (err) {
    error.value = true
    if (props.showError) {
      showToast(props.errorText)
    }
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 下拉刷新
const onRefresh = (val: boolean = true) => {
  finished.value = false
  error.value = false
  page.value = 1
  onLoad(val)
}

// 暴露一些方法和数据给父组件
defineExpose({
  refresh: onRefresh,
  dataList,
  loading,
  finished,
  error
})
</script>

<template>
  <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
    <van-list
      v-if="pagination"
      v-model:loading="loading"
      :finished="finished"
      :error="error"
      :finished-text="finishedText"
      :error-text="errorText"
      @load="onLoad"
    >
      <slot :list="dataList" />
    </van-list>
    <!-- 无分页模式 -->
    <div v-else>
      <slot :list="dataList" />
      <div v-if="loading" class="loading-container">
        <van-loading>加载中...</van-loading>
      </div>
      <div v-if="error" class="error-container" @click="() => onLoad()">
        {{ errorText }}
      </div>
    </div>
  </van-pull-refresh>
</template>

<style scoped>
.van-list {
  min-height: 100px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: #969799;
  cursor: pointer;
  user-select: none;
}

.error-container:hover {
  color: #323233;
}
</style>

<!--
使用示例：

1. 带分页的列表（默认）：
<template>
  <list-component
    :request-fn="fetchData"
    :page-size="10"
    v-slot="{ list }"
  >
    <van-cell
      v-for="item in list"
      :key="item.id"
      :title="item.title"
    />
  </list-component>
</template>

2. 不带分页的列表：
<template>
  <list-component
    :request-fn="fetchAllData"
    :pagination="false"
    v-slot="{ list }"
  >
    <van-cell
      v-for="item in list"
      :key="item.id"
      :title="item.title"
    />
  </list-component>
</template>

<script setup lang="ts">
// 分页数据请求
const fetchData = async (page: number, pageSize: number) => {
  const res = await api.getList({ page, pageSize });
  return {
    list: res.data.list,
    total: res.data.total
  };
};

// 一次性获取所有数据
const fetchAllData = async () => {
  const res = await api.getAllList();
  return {
    list: res.data.list,
    total: res.data.list.length
  };
};
</script>
-->