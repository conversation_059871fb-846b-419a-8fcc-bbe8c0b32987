<template>
  <div class="fixed inset-0 bg-gray-50">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white z-10">
      <div class="flex items-center justify-between px-4 py-3 border-b border-gray-200">
        <button @click="onClickLeft" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-bold">盘点详情</h1>
        <div class="w-8"></div>
      </div>

      <!-- Tab标签 -->
      <div class="flex bg-white">
        <button
          v-for="tab in tabs"
          :key="tab.value"
          @click="changeStatus(tab.value)"
          :class="[
            'flex-1 py-3 text-sm font-medium text-center relative',
            activeTab === tab.value
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 hover:text-gray-700 border-b border-gray-200'
          ]"
        >
          {{ tab.label }}
        </button>
      </div>
    </div>

    <!-- 装备列表 -->
    <div class="absolute top-24 bottom-0 left-0 right-0 overflow-y-auto no-scrollbar">
      <List ref="listRef" :request-fn="fetchData" v-slot="{ list }">
        <div class="px-4 pb-4 pt-6 space-y-4">
          <div 
            v-for="item in list" 
            :key="item.id"
            class="bg-white rounded-lg p-4 shadow-sm"
            @click="handleItemClick(item)"
          >
            <div class="flex items-start justify-between mb-3">
              <div class="flex-1 min-w-0 mr-4">
                <h3 class="text-base font-medium text-gray-900 truncate">{{ item.materialName }}</h3>
                <div class="flex items-center mt-4">
                  <span class="text-sm text-gray-500">资产编号：</span>
                  <span class="text-sm text-gray-900">{{ item.materialNo }}</span>
                </div>
              </div>
              <div class="flex items-center">
                <span 
                  :class="[
                    'px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap',
                    item.isAudit
                      ? (item.auditResult === '1' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800')
                      : 'bg-yellow-100 text-yellow-800'
                  ]"
                >
                  {{ 
                    item.isAudit
                      ? (item.auditResult === '1' ? '已找到' : '未找到')
                      : '未盘点' 
                  }}
                </span>
              </div>
            </div>
            
            <div class="space-y-2">
              <div class="flex items-center">
                <span class="text-sm text-gray-500 shrink-0 w-[4.5rem]">规格型号：</span>
                <span class="text-sm text-gray-900 flex-1 min-w-0">{{ item.materialModel }}</span>
              </div>
              <!-- 未找到时显示备注说明 -->
              <template v-if="item.status === 'inventoried' && item.result === 'not_found' && item.remark">
                <div class="border-t border-gray-100 my-3"></div>
                <div class="flex items-start">
                  <span class="text-sm text-gray-500 shrink-0 w-[4.5rem]">备注说明：</span>
                  <span class="text-sm text-gray-900 flex-1 min-w-0 break-words">{{ item.remark }}</span>
                </div>
              </template>
            </div>

            <!-- 操作按钮 -->
            <div 
              v-if="item.showActions && item.status === 0" 
              class="flex justify-end space-x-3 mt-4 pt-3 border-t border-gray-100"
            >
              <button 
                @click.stop="handleNotFound(item)"
                class="px-4 py-2 text-sm font-medium text-red-600 bg-red-50 rounded-lg hover:bg-red-100"
              >
                未找到
              </button>
              <button 
                @click.stop="handleFound(item)"
                class="px-4 py-2 text-sm font-medium text-white bg-primary rounded-lg hover:bg-primary-dark"
              >
                已找到
              </button>
            </div>
          </div>
        </div>
      </List>
    </div>

    <!-- 底部弹出的备注输入框 -->
    <div 
      v-if="showRemarkInput" 
      class="fixed inset-0 bg-black bg-opacity-50 z-20"
      @click="showRemarkInput = false"
    >
      <div 
        class="fixed bottom-0 left-0 right-0 bg-white rounded-t-2xl p-4 z-30 transform transition-transform duration-300"
        :class="[showRemarkInput ? 'translate-y-0' : 'translate-y-full']"
        @click.stop
      >
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium">记录未找到原因</h3>
          <button 
            @click="showRemarkInput = false"
            class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100"
          >
            <i class="fas fa-times text-gray-400"></i>
          </button>
        </div>
        
        <div class="space-y-1">
          <textarea
            v-model="remarkText"
            rows="4"
            class="w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent"
            :class="[
              showError ? 'border-red-300 focus:ring-red-200' : 'border-gray-200 focus:ring-primary'
            ]"
            placeholder="请输入未找到的具体原因..."
          ></textarea>
          <p v-if="showError" class="text-xs text-red-500">请输入未找到原因</p>
        </div>
        
        <div class="flex justify-end space-x-3 mt-4">
          <button 
            @click="showRemarkInput = false"
            class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200"
          >
            取消
          </button>
          <button 
            @click="handleConfirmNotFound"
            class="px-4 py-2 text-sm font-medium text-white bg-primary rounded-lg hover:bg-primary-dark"
          >
            确认
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { myAuditplanDetail, updatePlanDetail } from '@/api/equipment'
import List from '@/components/List.vue'
import { showConfirmDialog, showToast } from 'vant';

const listRef = ref(null)
onMounted(() => {
  // setTimeout(() => {
  //   console.log('listRef.value', listRef.value.dataList);
  // }, 3000)
  // // console.log('listRef.value', listRef.value);
})
const router = useRouter()
const route = useRoute()
interface InventoryItem {
  id: string
  name: string
  code: string
  model: string
  status: number
  result?: 'found' | 'not_found'
  remark?: string
  showActions?: boolean
}

// Tab选项
const tabs = [
  { label: '未盘点', value: 'false' },
  { label: '已盘点', value: 'true' }
]
const activeTab = ref('false')

const onClickLeft = () => {
  router.back()
}
const fetchData = async (page: number, pageSize: number) => {
  const params = {
    page,
    pageSize,
    query: JSON.stringify([{ auditPlanId: route.query.id }, { isAudit: activeTab.value }]),
    orderBy: JSON.stringify(["-updated_at", "-created_at"])
  }
  let res = await myAuditplanDetail(params)
  if (res.items && res.items.length) {
    res.items = res.items.map(item => {
      return {
        ...item,
        showActions: false
      }
    })
  }
  return {
    list: res.items,
    total: res.total
  }
}

const changeStatus = (tab: number) => {
  activeTab.value = tab
  listRef.value.refresh(true)
}
// 处理卡片点击
const handleItemClick = (item: InventoryItem) => {
  console.log('itemmmmmmmm', item);
  
  // 只有未盘点的物品才显示操作按钮
  if (!item.isAudit) {
    item.showActions = !item.showActions
  }
}

// 添加备注输入相关的状态
const showRemarkInput = ref(false)
const remarkText = ref('')
const currentItem = ref<InventoryItem | null>(null)
const showError = ref(false)

// 修改处理未找到按钮点击
const handleNotFound = (item: InventoryItem) => {
  currentItem.value = item
  remarkText.value = ''
  showError.value = false
  showRemarkInput.value = true
}

// 添加确认未找到的处理函数
const handleConfirmNotFound = () => {
  if (!currentItem.value) return
  
  if (!remarkText.value.trim()) {
    showError.value = true
    return
  }
  let data = {
    ...currentItem.value,
    remark: remarkText.value.trim(),
    auditResult: '0',
    isAudit: true
  }
  updatePlanDetail(data).then(res => {
    showRemarkInput.value = false
    showError.value = false
    currentItem.value = null
    remarkText.value = ''
    showToast('操作成功')
    listRef.value.refresh(true)
  })
}

// 处理已找到按钮点击
const handleFound = (item: InventoryItem) => {
  showConfirmDialog({
    title: '确认操作',
    message: '确认该操作吗？',
    confirmButtonColor: '#ef4444'
  })
    .then(() => {
      updatePlanDetail({ ...item, auditResult: '1', isAudit: true }).then(res => {
        showRemarkInput.value = false
        // key = new Date()
        // fetchData(1,10)
        showToast('操作成功')
        listRef.value ? listRef.value.refresh(true) : null
      })
    })
    .catch(() => {
      // on cancel
    });
}
</script>

<style scoped>
.page-container {
  padding-bottom: env(safe-area-inset-bottom);
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}
</style> 