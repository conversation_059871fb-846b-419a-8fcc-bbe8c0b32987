<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import DepartmentPicker from '@/components/DepartmentPicker.vue'
import { addRepairRecord, editRepairRecord, delRepairRecord } from '@/api/equipment'
import { ElMessage, ElMessageBox } from 'element-plus'
import { showToast, showConfirmDialog } from 'vant'
import ImageUpload from '@/components/ImageUpload.vue'
import NewFileUpload from '@/components/newFileUpload.vue'
import 'vant/lib/index.css'

const router = useRouter()
const route = useRoute()

// 判断是否为编辑模式
const isEditMode = computed(() => route.query.mode === 'edit')
console.log('isEditMode');

// 判断是否为查看模式
const isViewMode = computed(() => route.query.mode === 'view')

// 文件上传配置
const FILE_SIZE_LIMIT = 10 * 1024 * 1024; // 10MB
const ALLOWED_FILE_TYPES = {
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
};

const ALLOWED_IMAGE_TYPES = {
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png']
};

interface Attachment {
  name: string;
  url: string;
  size: number;
  type: string;
  progress?: number;
  status: 'uploading' | 'success' | 'error';
  errorMessage?: string;
}

// 表单数据
const formData = ref({
  materialName: '',
  departmentId: 0,
  materialAddress: '',
  problemDescription: '',
  files: [],
  images: []
})

// 初始化表单数据
const initFormData = () => {
  if ((isEditMode.value || isViewMode.value) && route.query.data) {
    const recordData = JSON.parse(route.query.data)
    formData.value = { ...recordData }
    formData.value.files = formData.value.files ? formData.value.files.split(',') : []
    formData.value.images = formData.value.images ? formData.value.images.split(',') : []
    console.log('编辑数据', formData.value);
  }
}

// 在组件挂载时初始化数据
onMounted(() => {
  initFormData()
})

// 返回上一页
const goBack = () => {
  router.back()
}
const handleDepartmentFinish = () => {}
// 提示框状态
const showSuccessModal = ref(false)


// 删除记录
const handleDelete = () => {
  showConfirmDialog({
    title: '确认删除',
    message: '确认要删除这条记录吗？',
    // confirm-button-text: '确定'
    confirmButtonColor: '#ef4444'
  })
    .then(() => {
      delRepairRecord(formData.value.id).then(res => {
        console.log('ressssss', res);
        showToast('删除成功')
        router.back()
      })
    })
    .catch(() => {
      // on cancel
    });
}
const toText = (data) => {
  let arr:string[] = []
  if (data && data.length) {
    data.map(item => {
      if (item.status === 'success') {
        arr.push(item.url)
      }
    })
  }
  return arr.join(',')
}
// 提交表单
const submitForm = () => {
  // 表单验证
  if (!formData.value.materialName) {
    ElMessage.error('请输入资产名称')
    return
  }
  if (!formData.value.departmentId) {
    ElMessage.error('请选择所属部门')
    return
  }
  if (!formData.value.materialAddress) {
    ElMessage.error('请输入位置信息')
    return
  }
  if (!formData.value.problemDescription) {
    ElMessage.error('请描述问题')
    return
  }
  const data = {
    ...formData.value,
    images: formData.value.images && formData.value.images ? formData.value.images.join(',') : '',
    files: formData.value.files && formData.value.files ? formData.value.files.join(',') : ''
  }
  // TODO: 这里添加提交到后端的逻辑
  console.log('提交的表单数据：', data)
  let api = isEditMode.value ? editRepairRecord : addRepairRecord
  api(data).then(res => {
    console.log('resss', res);
    showToast('提交成功')
    router.back()
  })
  // // 显示成功提示
  // showSuccessModal.value = true
  
  // // 3秒后自动返回
  // setTimeout(() => {
  //   showSuccessModal.value = false
  //   router.back()
  // }, 3000)
}
</script>

<template>
  <div class="">
    <div class="page-container min-h-screen no-scrollbar">
      <!-- 顶部导航 -->
      <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
        <div class="flex items-center justify-between px-4 py-3">
          <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
            <i class="fas fa-arrow-left text-gray-500"></i>
          </button>
          <h1 class="text-lg font-bold">{{ isViewMode ? '维修详情' : (isEditMode ? '编辑维修' : '维修登记') }}</h1>
          <button 
            v-if="isEditMode"
            @click="handleDelete" 
            class="w-8 h-8 rounded-full text-gray-400 hover:text-red-500 hover:bg-red-50 transition-all duration-300 flex items-center justify-center"
          >
            <i class="fas fa-trash-alt"></i>
          </button>
          <div v-else class="w-8"></div>
        </div>
      </div>

      <!-- 表单内容 -->
      <div class="h-full pt-14 pb-24 px-2 overflow-y-auto no-scrollbar">
        <div :class="['bg-white p-4 space-y-6 mb-4 rounded-lg', { 'view-mode': isViewMode }]">
          <!-- 资产名称 -->
          <div class="form-group">
            <label class="label"><span class="text-red-500" v-if="!isViewMode">*</span>资产名称</label>
            <input
              v-model="formData.materialName"
              type="text"
              class="input"
              :disabled="isViewMode"
              placeholder="请输入资产名称"
            >
          </div>

          <!-- 所属部门 -->
          <!-- <div class="form-group horizontal"> -->
            <!-- <label class="label"><span class="text-red-500" v-if="!isViewMode">*</span>所属部门</label> -->
            <DepartmentPicker v-if="!isViewMode" v-model="formData.departmentId" :department-name="formData.departmentName" title="所属部门" @finish="handleDepartmentFinish" />
            <div v-else class="form-group">
              <label class="label">所属部门</label>
                <input
                  v-model="formData.departmentName"
                  type="text"
                  class="input"
                  :disabled="isViewMode"
              >
            </div>
          <!-- </div> -->

          <!-- 位置信息 -->
          <div class="form-group">
            <label class="label"><span class="text-red-500" v-if="!isViewMode">*</span>位置信息</label>
            <input
              v-model="formData.materialAddress"
              type="text"
              class="input"
              :disabled="isViewMode"
              placeholder="请输入位置信息（如：3楼办公区）"
            >
          </div>

          <!-- 问题描述 -->
          <div class="form-group">
            <label class="label"><span class="text-red-500" v-if="!isViewMode">*</span>问题描述</label>
            <textarea
              v-model="formData.problemDescription"
              class="input min-h-[100px]"
              :disabled="isViewMode"
              placeholder="请详细描述问题"
            ></textarea>
          </div>

          <!-- 图片上传 -->
          <div class="form-group">
            <div class="">
              <label class="label">图片</label>
              <ImageUpload v-model="formData.images" :editable="!isViewMode"/>
            </div>
          </div>
          <!-- 附件上传 -->
          <div class="form-group">
            <label class="label">附件</label>
            <NewFileUpload v-model="formData.files" :editable="!isViewMode"/>
            <p v-if="isViewMode && !formData.files.length">暂无附件</p>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div v-if="!isViewMode" class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-3">
        <button
          @click="submitForm"
          class="w-full py-2 px-4 bg-primary text-white rounded-lg"
        >
          {{ isEditMode ? '保存修改' : '提交申请' }}
        </button>
      </div>

      <!-- 成功提示弹窗 -->
      <div
        v-if="showSuccessModal"
        class="fixed inset-0 flex items-center justify-center z-50"
      >
        <div class="bg-black bg-opacity-50 absolute inset-0"></div>
        <div class="bg-white rounded-lg p-6 relative z-10 w-80">
          <div class="flex flex-col items-center">
            <div class="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
              <i class="fas fa-check text-2xl text-green-500"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">提交成功</h3>
            <p class="text-sm text-gray-500">维修申请已提交，请等待处理</p>
          </div>
        </div>
      </div>

      <!-- 确认删除对话框 -->
      <!-- <div v-if="showConfirmDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-2xl w-[80%] max-w-sm p-6">
          <h3 class="text-lg font-medium text-center mb-4">确认删除</h3>
          <p class="text-gray-600 text-center mb-6">
            确定要删除这条维修记录吗？
          </p>
          <div class="flex space-x-3">
            <button 
              @click="cancelDelete"
              class="flex-1 py-2.5 rounded-full border border-gray-200 text-gray-600"
            >
              取消
            </button>
            <button 
              @click="confirmDelete"
              class="flex-1 py-2.5 rounded-full bg-red-500 text-white"
            >
              删除
            </button>
          </div>
        </div>
      </div> -->
    </div>
  </div>
</template>

<style scoped>
.page-container {
  /* padding-bottom: env(safe-area-inset-bottom);
  height: 100vh;
  overflow: hidden;
  position: relative; */
}

.form-group {
  @apply space-y-2;
}

.form-group.horizontal {
  @apply flex items-center space-y-0;
}

.form-group.horizontal .label {
  @apply w-[4.5rem] shrink-0 mr-3;
}

.form-group.horizontal .input {
  @apply flex-1;
}

.label {
  @apply text-sm text-gray-700 font-medium;
}

.input {
  @apply w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:ring-opacity-50 focus:border-primary transition-colors duration-200;
}

.input:disabled {
  @apply bg-gray-50 border-gray-200 text-gray-700;
}

.input::placeholder {
  @apply text-gray-400;
}

.view-mode {
  @apply bg-white;
}

.view-mode .form-group {
  @apply border-b border-gray-100 pb-4;
}

.view-mode .form-group.horizontal {
  @apply items-center;
}

.view-mode .label {
  @apply text-gray-500 mb-0;
}

.view-mode .input {
  @apply border-none px-0 py-1 text-gray-900 bg-transparent;
}

.view-mode .input:disabled {
  @apply bg-transparent;
}

.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

textarea {
  resize: none;
}

.mobile-select {
  @apply w-full px-4 py-2 border border-gray-300 rounded-lg text-base;
  @apply bg-white text-gray-900;
  @apply focus:ring-2 focus:ring-primary focus:ring-opacity-50 focus:border-primary;
  @apply transition-colors duration-200;
  -webkit-appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236B7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
}

.mobile-select:disabled {
  @apply bg-gray-50 border-gray-200 text-gray-700;
}

/* 移动端点击时的高亮样式 */
.mobile-select:active {
  @apply bg-gray-50;
}

/* iOS设备上的样式优化 */
@supports (-webkit-touch-callout: none) {
  .mobile-select {
    font-size: 16px; /* 防止iOS自动缩放 */
  }
}

/* Vant组件样式覆盖 */
:deep(.van-cascader__header) {
  @apply text-gray-900;
}

:deep(.van-cascader__option) {
  @apply text-gray-700;
}

:deep(.van-cascader__option--selected) {
  @apply text-primary;
}

:deep(.van-popup) {
  @apply max-h-[80vh];
}
</style> 