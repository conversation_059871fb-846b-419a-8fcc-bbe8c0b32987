<template>
  <!-- 组件模板内容... -->
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessageBox, ElMessage } from 'element-plus'
import type { Router, RouteLocationNormalizedLoaded } from 'vue-router'
import AcceptanceSignature from './order/AcceptanceSignature.vue'
import { useAcceptanceStore } from '@/stores/acceptance'

const router = useRouter()
const route = useRoute()
const store = useAcceptanceStore()
const formRef = ref<FormInstance>()
const submitting = ref(false)
const isEdit = ref(false)
const isView = ref(false)
const isCompleted = ref(false)

// 表单数据
interface FormItem {
  name: string
  quantity: number
  unit: string
  assetType: 'fixed' | 'intangible'  // 资产类型
  brand?: string                     // 品牌
  model?: string                     // 规格型号
  unitPrice: number                  // 单价
  totalAmount: number                // 总金额
  acquisitionMethod: string          // 取得方式
  managementDepartment: string       // 管理/使用部门
  manager: string                    // 管理/使用人
  location?: string                  // 存放地点
  photos: string[]                  // 照片
}

interface AcceptanceRecord {
  time: string
  type: 'operator' | 'acceptor'
  name: string
  signature: string
  description: string
}

interface FormData {
  items: FormItem[]
  theme: string
  acceptanceRecords: AcceptanceRecord[]
}

const formData = reactive<FormData>({
  items: [],
  theme: '',
  acceptanceRecords: []
})

// 表单验证规则
const formRules = {
  theme: [{ required: true, message: '请输入主题', trigger: 'blur' }]
}

const itemRules = {
  name: [{ required: true, message: '请输入物品名称', trigger: 'blur' }],
  assetType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
  brand: [{ required: true, message: '请输入品牌', trigger: 'blur' }],
  model: [{ required: true, message: '请输入规格型号', trigger: 'blur' }],
  unitPrice: [{ required: true, message: '请输入单价', trigger: 'blur' }],
  quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
  acquisitionMethod: [{ required: true, message: '请选择取得方式', trigger: 'change' }],
  managementDepartment: [{ required: true, message: '请选择管理/使用部门', trigger: 'change' }],
  manager: [{ required: true, message: '请输入管理/使用人', trigger: 'blur' }],
  location: [{ required: true, message: '请输入存放地点', trigger: 'blur' }]
}

// 监听store中的物品列表变化
watch(() => store.items, (newItems) => {
  console.log('Store物品列表更新:', newItems)
  formData.items = [...newItems]
})

// 删除物品
const removeItem = (index: number) => {
  ElMessageBox.confirm(
    '确定要删除该物品吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    store.removeItem(index)
    formData.items = store.items
    ElMessage.success('删除成功')
  }).catch(() => {})
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    // TODO: 调用API保存数据
    console.log('提交的数据:', formData)
    setTimeout(() => {
      submitting.value = false
      ElMessage.success('保存成功')
      router.push('/acceptance-records')
    }, 1000)
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否完整')
  }
}

// 删除记录
const removeRecord = (index: number) => {
  ElMessageBox.confirm(
    '确定要删除该验收记录吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    formData.acceptanceRecords.splice(index, 1)
    ElMessage.success('删除成功')
  }).catch(() => {})
}

// 初始化编辑数据
const initEditData = (id: string) => {
  // TODO: 调用API获取详情数据
  console.log('获取编辑数据:', id)
  // 模拟数据
  const editData = {
    theme: '办公设备及无形资产验收（2024年第一批）',
    items: [
      {
        name: '办公椅',
        quantity: 5,
        unit: '把',
        assetType: 'fixed',
        brand: '人体工学',
        model: 'ErgoChair-2024',
        unitPrice: 1200,
        totalAmount: 6000,
        acquisitionMethod: '采购',
        managementDepartment: '行政部',
        manager: '张三',
        location: '办公区A区',
        photos: []
      },
      {
        name: '打印机',
        quantity: 2,
        unit: '台',
        assetType: 'fixed',
        brand: '惠普',
        model: 'HP LaserJet Pro',
        unitPrice: 3500,
        totalAmount: 7000,
        acquisitionMethod: '采购',
        managementDepartment: 'IT部',
        manager: '李四',
        location: '打印室',
        photos: []
      },
      {
        name: '软件著作权',
        quantity: 1,
        unit: '项',
        assetType: 'intangible',
        unitPrice: 50000,
        totalAmount: 50000,
        acquisitionMethod: '自主研发',
        managementDepartment: '研发部',
        manager: '王五',
        photos: []
      }
    ],
    acceptanceRecords: [
      {
        time: '2024-03-15 14:30',
        type: 'operator',
        name: '张三',
        signature: '',
        description: '已完成物品清点和信息核对'
      },
      {
        time: '2024-03-15 15:00',
        type: 'acceptor',
        name: '李四',
        signature: '',
        description: '物品外观完好，功能正常'
      }
    ]
  }
  
  Object.assign(formData, editData)
}

// 计算页面标题
const getPageTitle = computed(() => {
  if (isView.value) return '验收登记详情'
  return isEdit.value ? '编辑验收登记' : '新增验收登记'
})

// 删除记录
const handleDelete = () => {
  ElMessageBox.confirm(
    '确定要删除该验收登记记录吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // TODO: 调用API删除数据
    console.log('删除记录:', route.query.id)
    ElMessage.success('删除成功')
    router.back()
  }).catch(() => {})
}

// 处理照片上传
const handlePhotoChange = (file: { raw: File; name: string; size: number }, index: number) => {
  // TODO: 实现照片上传逻辑
  console.log('照片上传:', file, index)
}

// 计算总金额
const totalAmount = computed(() => {
  return formData.items.reduce((sum, item) => {
    return sum + item.totalAmount
  }, 0)
})

// 计算单个物品总金额
const calculateTotalAmount = (item: FormItem) => {
  item.totalAmount = item.unitPrice * item.quantity
}

// 监听单价和数量变化
watch(() => formData.items, (items) => {
  items.forEach(item => {
    calculateTotalAmount(item)
  })
}, { deep: true })

// 签名验收相关
const showSignatureDialog = ref(false)
const signatureRef = ref()

const openSignatureDialog = () => {
  showSignatureDialog.value = true
  // 确保组件已经挂载后再调用重置方法
  nextTick(() => {
    signatureRef.value?.reset()
  })
}

const handleSignatureConfirm = (data: { type: string; description: string; signature: string; time: string }) => {
  formData.acceptanceRecords.push({
    type: data.type as 'operator' | 'acceptor',
    time: data.time,
    name: '张三', // 这里应该是从用户信息中获取的当前用户姓名
    signature: data.signature,
    description: data.description
  })
  showSignatureDialog.value = false
  ElMessage.success('验收签字完成')
}

const handleSignatureDialogClose = () => {
  showSignatureDialog.value = false
}

// 添加物品
const handleAddItem = () => {
  router.push('/acceptance-item')
}

// 返回列表页
const handleBack = () => {
  router.push('/acceptance-records')
}

// 处理验收完成
const handleComplete = () => {
  ElMessageBox.confirm(
    '确定要完成验收吗？完成后将无法修改。',
    '验收确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    isCompleted.value = true
    ElMessage.success('验收已完成')
  }).catch(() => {})
}

// 处理下载图片
const handleDownload = () => {
  // TODO: 实现下载图片逻辑
  ElMessage.success('开始下载验收单图片')
}

onMounted(() => {
  const id = route.query.id as string
  const view = route.query.view === 'true'
  
  if (id) {
    isEdit.value = !view
    isView.value = view
    // 如果是查看模式，自动设置为只读状态
    if (view) {
      isCompleted.value = true
    }
    initEditData(id)
  } else {
    // 从store加载物品列表
    formData.items = [...store.items]
  }
  
  // 打印当前items状态
  console.log('当前物品列表:', formData.items, '从store获取:', store.items)
})
</script>

<style scoped>
/* 样式内容... */
</style> 