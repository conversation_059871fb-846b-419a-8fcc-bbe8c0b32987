<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 内容区域的引用
const descriptionRef = ref<HTMLElement | null>(null)
const specsRef = ref<HTMLElement | null>(null)
const scrollContainerRef = ref<HTMLElement | null>(null)

// 监听滚动事件
const handleScroll = () => {
  if (!scrollContainerRef.value || !descriptionRef.value || !specsRef.value || isScrolling) return
  
  const scrollTop = scrollContainerRef.value.scrollTop
  const headerHeight = 56
  const tabHeight = 44
  const offset = headerHeight + tabHeight
  
  const descriptionTop = descriptionRef.value.offsetTop - offset
  const specsTop = specsRef.value.offsetTop - offset
  
  // 添加一个缓冲区，避免在边界处抖动
  const buffer = 10
  
  // 只在非手动切换时更新activeTab
  if (!isManualScrolling) {
    if (scrollTop >= specsTop - buffer) {
      activeTab.value = 'specs'
    } else if (scrollTop >= descriptionTop - buffer) {
      activeTab.value = 'description'
    }
  }
}

let isManualScrolling = false
let manualScrollTimeout: number | null = null

// 点击标签时滚动到对应位置
const scrollToSection = (section: 'description' | 'specs') => {
  const targetRef = section === 'description' ? descriptionRef.value : specsRef.value
  if (!targetRef || !scrollContainerRef.value) return
  
  // 立即更新激活状态
  activeTab.value = section
  isScrolling = true
  isManualScrolling = true
  
  // 计算滚动位置
  const headerHeight = 56
  const tabHeight = 44
  const offset = headerHeight + tabHeight
  
  // 直接设置滚动位置
  const targetPosition = targetRef.offsetTop - offset
  scrollContainerRef.value.scrollTop = targetPosition
  
  // 清除之前的定时器
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
  if (manualScrollTimeout) {
    clearTimeout(manualScrollTimeout)
  }
  
  // 设置滚动锁定的超时
  scrollTimeout = setTimeout(() => {
    isScrolling = false
  }, 100)
  
  // 设置手动滚动标记的超时
  manualScrollTimeout = setTimeout(() => {
    isManualScrolling = false
  }, 500)
}

onMounted(() => {
  if (scrollContainerRef.value) {
    scrollContainerRef.value.addEventListener('scroll', handleScroll)
  }
})

onUnmounted(() => {
  if (scrollContainerRef.value) {
    scrollContainerRef.value.removeEventListener('scroll', handleScroll)
  }
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
  if (manualScrollTimeout) {
    clearTimeout(manualScrollTimeout)
  }
})

// 获取商品ID
const productId = computed(() => Number(route.params.id))

// 商品数据（实际应用中应该从API获取）
const product = ref({
  id: productId.value,
  title: '智能手环',
  description: '小米（MI）手环9Pro黑色 智能手环 高精度运动健康 睡眠呼吸暂停监测 心率血氧监测运动手环',
  price: '¥299',
  originalPrice: '¥399',
  points: '或2990积分',
  sales: 1280,
  stock: 86,
  rating: 4.8,
  reviewCount: 356,
  images: [
    'https://img.freepik.com/free-photo/smart-watch-dark-surface_23-2148766897.jpg',
    'https://img.freepik.com/free-photo/person-wearing-smart-watch_23-2150316338.jpg',
    'https://img.freepik.com/free-psd/realistic-smart-watch-mockup_165789-534.jpg'
  ],
  features: [
    '24小时心率监测',
    '睡眠质量分析',
    'IP68防水',
    '运动模式识别',
    '来电提醒',
    '长达7天续航'
  ],
  specs: [
    { name: '屏幕', value: '1.1英寸AMOLED' },
    { name: '分辨率', value: '126 x 294' },
    { name: '亮度', value: '最高450尼特' },
    { name: '触控', value: '全面屏触控' },
    { name: '电池容量', value: '180mAh' },
    { name: '续航时间', value: '标准使用7天' },
    { name: '充电时间', value: '约2小时' },
    { name: '防水等级', value: 'IP68' },
    { name: '连接方式', value: '蓝牙5.0' },
    { name: '蓝牙范围', value: '10米' },
    { name: '重量', value: '22.1g（不含表带）' },
    { name: '表带材质', value: '肤感亲肤硅胶' },
    { name: '表带长度', value: '155-219mm可调' },
    { name: '表盘尺寸', value: '42.3 x 36.2 x 9.9mm' },
    { name: '兼容系统', value: 'iOS 9.0+/Android 5.0+' },
    { name: '传感器', value: '心率、血氧、加速度、陀螺仪' },
    { name: '运动模式', value: '支持30+种运动模式' },
    { name: '定位功能', value: 'GPS + 北斗' },
    { name: '包装清单', value: '手环主体×1、充电线×1、说明书×1' },
    { name: '产地', value: '中国大陆' }
  ],
  reviews: [
    {
      id: 1,
      user: '李**',
      avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
      rating: 5,
      content: '非常好用，电池续航确实如宣传的那样可以用一周，防水性能也很好。',
      date: '2023-03-15'
    },
    {
      id: 2,
      user: '张**',
      avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
      rating: 4,
      content: '整体不错，就是充电有点慢，但是续航确实很长。',
      date: '2023-03-10'
    }
  ],
  options: {
    colors: [
      { id: 1, name: '曜石黑', code: '#000000', image: 'https://img.freepik.com/free-photo/smart-watch-dark-surface_23-2148766897.jpg' },
      { id: 2, name: '月光银', code: '#C0C0C0', image: 'https://img.freepik.com/free-psd/realistic-smart-watch-mockup_165789-534.jpg' },
      { id: 3, name: '星空蓝', code: '#1E90FF', image: 'https://img.freepik.com/free-photo/person-wearing-smart-watch_23-2150316338.jpg' }
    ],
    sizes: [
      { id: 1, name: '标准版', desc: '适合手腕周长：155-219mm' },
      { id: 2, name: '加长版', desc: '适合手腕周长：165-229mm' }
    ]
  }
})

// 当前选中的图片索引
const currentImageIndex = ref(0)

// 当前选中的标签
const activeTab = ref('description')
let isScrolling = false
let scrollTimeout: number | null = null

// 数量
const quantity = ref(1)

// 增加数量
const increaseQuantity = () => {
  if (quantity.value < product.value.stock) {
    quantity.value++
  }
}

// 减少数量
const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}

// 返回上一页
const goBack = () => {
  const fromPage = route.query.from as string
  if (fromPage === 'mall') {
    router.push('/mall')
  } else if (fromPage === 'cart') {
    router.push('/cart')
  } else {
    router.back()
  }
}

// 底部弹出层状态
const isSkuDrawerVisible = ref(false)
const isAddSuccess = ref(false)

// 选中的商品选项
const selectedOptions = ref({
  color: null as number | null,
  size: null as number | null
})

// 检查是否已选择所有必要选项
const isOptionsSelected = computed(() => {
  return selectedOptions.value.color !== null && selectedOptions.value.size !== null
})

// 选择商品选项
const selectOption = (type: 'color' | 'size', id: number) => {
  selectedOptions.value[type] = id
}

// 打开商品选择弹出层
const openSkuDrawer = () => {
  isSkuDrawerVisible.value = true
}

// 关闭商品选择弹出层
const closeSkuDrawer = () => {
  isSkuDrawerVisible.value = false
}

// 修改添加到购物车方法
const addToCart = () => {
  if (!isOptionsSelected.value) {
    openSkuDrawer()
    return
  }
  // 实际应用中应该调用API
  isAddSuccess.value = true
  
  // 延迟关闭弹出层和重置状态
  setTimeout(() => {
    isAddSuccess.value = false
    isSkuDrawerVisible.value = false
    // 重置选项
    selectedOptions.value.color = null
    selectedOptions.value.size = null
    quantity.value = 1
  }, 3000)
}

// 立即购买
const buyNow = () => {
  // 实际应用中应该跳转到结算页面
  alert(`正在购买 ${quantity.value} 个 ${product.value.title}`)
}

// 在 script setup 中添加新的方法
const handleSwipe = (direction: 'left' | 'right') => {
  if (direction === 'left' && currentImageIndex.value < product.value.images.length - 1) {
    currentImageIndex.value++
  } else if (direction === 'right' && currentImageIndex.value > 0) {
    currentImageIndex.value--
  }
}

// 触摸相关变量和方法
const touchStart = ref(0)
const touchEnd = ref(0)

const handleTouchStart = (e: TouchEvent) => {
  touchStart.value = e.touches[0].clientX
}

const handleTouchMove = (e: TouchEvent) => {
  touchEnd.value = e.touches[0].clientX
}

const handleTouchEnd = () => {
  const swipeThreshold = 50 // 滑动阈值
  const diff = touchEnd.value - touchStart.value
  
  if (Math.abs(diff) > swipeThreshold) {
    if (diff > 0) {
      handleSwipe('right')
    } else {
      handleSwipe('left')
    }
  }
}

// 顶部提示条
const showToast = ref(false)
const toastMessage = ref('')
</script>

<template>
  <div class="product-page">
    <!-- 顶部导航 -->
    <div class="fixed-header">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-bold flex-1 text-center text-gray-900">{{ product.title }}</h1>
        <div class="w-8"></div>
      </div>
    </div>
    
    <!-- 可滚动的内容区域 -->
    <div class="scrollable-content no-scrollbar" ref="scrollContainerRef">
      <!-- 商品图片轮播 -->
      <div class="relative mb-6">
        <div 
          class="rounded-xl overflow-hidden bg-gray-100 relative"
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
        >
          <img 
            :src="product.images[currentImageIndex]" 
            :alt="product.title" 
            class="w-full h-64 object-contain"
            @error="(e: Event) => {
              const img = e.target as HTMLImageElement;
              img.src = 'https://via.placeholder.com/400x400?text=商品图片';
            }"
          >
        </div>
        
        <!-- 轮播指示器 -->
        <div class="absolute bottom-2 right-2 flex space-x-1">
          <div 
            v-for="(_, index) in product.images" 
            :key="index"
            class="w-2 h-2 rounded-full bg-white"
            :class="{ 'opacity-100': currentImageIndex === index, 'opacity-50': currentImageIndex !== index }"
            @click="currentImageIndex = index"
          ></div>
        </div>
      </div>
      
      <!-- 缩略图 -->
      <div class="flex space-x-2 mb-6 overflow-x-auto">
        <div 
          v-for="(image, index) in product.images" 
          :key="index"
          class="w-16 h-16 rounded-lg overflow-hidden cursor-pointer bg-gray-100"
          :class="{ 'ring-2 ring-primary': currentImageIndex === index }"
          @click="currentImageIndex = index"
        >
          <img 
            :src="image" 
            :alt="`${product.title} ${index + 1}`" 
            class="w-full h-full object-contain"
            @error="(e: Event) => {
              const img = e.target as HTMLImageElement;
              img.src = 'https://via.placeholder.com/100x100?text=缩略图';
            }"
          >
        </div>
      </div>
      
      <!-- 商品信息 -->
      <div class="card mb-6">
        <div class="flex items-center justify-between mb-2">
          <h2 class="text-xl font-bold">{{ product.title }}</h2>
          <span class="text-sm text-gray-500">库存: {{ product.stock }}件</span>
        </div>
        <p class="text-gray-600">{{ product.description }}</p>
      </div>
      
      <!-- 标签页 -->
      <div class="tab-container">
        <div class="tab-wrapper">
          <div 
            class="tab-button"
            :class="{ active: activeTab === 'description' }"
            @click="scrollToSection('description')"
          >
            商品详情
            <div class="tab-line" v-show="activeTab === 'description'"></div>
          </div>
          <div 
            class="tab-button"
            :class="{ active: activeTab === 'specs' }"
            @click="scrollToSection('specs')"
          >
            规格参数
            <div class="tab-line" v-show="activeTab === 'specs'"></div>
          </div>
        </div>
      </div>
      
      <!-- 商品详情 -->
      <div ref="descriptionRef" class="py-4">
        <div class="space-y-6 px-4">
          <!-- 产品介绍 -->
          <div>
            <h3 class="font-bold mb-3 text-lg">产品介绍</h3>
            <p class="text-gray-600 leading-relaxed">
              小米手环9Pro是一款集健康监测、运动追踪和智能提醒于一体的高端智能手环。采用1.1英寸高清AMOLED彩色触摸屏，显示效果清晰明亮。全新升级的健康监测系统，提供24小时心率监测、血氧饱和度检测和睡眠质量分析等功能。
            </p>
          </div>

          <!-- 使用场景 -->
          <div>
            <h3 class="font-bold mb-3 text-lg">使用场景</h3>
            <div class="grid grid-cols-2 gap-4">
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium mb-2">运动健身</h4>
                <p class="text-sm text-gray-600">支持30+运动模式，精准记录运动数据，帮助您科学健身</p>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium mb-2">日常监测</h4>
                <p class="text-sm text-gray-600">全天候监测心率、血氧和睡眠质量，守护健康</p>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium mb-2">游泳防水</h4>
                <p class="text-sm text-gray-600">IP68级防水，可在游泳时佩戴使用</p>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium mb-2">智能助手</h4>
                <p class="text-sm text-gray-600">来电提醒、消息通知、天气预报，贴心生活助手</p>
              </div>
            </div>
          </div>

          <!-- 产品特点 -->
          <div>
            <h3 class="font-bold mb-3 text-lg">产品特点</h3>
            <ul class="space-y-3">
              <li v-for="(feature, index) in product.features" :key="index" class="flex items-start bg-gray-50 p-3 rounded-lg">
                <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                <span class="text-gray-700">{{ feature }}</span>
              </li>
            </ul>
          </div>

          <!-- 健康监测 -->
          <div>
            <h3 class="font-bold mb-3 text-lg">健康监测功能</h3>
            <div class="bg-gray-50 p-4 rounded-lg space-y-3">
              <div class="flex items-center">
                <i class="fas fa-heartbeat text-red-500 mr-3"></i>
                <div>
                  <h4 class="font-medium">心率监测</h4>
                  <p class="text-sm text-gray-600">24小时持续监测心率变化，异常心率提醒</p>
                </div>
              </div>
              <div class="flex items-center">
                <i class="fas fa-moon text-blue-500 mr-3"></i>
                <div>
                  <h4 class="font-medium">睡眠分析</h4>
                  <p class="text-sm text-gray-600">专业睡眠分期监测，提供睡眠质量报告</p>
                </div>
              </div>
              <div class="flex items-center">
                <i class="fas fa-running text-green-500 mr-3"></i>
                <div>
                  <h4 class="font-medium">运动模式</h4>
                  <p class="text-sm text-gray-600">支持30+专业运动模式，智能识别运动类型</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 规格参数 -->
      <div ref="specsRef" class="py-4">
        <div class="divide-y px-4">
          <div 
            v-for="(spec, index) in product.specs" 
            :key="index"
            class="flex justify-between py-3"
          >
            <span class="text-gray-500">{{ spec.name }}</span>
            <span>{{ spec.value }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部操作栏 -->
    <div class="fixed-footer">
      <button @click="addToCart" class="btn w-full bg-orange-500 text-white rounded-full py-3 text-lg font-medium">
        加入购物车
      </button>
    </div>

    <!-- 顶部提示条 -->
    <div 
      v-if="showToast" 
      class="fixed top-16 left-1/2 bg-gray-800 text-white px-4 py-2 rounded-full z-50 shadow-lg"
      style="transform: translateX(-50%)"
    >
      {{ toastMessage }}
    </div>

    <!-- 商品选择弹出层 -->
    <div 
      v-if="isSkuDrawerVisible" 
      class="fixed inset-0 bg-black bg-opacity-50 z-50"
      @click="closeSkuDrawer"
    >
      <div 
        class="fixed bottom-0 left-0 right-0 bg-white rounded-t-2xl p-4"
        @click.stop
      >
        <template v-if="!isAddSuccess">
          <!-- 商品信息 -->
          <div class="mb-6">
            <div class="flex items-center justify-between mb-3">
              <h3 class="text-lg font-medium">{{ product.title }}</h3>
              <button 
                @click="closeSkuDrawer"
                class="p-2 hover:bg-gray-100 rounded-full"
              >
                <i class="fas fa-times text-gray-400"></i>
              </button>
            </div>
            <div class="h-[1px] bg-gray-100"></div>
          </div>

          <!-- 颜色选择 -->
          <div class="mb-6">
            <h4 class="text-gray-500 mb-3">颜色</h4>
            <div class="flex flex-wrap gap-3">
              <button
                v-for="color in product.options.colors"
                :key="color.id"
                class="flex items-center px-4 py-2 rounded-full border-2"
                :class="{
                  'border-primary text-primary': selectedOptions.color === color.id,
                  'border-gray-200': selectedOptions.color !== color.id
                }"
                @click="selectOption('color', color.id)"
              >
                <span 
                  class="w-4 h-4 rounded-full mr-2"
                  :style="{ backgroundColor: color.code }"
                ></span>
                {{ color.name }}
              </button>
            </div>
          </div>

          <!-- 尺寸选择 -->
          <div class="mb-6">
            <h4 class="text-gray-500 mb-3">尺寸</h4>
            <div class="flex flex-wrap gap-3">
              <button
                v-for="size in product.options.sizes"
                :key="size.id"
                class="flex flex-col items-start px-4 py-2 rounded-lg border-2"
                :class="{
                  'border-primary text-primary': selectedOptions.size === size.id,
                  'border-gray-200': selectedOptions.size !== size.id
                }"
                @click="selectOption('size', size.id)"
              >
                <span class="font-medium">{{ size.name }}</span>
                <span class="text-xs text-gray-500">{{ size.desc }}</span>
              </button>
            </div>
          </div>

          <!-- 数量选择 -->
          <div class="mb-6">
            <div class="flex items-center justify-between">
              <h4 class="text-gray-500">数量</h4>
              <div class="flex items-center">
                <button 
                  @click="decreaseQuantity"
                  class="w-8 h-8 rounded-full border border-gray-200 flex items-center justify-center"
                  :disabled="quantity <= 1"
                >
                  <i class="fas fa-minus text-sm"></i>
                </button>
                <span class="mx-4 min-w-[3ch] text-center">{{ quantity }}</span>
                <button 
                  @click="increaseQuantity"
                  class="w-8 h-8 rounded-full border border-gray-200 flex items-center justify-center"
                  :disabled="quantity >= product.stock"
                >
                  <i class="fas fa-plus text-sm"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- 确认按钮 -->
          <button 
            @click="addToCart"
            class="w-full bg-primary text-white rounded-full py-3 text-lg font-medium"
            :disabled="!isOptionsSelected"
          >
            确定
          </button>
        </template>

        <!-- 成功状态 -->
        <template v-else>
          <div class="min-h-[300px] flex flex-col items-center justify-center">
            <div class="mb-4">
              <i class="fas fa-check-circle text-6xl text-green-500"></i>
            </div>
            <p class="text-xl font-medium text-gray-800">加入购物车成功</p>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped>
.product-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100vh;
  position: relative;
  background: white;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: white;
  z-index: 50;
  border-bottom: 1px solid #e5e7eb;
  height: 56px;
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding-top: 56px;
  padding-bottom: 80px;
}

.fixed-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  z-index: 50;
}

.btn {
  -webkit-tap-highlight-color: transparent;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.tab-container {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 40;
  background: white;
  width: 100%;
}

.tab-wrapper {
  display: flex;
  width: 100%;
  border-bottom: 1px solid #eee;
  background: white;
  padding: 0 16px;
}

.tab-button {
  position: relative;
  flex: 1;
  padding: 14px 0;
  text-align: center;
  font-size: 14px;
  font-weight: normal;
  color: #999;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.tab-button.active {
  color: #1890ff;
  font-weight: normal;
}

.tab-line {
  position: absolute;
  left: 0;
  bottom: -1px;
  height: 2px;
  background-color: #1890ff;
  width: 100%;
  pointer-events: none;
}

.card {
  padding: 16px;
  margin: 0;
}

#descriptionRef,
#specsRef {
  padding: 16px;
  scroll-margin-top: 44px;
}

.primary {
  --tw-text-opacity: 1;
  color: rgb(24 144 255 / var(--tw-text-opacity));
}

.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(24 144 255 / var(--tw-bg-opacity));
}

.border-primary {
  --tw-border-opacity: 1;
  border-color: rgb(24 144 255 / var(--tw-border-opacity));
}
</style> 