@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
@layer components {
  .app-container {
    @apply min-h-screen bg-gray-50 text-gray-900 font-sans;
  }
  
  .bottom-nav {
    @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 flex justify-around items-center h-16 text-xs z-10;
  }
  
  .bottom-nav-item {
    @apply flex flex-col items-center justify-center w-1/4 py-1;
  }
  
  .bottom-nav-icon {
    @apply text-xl mb-1;
  }
  
  .active-nav-item {
    @apply text-primary;
  }
  
  .inactive-nav-item {
    @apply text-gray-500;
  }
  
  .page-container {
    @apply pt-4 px-4 max-w-screen-lg mx-auto;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-sm p-4 mb-4;
  }
  
  .btn {
    @apply px-4 py-2 rounded-lg font-medium;
  }
  
  .btn-primary {
    @apply bg-primary text-white;
  }
  
  .btn-secondary {
    @apply bg-secondary text-white;
  }
  
  .section-title {
    @apply text-lg font-bold mb-3;
  }
}
