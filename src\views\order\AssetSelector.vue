<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-center justify-center">
    <!-- 遮罩层 -->
    <div class="absolute inset-0 bg-black bg-opacity-50" @click="handleClose"></div>
    
    <!-- 弹窗内容 -->
    <div class="relative bg-white w-full max-w-md mx-4 rounded-lg shadow-xl">
      <!-- 弹窗头部 -->
      <div class="px-4 py-3 border-b flex items-center justify-between">
        <h3 class="text-lg font-medium">选择资产</h3>
        <div class="flex items-center space-x-3">
          <button 
            @click="handleConfirm" 
            class="text-blue-600 text-sm font-medium"
            :disabled="currentSelected.length === 0"
            :class="{'opacity-50': currentSelected.length === 0}"
          >
            完成
          </button>
          <button @click="handleClose" class="text-gray-400 hover:text-gray-500">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      
      <!-- 搜索框 -->
      <div class="p-4 border-b">
        <div class="relative">
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="搜索资产"
            class="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>
      
      <!-- 资产列表 -->
      <div class="max-h-96 overflow-y-auto">
        <div class="divide-y">
          <div
            v-for="asset in filteredAssets"
            :key="asset.id"
            class="p-4 flex items-center hover:bg-gray-50 cursor-pointer"
            @click="toggleAssetSelection(asset)"
          >
            <div class="flex-1">
              <div class="text-sm font-medium text-gray-900">{{ asset.name }}</div>
              <div class="text-xs text-gray-500">{{ asset.code }}</div>
            </div>
            <div class="ml-4">
              <div 
                class="w-6 h-6 rounded-full border-2 flex items-center justify-center"
                :class="isSelected(asset) ? 'border-blue-600 bg-blue-600' : 'border-gray-300'"
              >
                <i v-if="isSelected(asset)" class="fas fa-check text-white text-sm"></i>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="filteredAssets.length === 0" class="p-4 text-center text-gray-500">
          未找到相关资产
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Asset {
  id: number
  name: string
  code: string
  model?: string
}

const props = defineProps<{
  show: boolean
  selectedAssets: Asset[]
}>()

const emit = defineEmits<{
  (e: 'update:show', value: boolean): void
  (e: 'confirm', assets: Asset[]): void
}>()

// 搜索关键词
const searchKeyword = ref('')

// 模拟资产数据
const assets: Asset[] = [
  { id: 1, name: 'ThinkPad X1 Carbon', code: 'NB001', model: '2022款 i7 16GB' },
  { id: 2, name: 'Dell P2415Q 显示器', code: 'DS001', model: '27英寸 4K' },
  { id: 3, name: 'MacBook Pro M1', code: 'NB002', model: '13英寸 8GB' },
  { id: 4, name: '惠普打印机', code: 'PT001', model: 'LaserJet Pro' },
  { id: 5, name: '投影仪', code: 'PJ001', model: 'Epson CB-X05' }
]

// 当前选中的资产
const currentSelected = ref<Asset[]>([...props.selectedAssets])

// 根据搜索关键词过滤资产
const filteredAssets = computed(() => {
  if (!searchKeyword.value) return assets
  const keyword = searchKeyword.value.toLowerCase()
  return assets.filter(
    asset => 
      asset.name.toLowerCase().includes(keyword) || 
      asset.code.toLowerCase().includes(keyword)
  )
})

// 检查资产是否被选中
const isSelected = (asset: Asset) => {
  return currentSelected.value.some(item => item.id === asset.id)
}

// 切换资产选中状态
const toggleAssetSelection = (asset: Asset) => {
  const index = currentSelected.value.findIndex(item => item.id === asset.id)
  if (index > -1) {
    currentSelected.value.splice(index, 1)
  } else {
    currentSelected.value.push(asset)
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:show', false)
}

// 确认选择
const handleConfirm = () => {
  emit('confirm', currentSelected.value)
  handleClose()
}
</script> 