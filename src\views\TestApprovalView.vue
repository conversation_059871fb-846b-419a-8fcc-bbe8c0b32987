<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-medium">测试审批</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <van-button @click="handleTestApproval">测试审批</van-button>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import * as ww from '@wecom/jssdk'
import { getJsSDKAgentSignature, getJsSDKConfigSignature, getRegisterInfo } from '@/api/workwx'
import { useRouter } from 'vue-router'
import { v4 as uuidv4 } from 'uuid';

const router = useRouter()

const goBack = () => {
  router.back()
}

const handleTestApproval = async () => {
  console.log('ww.SDK_VERSION', ww.SDK_VERSION)

  try {
    // 获取当前页面完整 URL，不包含 hash
    // console.log('当前页面URL:', currentUrl)
    // const ticket = 'HoagFKDcsGMVCIY2vOjf9oU9v1guggZyRL70kLLKn2T9Ydq4DXUBO13MwSY0k2QqwZ8s3kGQRytEY2diHgP1Nw'
    const res = await getRegisterInfo()
    console.log('getRegisterInfo res:', res)

    ww.register({
      corpId: res.corpId,
      agentId: res.agentId,
      // suiteId: res.suiteId,
      jsApiList: [
        'thirdPartyOpenPage',
      ],
      async getConfigSignature(url: string): Promise<ww.SignatureData> {
        const currentUrl = window.location.href.split('#')[0]
        console.log('getConfigSignature href:', window.location.href)
        console.log('getConfigSignature currentUrl:', currentUrl)
        console.log('getConfigSignature url:', url)
        const res = await getJsSDKConfigSignature({ url: url })
        console.log('getConfigSignature response:', res)
        // return ww.getSignature(res.ticket)
        return {
          timestamp: res.timestamp,
          nonceStr: res.nonceStr,
          signature: res.signature
        }
      },
      async getAgentConfigSignature(url: string): Promise<ww.SignatureData> {
        const currentUrl = window.location.href.split('#')[0]
        console.log('getAgentConfigSignature href:', window.location.href)
        console.log('getAgentConfigSignature currentUrl:', currentUrl)
        console.log('getAgentConfigSignature url:', url)
        const res = await getJsSDKAgentSignature({ url: url })
        console.log('getAgentConfigSignature response:', res)
        // return ww.getSignature(ticket)
        const obj = {
          timestamp: res.timestamp,
          nonceStr: res.nonceStr,
          signature: res.signature
        }
        console.log('getAgentConfigSignature obj:', obj)
        return obj
      },
      onConfigSuccess: (res) => {
        console.log('onConfigSuccess', res)
      },
      onConfigFail: (res) => {
        console.error('onConfigFail', res)
      },
      onConfigComplete: (res) => {
        console.log('onConfigComplete', res)
      },
      onAgentConfigSuccess: (res) => {
        console.log('onAgentConfigSuccess', res)
      },
      onAgentConfigFail: (res) => {
        console.error('onAgentConfigFail', res)
      },
      onAgentConfigComplete: (res) => {
        console.log('onAgentConfigComplete', res)
      }
    })

    ww.thirdPartyOpenPage({
      oaType: ww.OAType.create_approval,
      templateId: 'c2c9482ab06ec7e3eb2220004a6e108a_1669874093',
      thirdNo: '123',
      extData: {
        fieldList: [
          {
            title: '采购类型',
            type: ww.OaExtDataType.text,
            value: '市场活动',
          },
          {
              'title': '采购说明',
              'type': ww.OaExtDataType.text,
              'value': '购买个人办公电脑',
          },
          {
              'title': '采购金额',
              'type': ww.OaExtDataType.text,
              'value': '4839.00元',
          },
          {
              'title': '申请时间',
              'type': ww.OaExtDataType.text,
              'value': '2018/06/20',
          },
          {
            type: ww.OaExtDataType.link,
            title: '订单链接',
            value: 'https://work.weixin.qq.com/dddd?id=123'
          }
        ]
      }
    })
  } catch (error) {
    console.error('企业微信 JSSDK 初始化失败:', error)
  }
}

onMounted(() => {
})
</script>

<style scoped>
.page-container {
  padding-top: 56px;
  padding-bottom: 60px;
  background-color: #f9fafb;
  min-height: 100vh;
}

.form-content {
  padding: 20px 8px 8px;
}

/* 审批流程自定义样式 */
:deep(.approval-flow-custom) {
  margin-left: 2px;
  padding-left: 5px;
  margin-top: -10px;
}
</style>