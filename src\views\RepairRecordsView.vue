<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import List from '@/components/List.vue'
import { hpRepairRecord } from '@/api/equipment'

const router = useRouter()

// 定义维修记录类型
interface RepairRecord {
  id: string;
  materialName: string;
  createdAt: string;
  createdBy: string;
  departmentId: number;
  departmentName: string;
  files: string[];
  images: string[];
  materialAddress: string;
  problemDescription: string;
  remark: string;
  // status: 'pending' | 'completed' | 'rejected';
  status: number;
  updatedAt: string;
  updatedBy: string;
}

// 维修记录数据
// const records = ref<RepairRecord[]>([])
const fetchData = async (page: number, pageSize: number) => {
  const params = {
    page,
    pageSize,
    orderBy: JSON.stringify(["-updated_at", "-created_at"])
  }
  let res = await hpRepairRecord(params)
  // 发起实际的数据请求
  // const res = await api.getList({ page, pageSize });
  // return {
  //   list: res.data.list,
  //   total: res.data.total
  // };
  // const res = setTimeout(() => {}, 1000)
  return {
    list: res.items,
    total: res.total
  };
}
// 获取状态样式
const getStatusStyle = (status: number) => {
  const styles = {
    0: 'bg-yellow-100 text-yellow-800',
    1: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return styles[status as keyof typeof styles] || 'bg-gray-100 text-gray-800'
}

// 跳转到维修登记页面
const goToRepairRegistration = () => {
  router.push('/repair-registration')
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 查看记录详情
const viewRecordDetail = (record) => {
  // const record = records.value.find(r => r.id === id)
  // if (record) {
    router.push({
      path: '/repair-registration',
      query: {
        mode: record.status === 1 ? 'view' : 'edit',
        data: JSON.stringify(record)
      }
    })
  // }
}
</script>

<template>
  <div class="page-container min-h-screen bg-gray-50 no-scrollbar">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-bold">维修登记</h1>
        <button
          @click="goToRepairRegistration"
          class="w-6 h-6 rounded-full bg-primary flex items-center justify-center"
        >
          <i class="fas fa-plus text-white"></i>
        </button>
      </div>
    </div>

    <!-- 记录列表 -->
    <List :request-fn="fetchData" v-slot="{ list }">
      <div class="pt-14 pb-4 px-2">
        <div class="space-y-4">
          <div
            v-for="record in list"
            :key="record.id"
            class="bg-white rounded-lg p-4 shadow-sm"
            @click="viewRecordDetail(record)"
          >
            <div class="flex items-start justify-between mb-3">
              <div class="flex-1 min-w-0 mr-4">
                <h3 class="text-base font-medium text-gray-900 truncate">{{ record.materialName }}</h3>
              </div>
              <div class="flex items-center">
                <span
                  :class="[
                    'px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap',
                    getStatusStyle(record.status)
                  ]"
                >
                  {{ record.status == 1 ? '维修完成' : '待维修' }}
                </span>
              </div>
            </div>

            <div class="space-y-2">
              <div class="flex items-center">
                <span class="text-sm text-gray-500 shrink-0 w-[4.5rem]">报修部门：</span>
                <span class="text-sm text-gray-900 flex-1 min-w-0">{{ record.departmentName }}</span>
              </div>
              <div class="flex items-center">
                <span class="text-sm text-gray-500 shrink-0 w-[4.5rem]">位置：</span>
                <span class="text-sm text-gray-900 flex-1 min-w-0">{{ record.materialAddress }}</span>
              </div>
              <div class="flex items-start">
                <span class="text-sm text-gray-500 shrink-0 w-[4.5rem]">问题描述：</span>
                <span class="text-sm text-gray-900 flex-1 min-w-0">{{ record.problemDescription }}</span>
              </div>
              <div class="flex items-center">
                <span class="text-sm text-gray-500 shrink-0 w-[4.5rem]">报修人：</span>
                <span class="text-sm text-gray-900 flex-1 min-w-0">{{ record.createdBy }}</span>
              </div>
              <div class="flex items-center">
                <span class="text-sm text-gray-500 shrink-0 w-[4.5rem]">报修时间：</span>
                <span class="text-sm text-gray-900 flex-1 min-w-0">{{ record.createdAt }}</span>
              </div>
              <template v-if="record.status === 1">
                <div class="flex items-center">
                  <span class="text-sm text-gray-500 shrink-0 w-[4.5rem]">完成时间：</span>
                  <span class="text-sm text-gray-900 flex-1 min-w-0">{{ record.updatedAt }}</span>
                </div>
                <div v-if="record.remark" class="flex items-start">
                  <span class="text-sm text-gray-500 shrink-0 w-[4.5rem]">维修备注：</span>
                  <span class="text-sm text-gray-900 flex-1 min-w-0">{{ record.remark }}</span>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </List>
  </div>
</template>

<style scoped>
.page-container {
  /* padding-bottom: env(safe-area-inset-bottom); */
}

.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}
</style>
