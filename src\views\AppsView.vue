<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'

const router = useRouter()

// 定义功能项类型
interface FunctionItem {
  id: number;
  name: string;
  icon: string;
  color: string;
  badge?: number;
  link?: string;
}

// 所有可用的功能项
const allFunctions: FunctionItem[] = [
  { id: 1, name: '发起申请', icon: 'mdi:file-document-edit', color: 'bg-blue-100 text-blue-500' },
  { id: 2, name: '预算查询', icon: 'mdi:chart-line', color: 'bg-green-100 text-green-500', link: '/budget-statistics' },
  { id: 3, name: '预购登记', icon: 'mdi:clipboard-text', color: 'bg-orange-100 text-orange-500', link: '/preorder-records' },
  { id: 4, name: '资产查询', icon: 'mdi:magnify', color: 'bg-purple-100 text-purple-500' },
  { id: 5, name: '发起申请', icon: 'mdi:file-document-edit', color: 'bg-blue-100 text-blue-500' },
  { id: 6, name: '我的审批', icon: 'mdi:check-circle', color: 'bg-green-100 text-green-500', link: '/my-approval' },
  { id: 7, name: '抄送我的', icon: 'mdi:email', color: 'bg-yellow-100 text-yellow-600', link: '/my-copy' },
  { id: 8, name: '我提交的', icon: 'mdi:upload', color: 'bg-red-100 text-red-500', link: '/my-submit' },
  { id: 10, name: '预购登记', icon: 'mdi:clipboard-text', color: 'bg-orange-100 text-orange-500', link: '/preorder-records' },
  { id: 12, name: '维修登记', icon: 'mdi:wrench', color: 'bg-indigo-100 text-indigo-500', link: '/repair-records' },
  { id: 13, name: '验收登记', icon: 'mdi:checkbox-marked-circle', color: 'bg-green-100 text-green-500', link: '/acceptance-records' },
  { id: 14, name: '预算申请', icon: 'mdi:currency-usd', color: 'bg-emerald-100 text-emerald-600', link: '/budget-application' },
  { id: 15, name: '调拨申请', icon: 'mdi:transfer', color: 'bg-sky-100 text-sky-600', link: '/transfer-application' },
  { id: 16, name: '报废申请', icon: 'mdi:delete-circle', color: 'bg-rose-100 text-rose-600', link: '/scrap-application' },
  { id: 17, name: '购物车', icon: 'mdi:hand-extended', color: 'bg-amber-100 text-amber-600', link: '/cart?type=apply' },
  { id: 18, name: '退还入仓', icon: 'mdi:package-variant-closed', color: 'bg-violet-100 text-violet-600', link: '/return-application' },
  // { id: 19, name: '测试审批', icon: 'mdi:magnify', color: 'bg-purple-100 text-purple-500', link: '/test-approval' }
]

// 最近使用的功能项
const recentlyUsed = ref<FunctionItem[]>([])

// 从本地存储获取最近使用的功能
const getRecentlyUsed = () => {
  const stored = localStorage.getItem('recentlyUsed')
  if (stored) {
    const ids = JSON.parse(stored)
    recentlyUsed.value = ids
      .map((id: number) => allFunctions.find(f => f.id === id))
      .filter(Boolean)
      .slice(0, 4)
  }
}

// 更新最近使用的功能
const updateRecentlyUsed = (item: FunctionItem) => {
  const stored = localStorage.getItem('recentlyUsed')
  let recentIds = stored ? JSON.parse(stored) : []

  // 移除已存在的相同ID
  recentIds = recentIds.filter((id: number) => id !== item.id)
  // 将新项添加到开头
  recentIds.unshift(item.id)
  // 只保留最近4个
  recentIds = recentIds.slice(0, 4)

  localStorage.setItem('recentlyUsed', JSON.stringify(recentIds))
  getRecentlyUsed()
}

// 功能模块数据
const functionModules = ref([
  {
    title: '最近使用',
    items: recentlyUsed
  },
  {
    title: '发起申请',
    items: allFunctions.filter(item => [19,14, 15, 16, 17, 18].includes(item.id))
  },
  {
    title: '审批管理',
    items: allFunctions.filter(item => [6, 7, 8].includes(item.id))
  },
  {
    title: '资产管理',
    items: allFunctions.filter(item => [10, 12, 13].includes(item.id))
  }
])

// 点击功能模块
const handleModuleClick = (item: FunctionItem) => {
  updateRecentlyUsed(item)

  if (item.link) {
    console.log('正在跳转到:', item.link)
    router.push(item.link)
  } else if (item.id === 1 || item.id === 5) {
    console.log('跳转到应用页面')
    router.push('/applications')
  } else {
    console.log('无链接的功能:', item.name)
  }
}

// 组件挂载时获取最近使用数据
onMounted(() => {
  getRecentlyUsed()
})
</script>

<template>
  <div class="page-container bg-gray-50">
    <!-- 顶部标题 -->
    <!-- <div class="py-3 mb-2 text-center">
      <h1 class="text-lg font-bold text-gray-800">应用中心</h1>
    </div> -->
    <van-nav-bar fixed title="应用中心" />

    <div
      v-for="(module, moduleIndex) in functionModules"
      :key="moduleIndex"
      class="mb-4"
    >
      <div class="bg-white rounded-lg p-4 shadow-sm">
        <h2 class="text-sm font-medium mb-3 text-gray-700">{{ module.title }}</h2>
        <div class="grid grid-cols-4 gap-4">
          <div
            v-for="(item, itemIndex) in module.items"
            :key="`${moduleIndex}-${item.id}`"
            class="flex flex-col items-center cursor-pointer"
            @click="handleModuleClick(item)"
          >
            <div class="relative mb-2">
              <div :class="['w-12 h-12 rounded-full flex items-center justify-center', item.color]">
                <Icon :icon="item.icon" class="text-2xl" />
              </div>
              <div v-if="item.badge" class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                <span class="text-white text-xs">{{ item.badge }}</span>
              </div>
            </div>
            <span class="text-xs text-center text-gray-700 w-full whitespace-normal min-h-[1.5rem] break-words">{{ item.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 自定义样式 */
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.page-container {
  /* padding-top: 50px; */
  /* padding-bottom: 5rem; */
  padding: 48px 16px 0px;
}
</style>