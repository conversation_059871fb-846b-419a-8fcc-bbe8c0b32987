<template>
  <div class="page-container min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-medium">选择资产</h1>
        <button 
          @click="handleConfirm" 
          class="text-blue-600 text-sm font-medium"
        >
          完成
        </button>
      </div>
    </div>

    <!-- 搜索框 -->
    <div class="fixed top-12 left-0 right-0 bg-white border-b border-gray-200 z-10 px-4 py-3">
      <div class="relative">
        <input
          v-model="searchKeyword"
          type="text"
          placeholder="搜索资产"
          class="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
      </div>
    </div>

    <!-- 资产列表 -->
    <div class="pt-28 px-4 pb-4">
      <div class="space-y-3">
        <div
          v-for="asset in filteredAssets"
          :key="asset.id"
          class="bg-white p-4 rounded-lg shadow-sm flex items-center"
          @click="toggleAssetSelection(asset)"
        >
          <div class="flex-1">
            <div class="text-sm font-medium text-gray-900">{{ asset.name }}</div>
            <div class="text-xs text-gray-500">资产编号：{{ asset.code }}</div>
            <div class="text-xs text-gray-500 mt-1">规格型号：{{ asset.model }}</div>
          </div>
          <div class="ml-4">
            <div 
              class="w-6 h-6 rounded-full border-2 flex items-center justify-center"
              :class="isSelected(asset) ? 'border-blue-600 bg-blue-600' : 'border-gray-300'"
            >
              <i v-if="isSelected(asset)" class="fas fa-check text-white text-sm"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredAssets.length === 0" class="text-center py-8 text-gray-500">
        <i class="fas fa-search text-3xl mb-2"></i>
        <p>未找到相关资产</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'

interface Asset {
  id: number
  name: string
  code: string
  model?: string
}

const router = useRouter()
const route = useRoute()

// 搜索关键词
const searchKeyword = ref('')

// 模拟资产数据
const assets: Asset[] = [
  { id: 1, name: 'ThinkPad X1 Carbon', code: 'NB001', model: '2022款 i7 16GB' },
  { id: 2, name: 'Dell P2415Q 显示器', code: 'DS001', model: '27英寸 4K' },
  { id: 3, name: 'MacBook Pro M1', code: 'NB002', model: '13英寸 8GB' },
  { id: 4, name: '惠普打印机', code: 'PT001', model: 'LaserJet Pro' },
  { id: 5, name: '投影仪', code: 'PJ001', model: 'Epson CB-X05' }
]

// 当前选中的资产
const selectedAssets = ref<Asset[]>(route.query.selectedAssets ? JSON.parse(route.query.selectedAssets as string) : [])

// 根据搜索关键词过滤资产
const filteredAssets = computed(() => {
  if (!searchKeyword.value) return assets
  const keyword = searchKeyword.value.toLowerCase()
  return assets.filter(
    asset => 
      asset.name.toLowerCase().includes(keyword) || 
      asset.code.toLowerCase().includes(keyword)
  )
})

// 检查资产是否被选中
const isSelected = (asset: Asset) => {
  return selectedAssets.value.some(item => item.id === asset.id)
}

// 切换资产选中状态
const toggleAssetSelection = (asset: Asset) => {
  const index = selectedAssets.value.findIndex(item => item.id === asset.id)
  if (index > -1) {
    selectedAssets.value.splice(index, 1)
  } else {
    selectedAssets.value.push(asset)
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 确认选择
const handleConfirm = () => {
  // 获取来源路径
  const sourcePath = route.query.source as string || '/scrap-application'
  
  router.replace({
    path: sourcePath,
    query: {
      selectedAssets: JSON.stringify(selectedAssets.value)
    }
  })
}
</script> 