import { defHttp } from "../axios";

export const getEquipmentDetailData = (id: string) => defHttp.get(
  {
    url: `/wms/v1/equipment/${id}`,
    headers: {
      ignoreCancelToken: true,
      
    },
  }
);
// 部门
export const hpWxworkDepartment = () => defHttp.get(
  {
    url: `/hp/v1/hpWxworkDepartment`,
    params: {
      nopaging: true,
    },
    headers: {
      ignoreCancelToken: true,
    }
  }
);

// 人员
export const hpWxworkUser = (data) => defHttp.get(
  {
    url: `/hp/v1/hpWxworkUser`,
    params: data
  }
);

// 资产类型
export const hpMaterialType = () => defHttp.get(
  {
    url: `/hp/v1/hpMaterialType`
  }
);

// 预购记录列表
export const hpPreOrderRecord = () => defHttp.get(
  {
    url: `/hp/v1/hpPreOrderRecord`
  }
);
// 新增预购
export const addOrderRecord = (data) => defHttp.post(
  {
    url: `/hp/v1/hpPreOrderRecord`,
    data
  }
);

// 编辑预购
export const editOrderRecord = (data) => defHttp.put(
  {
    url: `/hp/v1/hpPreOrderRecord/${data.id}`,
    data
  }
);

// 删除预购
export const delOrderRecord = (id: string) => defHttp.delete(
  {
    url: `/hp/v1/hpPreOrderRecord/${id}`
  }
);

// 上传附件
export const upload = (data:any) => defHttp.post(
  {
    url: `/system/v1/upload`,
    data,
    headers: {
      'Content-Type': 'multipart/form-data',  // 确保设置正确的 Content-Type
    }
  }
);

// 申领
export const hpClaimOrder = (data:any) => defHttp.post(
  {
    url: `/hp/v1/hpClaimOrder`,
    data
  }
);

interface Data {
  page?: number;
  pageSize?: number;
  nopaging?: boolean;
  query?: string;
  orderBy?: string;
}
// 维修记录
export const hpRepairRecord = (data: Data) => defHttp.get(
  {
    url: `/hp/v1/hpRepairRecord`,
    params: data
  }
);

// 新增维修
export const addRepairRecord = (data) => defHttp.post(
  {
    url: `/hp/v1/hpRepairRecord`,
    data
  }
);

// 编辑维修
export const editRepairRecord = (data) => defHttp.put(
  {
    url: `/hp/v1/hpRepairRecord/${data.id}`,
    data
  }
);

// 删除维修
export const delRepairRecord = (id: string) => defHttp.delete(
  {
    url: `/hp/v1/hpRepairRecord/${id}`
  }
);

// 验收单记录
export const checkAcceptOrder = (data: Data) => defHttp.get(
  {
    url: `/hp/v1/hpCheckAcceptOrder`,
    params: data
  }
);
// 新增验收单
export const addCheckAcceptOrder = (data) => defHttp.post(
  {
    url: `/hp/v1/hpCheckAcceptOrder`,
    data
  }
);

// 编辑维修
export const editCheckAcceptOrder = (data) => defHttp.put(
  {
    url: `/hp/v1/hpCheckAcceptOrder/${data.id}`,
    data
  }
);

// 删除维修
export const delCheckAcceptOrder = (id: string) => defHttp.delete(
  {
    url: `/hp/v1/hpCheckAcceptOrder/${id}`
  }
);

export const dictionary = (data) => defHttp.get(
  {
    url: `/system/v1/sysDictionaryDetailByDictionaryName`,
    params: data
  }
);

// 我的资产
export const myMaterial = (data:Data) => defHttp.get(
  {
    url: `/hp/v1/material/my`,
    params: data
  }
);

// 我的盘点
export const myAuditplan = (data:Data) => defHttp.get(
  {
    url: `/hp/v1/auditplan/my`,
    params: data
  }
);

// 我的盘点详情
export const myAuditplanDetail = (data:Data) => defHttp.get(
  {
    url: `/hp/v1/auditplandetail/my`,
    params: data
  }
);

export const updatePlanDetail = (data) => defHttp.put(
  {
    url: `/hp/v1/hpAuditPlanDetail/${data.id}`,
    data
  }
);