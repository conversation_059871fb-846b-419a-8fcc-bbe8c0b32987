pipeline {
  agent any
  environment {
    //CD repo
    DOCKER_DEPLOY_REPO = 'e.coding.net/baigong/FirePlatfromLv2Node/docker-deploy.git'

    //Dockerilfe
    DOCKERFILE_PATH  = "Dockerfile"

    //Context
    DOCKER_BUILD_CONTEXT = "."

    //这里设置你想推送过去的镜像仓库
    DOCKER_REPO_NAME = "docker"

    //镜像名默认是git仓库名
    DOCKER_IMAGE_NAME = "${DEPOT_NAME}"
    CODING_DOCKER_REG_HOST = "${CCI_CURRENT_TEAM}-docker.pkg.${CCI_CURRENT_DOMAIN}"
    CODING_DOCKER_IMAGE_NAME = "${PROJECT_NAME.toLowerCase()}/${DOCKER_REPO_NAME.toLowerCase()}/${DOCKER_IMAGE_NAME.toLowerCase()}"

    BRANCH = "${BRANCH_NAME == "" ? MR_TARGET_BRANCH : BRANCH_NAME}"
    //镜像版本，如果是打了标签，就是标签名，否则就是branch+commit
    VERSION_DEV = "${BRANCH}-${GIT_COMMIT}"
    VERSION_PROD = "${GIT_TAG}"

    //Super Credentials
    CI_CREDENTIAL="7d92607b-c449-4736-ae73-c9a511ba211a"

  }
  stages  {
    stage("检出") {
      steps {
        checkout(
          [$class: 'GitSCM',
          branches: [[name: GIT_BUILD_REF]],
          userRemoteConfigs: [[
            url: GIT_REPO_URL,
              credentialsId: CREDENTIALS_ID
            ]]]
        )
      }
    }

    stage("构建镜像并推送到 CODING Docker 制品库") {
      steps {
        script {
          docker.withRegistry(
            "${CCI_CURRENT_WEB_PROTOCOL}://${CODING_DOCKER_REG_HOST}",
            "${CODING_ARTIFACTS_CREDENTIALS_ID}"
          ) {
            if(env.GIT_TAG != null) {
               def dockerImage = docker.build("${CODING_DOCKER_IMAGE_NAME}:${VERSION_PROD}", "-f ${DOCKERFILE_PATH} ${DOCKER_BUILD_CONTEXT}")
               dockerImage.push()
            } else {
               def dockerImage = docker.build("${CODING_DOCKER_IMAGE_NAME}:${VERSION_DEV}", "-f ${DOCKERFILE_PATH} ${DOCKER_BUILD_CONTEXT}")
               dockerImage.push()
            }
          }
        }
      }
    }

    stage('更新测试环境CD') {
      when {
        not {
          tag '*'
        }
      }
      steps {
        withCredentials([[$class: 'UsernamePasswordMultiBinding',
             credentialsId: CI_CREDENTIAL, usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD']]) {
          script {
            sh """
           git clone https://${SUB119_CI_GK}:${SUB119_CI_TOKEN}@${DOCKER_DEPLOY_REPO} docker_repo
           cd docker_repo/lv1
           sed -i 's|\\(image: ${CODING_DOCKER_REG_HOST}/${CODING_DOCKER_IMAGE_NAME}:\\).*|\\1${VERSION_DEV}|' docker-compose.yaml
           git add . &&  git commit -m 'update staging image tag'
           git push https://${SUB119_CI_GK}:${SUB119_CI_TOKEN}@${DOCKER_DEPLOY_REPO}
           """
          }
             }
      }
    }
  }
}
