<template>
  <div class="image-uploader">
    <van-uploader
      v-model="internalFileList"
      :after-read="handleUpload"
      :before-read="beforeRead"
      :max-count="maxCount"
      :deletable="editable"
      :multiple="multiple"
      :disabled="!editable"
      @delete="handleDelete"
    >
      <!-- 自定义预览遮罩 -->
      <template #preview-cover="{ file, index }">
        <div class="preview-mask" @click.stop="handlePreview(index)">
          <van-icon name="eye-o" size="24" />
        </div>
      </template>
      
      <!-- 自定义上传图标 -->
      <!-- <template #default>
        <van-icon name="plus" size="24" />
      </template> -->
    </van-uploader>
  </div>
</template>

<script setup>
import { upload } from '@/api/equipment'
import { ref, watch } from 'vue';
import { showImagePreview, showToast, showLoadingToast, closeToast } from 'vant';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  maxCount: {
    type: Number,
    default: 9
  },
  editable: {
    type: Boolean,
    default: true
  },
  multiple: {
    type: Boolean,
    default: true
  },
  maxSize: {
    type: Number,
    default: 5 * 1024 * 1024 // 默认5MB
  }
});

const emit = defineEmits(['update:modelValue', 'upload-success', 'upload-error']);

// 内部管理的文件列表
const internalFileList = ref([]);

// 获取当前有效URL列表
const getCurrentUrls = () => {
  return internalFileList.value
    .filter(item => item.status === 'done')
    .map(item => item.url);
};

// 同步外部数据到内部列表
const syncFileList = (urls = []) => {
  console.log('调了吗', urls);
  
  internalFileList.value = urls.map(url => ({
    url,
    status: 'done',
    message: '上传成功'
  }));
  console.log('internalFileList', internalFileList.value);
  
};

// 初始化同步数据
watch(() => props.modelValue, (newVal) => {

  if (JSON.stringify(newVal) !== JSON.stringify(getCurrentUrls())) {
    syncFileList(newVal);
  }
}, { immediate: true });

// 文件校验
const beforeRead = (file) => {
  console.log('aaa', file);
  if (Array.isArray(file)) {
    for (const fileItem of file) {
      if (!fileItem.type.startsWith('image/')) {
        showToast('请选择图片文件');
        return false;
      }
      if (fileItem.size > props.maxSize) {
        showToast(`图片大小不能超过${props.maxSize / 1024 / 1024}MB`);
        return false;
      }
    }
  } else {
    if (!file.type.startsWith('image/')) {
      showToast('请选择图片文件');
      return false;
    }
    if (file.size > props.maxSize) {
      showToast(`图片大小不能超过${props.maxSize / 1024 / 1024}MB`);
      return false;
    }
  }
  return true;
};

// 处理文件上传
const handleUpload = async (file) => {
  try {
    let res
    if (Array.isArray(file)) {
      for (const fileItem of file) {
        const formDataToUpload = new FormData()
        formDataToUpload.append('file', fileItem.file)
        fileItem.status = 'uploading';
        fileItem.message = '上传中...';
        res = await upload(formDataToUpload)
        console.log('图片', res);    
        fileItem.status = 'done';
        fileItem.url = res.value;
        fileItem.message = '上传成功';
        
        emitUpdate();
        emit('upload-success', res.value);  
      }
    } else {
      const formDataToUpload = new FormData()
      formDataToUpload.append('file', file.file)
      res = await upload(formDataToUpload)
      console.log('图片', res);   
      file.status = 'done';
      file.url = res.value;
      file.message = '上传成功';
      emitUpdate();
      emit('upload-success', res.value);
    }
  } catch (error) {
    fileItem.status = 'failed';
    fileItem.message = '上传失败';
    emit('upload-error', error);
    showToast('上传失败，请重试');
  }
};

// 删除文件
const handleDelete = (fileItem) => {
  internalFileList.value = internalFileList.value.filter(item => item !== fileItem);
  emitUpdate();
};

// 图片预览
const handlePreview = (index) => {
  console.log('Preview index:', index);
  console.log('Internal file list:', internalFileList.value);
  
  // 检查索引是否有效
  if (typeof index !== 'number' || index < 0 || index >= internalFileList.value.length) {
    showToast('预览失败：无效的图片索引');
    return;
  }

  // 获取预览列表
  const previewList = internalFileList.value
    .filter(item => item.status === 'done')
    .map(item => item.url)
    .filter(Boolean);

  console.log('Preview list:', previewList);
  
  if (previewList.length === 0) {
    showToast('没有可预览的图片');
    return;
  }

  // 使用传入的索引作为起始位置
  const startPosition = index;

  showImagePreview({
    images: previewList,
    startPosition
  });
};

// 触发数据更新
const emitUpdate = () => {
  const urls = getCurrentUrls();
  emit('update:modelValue', urls);
};

// 模拟上传接口
const mockUpload = (file) => {
  return new Promise((resolve, reject) => {
    showLoadingToast({
      message: '上传中...',
      forbidClick: true,
      duration: 0
    });

    setTimeout(() => {
      closeToast();
      if (Math.random() > 0.1) { // 模拟10%失败率
        resolve(URL.createObjectURL(file));
      } else {
        reject(new Error('模拟上传失败'));
      }
    }, 1500);
  });
};
</script>

<style scoped>
.image-uploader {
  --uploader-size: 100px;
}

:deep(.van-uploader__upload) {
  width: var(--uploader-size);
  height: var(--uploader-size);
  margin: 8px;
  border: 1px dashed #ccc;
  border-radius: 8px;
}

:deep(.van-uploader__preview-image) {
  width: var(--uploader-size);
  height: var(--uploader-size);
  margin: 8px;
  border-radius: 8px;
  overflow: hidden;
}

.preview-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
  cursor: pointer;
}

.preview-mask:hover {
  opacity: 1;
}

.preview-mask .van-icon {
  color: #fff;
}
</style>