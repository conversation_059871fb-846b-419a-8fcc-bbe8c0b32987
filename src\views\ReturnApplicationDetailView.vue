<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-medium">退还单据详情</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <!-- 申请表单 -->
    <div class="form-content">
      <form class="space-y-4">
        <!-- 基本信息 -->
        <BasicInfoCard
          :applicant="formData.createdBy"
          :department="formData.createdByDepartmentName"
          :apply-time="formData.createdAt"
        />

        <!-- 事由 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-3">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              退还说明
            </label>
            <div class="w-full p-2 bg-gray-50 rounded-md min-h-[100px]">
              {{ formData.reason }}
            </div>
          </div>
        </div>

        <!-- 退还明细 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-3">
            <div class="flex justify-between items-center mb-3">
              <label class="block text-sm font-medium text-gray-700">
                退还明细
              </label>
            </div>
            
            <div v-if="!formData.details || formData.details.length === 0" class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg">
              <i class="fas fa-inbox text-3xl mb-2"></i>
              <p>暂无退还物品</p>
            </div>
            
            <div v-else class="space-y-2">
              <div 
                v-for="item in formData.details" 
                :key="item.id" 
                class="flex justify-between items-center p-2 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-900 mb-1">{{ item.name }}</div>
                  <div class="text-xs text-gray-500 mb-1 flex items-center">
                    <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                    <span>资产编号：{{ item.no }}</span>
                  </div>
                  <div class="text-xs text-gray-500 flex items-center">
                    <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                    <span>规格型号：{{ item.model }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 资产状态 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-3">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              资产状态
            </label>
            <div class="px-3 py-2 bg-gray-50 rounded-md inline-block">
              <span v-if="formData.materialStatus === 'normal'" class="text-green-600">
                <i class="fas fa-check-circle mr-1.5"></i> 完好
              </span>
              <span v-else-if="formData.materialStatus === 'damaged'" class="text-orange-600">
                <i class="fas fa-tools mr-1.5"></i> 损坏
              </span>
              <span v-else-if="formData.materialStatus === 'lost'" class="text-red-600">
                <i class="fas fa-times-circle mr-1.5"></i> 丢失
              </span>
            </div>
          </div>
        </div>

        <!-- 备注 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-3">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              备注
            </label>
            <div class="w-full p-2 bg-gray-50 rounded-md">
              {{ formData.remark || '无' }}
            </div>
          </div>
        </div>

        <!-- 附件列表 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <div class="mb-4">
              <label class="text-sm font-medium text-gray-700">附件列表</label>
              <NewFileUpload v-if="formData.files && formData.files.length" v-model="formData.files" :editable="false" class="mt-2"/>
              <div v-else class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg mt-2">
                <i class="fas fa-cloud-upload-alt text-3xl mb-2"></i>
                <p>暂无附件</p>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, defineAsyncComponent } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import BasicInfoCard from '@/views/order/BasicInfoCard.vue'
import { returnOrderDetail } from '@/api/approval'
import NewFileUpload from '@/components/newFileUpload.vue'
const router = useRouter()
const route = useRoute()

// 表单数据接口定义
interface ReturnItem {
  id: number;
  name: string;
  model: string;
  image: string;
  code: string;
  warehouse: string;
}

interface Attachment {
  name: string;
  url: string;
  size: number;
  type: string;
}

// 模拟数据
const formData = ref({
  details: [],
  files: []
})

// 返回上一页
const goBack = () => {
  router.back()
}
// 获取申请详情
const fetchApplicationDetail = async () => {
  try {
    const id = route.query.id
    console.log('ca参数id', id);
    
    if (!id) {
      ElMessage.error('未找到申请ID')
      router.replace('/my-submit')
      return
    }
    console.log(`获取ID为 ${id} 的退还申请详情`)
    returnOrderDetail(id).then(res => {
      console.log('ress退还详情', res);
      res.files = res.hasOwnProperty('files') && res.files ? res.files.split(',') : []
      formData.value = res
    })
    
    // 数据已在上面的formData中定义
  } catch (error) {
    console.error('获取申请详情失败：', error)
    ElMessage.error('获取申请详情失败')
    router.replace('/my-submit')
  }
}

onMounted(() => {
  fetchApplicationDetail()
})
</script>

<style scoped>
.page-container {
  padding-top: 56px;
  padding-bottom: 60px;
  background-color: #f9fafb;
  min-height: 100vh;
}

.form-content {
  padding: 20px 8px 8px;
}

/* 审批流程自定义样式 */
:deep(.approval-flow-custom) {
  margin-left: 2px;
  padding-left: 5px;
  margin-top: -10px;
}
</style> 