<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-medium">调拨申请</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <!-- 申请表单 -->
    <div class="form-content">
      <div class="space-y-6">
        <!-- 调入部门 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4 space-y-4">
            <!-- 所属部门 -->
            <DepartmentPicker v-model="formData.departmentId" title="接收部门" @finish="handleDepartmentFinish" :department-name="formData.departmentName"/>
            <User v-model="formData.userId" title="接收人员" :user-name="formData.userName" @finish="handleUserFinish" :disabled="!formData.departmentId" :disabled-text="'请先选择部门'" :department-id="formData.departmentId"/>
          </div>
        </div>

        <!-- 调拨资产列表 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <div class="flex justify-between items-center mb-4">
              <label class="block text-sm font-medium text-gray-700">
                <span class="text-red-500">*</span>调拨资产
              </label>
              <button 
                type="button"
                @click="goToAssetSelector"
                class="inline-flex items-center px-3 py-1.5 border border-blue-600 text-blue-600 rounded-md text-sm font-medium hover:bg-blue-50"
              >
                <i class="fas fa-plus mr-1"></i>
                添加资产
              </button>
            </div>
            
            <div v-if="selectedAssets.length === 0" class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg">
              <i class="fas fa-inbox text-3xl mb-2"></i>
              <p>暂未选择资产</p>
            </div>
            
            <div v-else class="space-y-3">
              <div 
                v-for="asset in selectedAssets" 
                :key="asset.id" 
                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-900">{{ asset.name }}</div>
                  <div class="text-xs text-gray-500">资产编号：{{ asset.no }}</div>
                </div>
                <button 
                  type="button"
                  @click="removeAsset(asset.id)"
                  class="text-red-500 hover:text-red-600 p-1"
                >
                  <i class="fas fa-trash-alt"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 调拨原因 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <span class="text-red-500">*</span>调拨原因
            </label>
            <textarea 
              v-model="formData.reason"
              rows="4"
              class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入调拨原因"
            ></textarea>
          </div>
        </div>

        <!-- 上传附件 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <label class="text-sm font-medium text-gray-700">附件</label>
            <NewFileUpload v-model="formData.files" class="mt-2"/>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
      <button 
        type="button"
        @click="handleSubmit"
        class="w-full px-4 py-2 border border-transparent rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        :disabled="isSubmitting"
      >
        {{ isSubmitting ? '提交中...' : '下一步' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import ApprovalFlow from '@/views/order/ApprovalFlow.vue'
import DepartmentPicker from '@/components/DepartmentPicker.vue'
import User from '@/components/User.vue'
import type { FinishData } from '@/components/DepartmentPicker.vue'
import { hpTransferOrder } from '@/api/approval'
import * as ww from '@wecom/jssdk'
import NewFileUpload from '@/components/newFileUpload.vue'

const router = useRouter()
const route = useRoute()

interface DepartmentOption {
  text: string;
  value: string;
  children?: DepartmentOption[];
}
interface FinishEvent {
  selectedOptions: DepartmentOption[];
}

interface Asset {
  id: number;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
  status: number;
  no: string;
  name: string;
  type?: string;
  brand?: string;
  model?: string;
  unitPrice?: number;
  quantity?: number;
  unit?: string;
  totalPrice?: number;
  isFromCheckAcceptOrder?: number;
  departmentId?: number;
  materialAlias?: string;
  images?: string;
}

interface User {
  id: number;
  name: string;
  departmentId: number;
  departmentName: string;
}

interface Attachment {
  name: string;
  size: number;
  file: File;
  status: 'uploading' | 'success' | 'error';
  progress?: number;
}


// 处理部门选择完成
const handleDepartmentFinish = (data: FinishData) => {
  formData.departmentName = data.displayText
  formData.userName = ''
  formData.userId = ''
}
// 人员完成
const handleUserFinish = (data) => {
  formData.userName = data.displayText
}

// 表单数据
const formData = reactive({
  departmentId: 0,
  departmentName: '',
  userName: '',
  userId: '',
  reason: '',
  files: []
})

// 选中的资产列表
const selectedAssets = ref<Asset[]>([])

// 文件上传相关
const fileInput = ref<HTMLInputElement | null>(null)
const FILE_SIZE_LIMIT = 10 * 1024 * 1024 // 10MB

// 监听路由参数变化，更新选中的资产
onMounted(() => {
  if (localStorage.getItem('transferData')) {
    let transferData = JSON.parse(localStorage.getItem('transferData') as string)
    formData.departmentId = transferData.departmentId
    formData.departmentName = transferData.departmentName
    formData.files = transferData.files
    formData.reason = transferData.reason
    formData.userName = transferData.userName
    formData.userId = transferData.userId
    localStorage.removeItem('transferData')
  }
  if (route.query.selectedAssets) {
    selectedAssets.value = JSON.parse(route.query.selectedAssets as string)
  }
})

const isSubmitting = ref(false)

// 移除资产
const removeAsset = (assetId: number) => {
  const index = selectedAssets.value.findIndex(asset => asset.id === assetId)
  if (index > -1) {
    selectedAssets.value.splice(index, 1)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (isSubmitting.value) return

  // 表单验证
  if (!formData.departmentId) {
    ElMessage.error('请选择接收部门')
    return
  }

  if (!formData.userId) {
    ElMessage.error('请选择接收人员')
    return
  }

  if (selectedAssets.value.length === 0) {
    ElMessage.error('请选择需要调拨的资产')
    return
  }

  if (!formData.reason) {
    ElMessage.error('请填写调拨原因')
    return
  }
  
  try {
    isSubmitting.value = true
    // TODO: 实现提交逻辑
    const data =  {
      ...formData,
      files: formData.files && formData.files.length ? formData.files.join(',') : '',
      details: JSON.parse(JSON.stringify(selectedAssets.value))
    }
    console.log('提交的表单数据:', data)
    const startTime = Date.now()
    const res = await hpTransferOrder(data)
    // 计算已经过去的时间
    const elapsedTime = Date.now() - startTime
    // 如果耗时小于200ms，则等待剩余时间
    if (elapsedTime < 200) {
      await new Promise(resolve => setTimeout(resolve, 200 - elapsedTime))
    }

    // 显示成功提示
    // showSuccessModal.value = true
    // 开始时间
    console.log( Date.now(), '开始')
    const url = import.meta.env.VITE_URL
    const link = `${url}#/transfer-application-detail?id=${res.id}`
    let templateId = import.meta.env.VITE_ENV === 'prod' ? '1887a3147a69c1a7081f0c0ce0f2f6b8_543392048' : '94c90b30c42ac0654378a7b9a951cef7_914654109'
    ww.thirdPartyOpenPage({
      oaType: ww.OAType.create_approval,
      templateId,
      thirdNo: 'DBSQ-' + res.id,
      extData: {
        fieldList: [
          {
            title: '申请类型',
            type: ww.OaExtDataType.text,
            value: '调拨申请',
          },
          {
              'title': '申请部门',
              'type': ww.OaExtDataType.text,
              'value': formData.departmentName,
          },
          {
              'title': '申请时间',
              'type': ww.OaExtDataType.text,
              'value': res.createdAt,
          },
          {
            'title': '调拨原因',
            'type': ww.OaExtDataType.text,
            'value': formData.reason,
          },
          {
            type: ww.OaExtDataType.link,
            title: '详细内容',
            value: link
          }
        ]
      },
      success: () => {
        console.log( Date.now(), '提交成功')
        // router.push('/apps')
      },
      fail: () => {
        console.log( Date.now(), '提交失败')
      },
      complete: () => {
        console.log( Date.now(), '提交完成')
      }
    })
    
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.push('/apps')
}

// 跳转到资产选择页面
const goToAssetSelector = () => {
  localStorage.setItem('transferData', JSON.stringify(formData))
  router.push({
    path: '/asset-selector-transfer',
    query: {
      selectedAssets: JSON.stringify(selectedAssets.value)
    }
  })
}
</script>

<style scoped>
.form-group {
  @apply space-y-1 relative;
}
.page-container {
  background-color: rgb(249, 250, 251);
}

.form-content {
  padding: 50px 16px 82px;
}
.input {
  @apply w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-primary;
}

/* 下拉框样式 */
select.input {
  @apply appearance-none bg-white cursor-pointer w-full;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23999' d='M6 8.825L1.175 4 2.238 2.938 6 6.7l3.763-3.763L10.825 4z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 0.75rem;
  padding-right: 2.5rem;
}

select.input::-ms-expand {
  display: none;
}

select.input option {
  @apply py-2 px-3 text-gray-600 cursor-pointer;
  max-width: 100%;
  width: 100%;
  box-sizing: border-box;
}

/* 添加一个新的样式来控制下拉列表 */
select.input:not([size]) {
  background-color: white;
}
.label {
  @apply block text-sm font-medium text-gray-700;
}
</style> 