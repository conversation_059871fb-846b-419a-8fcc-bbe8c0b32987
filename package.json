{"name": "vuw-project-a", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 8083 --host 0.0.0.0 --mode dev", "build": "pnpm vite build -- --max-old-space-size=4096 && cd dist && zip -r dist.zip . && mv dist.zip ../", "build-win:prod": "pnpm vite build --mode prod -- --max-old-space-size=4096 && cd dist && zip -r dist.zip . && del ..\\dist.zip && move .\\dist.zip ..\\", "build-win:bg": "pnpm vite build --mode bg -- --max-old-space-size=4096 && cd dist && zip -r dist.zip . && del ..\\dist.zip && move .\\dist.zip ..\\", "build:docker": "pnpm vite build --mode prod -- --max-old-space-size=4096", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-free": "^6.7.2", "@iconify/vue": "^4.3.0", "@types/signature_pad": "^4.0.0", "@vueuse/motion": "^3.0.3", "@wecom/jssdk": "^2.3.1", "axios": "^1.8.2", "dayjs": "^1.11.13", "element-plus": "^2.9.6", "lodash-es": "^4.17.21", "nanoid": "^5.1.5", "pinia": "^3.0.1", "qs": "^6.14.0", "signature_pad": "^5.0.6", "vant": "^4.9.17", "vconsole": "^3.15.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-signature-pad": "^3.0.2"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/jsdom": "^21.1.7", "@types/node": "^22.13.10", "@vitejs/plugin-vue": "^5.2.1", "@vitest/eslint-plugin": "^1.1.36", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.17", "eslint": "^9.21.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "postcss": "^8.4.35", "prettier": "3.5.3", "tailwindcss": "^3.4.1", "typescript": "~5.8.0", "vite": "^6.2.1", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.0.8", "vue-tsc": "^2.2.8"}}