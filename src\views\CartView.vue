<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '@/stores/cart'
import List from '@/components/List.vue'
interface CartItem {
  id: number
  name: string
  model: string
  quantity: number
  image: string
  code: string
  selected: boolean
}

const router = useRouter()
const baseUrl = import.meta.env.VITE_BASE_URL
// 购物车数据
const cartStore = useCartStore()
const cartItems = computed(() => {
  console.log('cartStore.cartItems', cartStore.cartItems);

  return cartStore.cartItems
})

// 搜索关键词
const searchKeyword = ref('')

// 购物车商品列表
const cartItemsList = ref<CartItem[]>([])

// 从 localStorage 加载购物车数据
const loadCartItems = () => {
  cartStore.loadCartFromStorage()
}

// 初始加载
onMounted(() => {
  loadCartItems()
})

// 监听购物车数据变化，保存到 localStorage
watch(cartItems, () => {
  cartStore.saveCartToStorage()
}, { deep: true })

// 过滤后的购物车商品
const filteredCartItems = computed(() => {
  if (!searchKeyword.value) return cartItems.value
  const keyword = searchKeyword.value.toLowerCase()
  return cartItems.value.filter(item => {
    const name = (item.name || '').toLowerCase()
    const model = (item.model || '').toLowerCase()
    const code = (item.code || '').toLowerCase()
    return name.includes(keyword) ||
           model.includes(keyword) ||
           code.includes(keyword)
  })
})

// 返回上一页
const goBack = () => {
  // 使用 replace 并添加强制刷新参数
  // router.replace({
  //   path: '/mall',
  //   query: { t: Date.now() }  // 添加时间戳参数强制页面刷新
  // })
  router.back()
}

// 全选状态
const allSelected = computed({
  get: () => cartItems.value.length > 0 && cartItems.value.every(item => item.selected),
  set: (value) => {
    cartItems.value.forEach(item => {
      item.selected = value
    })
  }
})

// 选中商品数量
const selectedCount = computed(() => {
  return cartItems.value.filter(item => item.selected).length
})

// 选中商品总数量（包含每个商品的数量）
const totalSelectedQuantity = computed(() => {
  return cartItems.value.reduce((total, item) => total + (item.selected ? item.quantity : 0), 0)
})

// 选中商品总价
const totalPrice = computed(() => {
  return cartItems.value.reduce((total, item) => total + (item.selected ? item.quantity : 0), 0)
})

// 切换商品选中状态
const toggleItemSelection = (itemId: number) => {
  const item = cartItems.value.find(item => item.id === itemId)
  if (item) {
    item.selected = !item.selected
  }
}

// 增加商品数量
const increaseQuantity = (itemId: number) => {
  const item = cartItems.value.find(item => item.id === itemId)
  if (item) {
    cartStore.updateQuantity(itemId, item.quantity + 1)
  }
}

// 减少商品数量
const decreaseQuantity = (itemId: number) => {
  const item = cartItems.value.find(item => item.id === itemId)
  if (item && item.quantity > 1) {
    cartStore.updateQuantity(itemId, item.quantity - 1)
  }
}

// 确认框状态
const showConfirmDialog = ref(false)
const itemToDelete = ref<number | null>(null)

// 删除商品
const removeItem = (itemId: number) => {
  itemToDelete.value = itemId
  showConfirmDialog.value = true
}

// 提示框状态
const showToast = ref(false)
const toastMessage = ref('')

// 显示提示框
const showToastMessage = (message: string) => {
  toastMessage.value = message
  showToast.value = true
  setTimeout(() => {
    showToast.value = false
  }, 2000)
}

// 确认删除
const confirmDelete = () => {
  if (itemToDelete.value === null) return

  const item = cartItems.value.find(item => item.id === itemToDelete.value)
  if (!item) return

  cartStore.removeFromCart(itemToDelete.value)
  showToastMessage(`${item.name}已删除`)

  showConfirmDialog.value = false
  itemToDelete.value = null
}

// 取消删除
const cancelDelete = () => {
  showConfirmDialog.value = false
  itemToDelete.value = null
}

// 申领
const applyItems = () => {
  if (selectedCount.value === 0) {
    alert('请选择要申领的物品')
    return
  }
  router.push('/apply-form')
}

// 借用
const borrowItems = () => {
  if (selectedCount.value === 0) {
    alert('请选择要借用的物品')
    return
  }
  router.push('/borrow-form')
}

// 重置购物车数据
const resetCartData = () => {
  cartStore.clearCart()
  showToastMessage('购物车数据已重置')
}

// 更新商品数量
const updateQuantity = (itemId: number, newQuantity: number) => {
  if (newQuantity < 1) {
    newQuantity = 1
  }
  cartStore.updateQuantity(itemId, newQuantity)
}

// Add this to the script section
const handleImageError = (e: Event) => {
  const img = e.target as HTMLImageElement;
  img.src = 'https://img.freepik.com/free-photo/product-package-box-isolated_125540-1169.jpg';
};
const fetchData = async () => {
  return {
    list: filteredCartItems.value,
    total: filteredCartItems.value.length
  }
}
</script>

<template>
  <div class="page-container flex flex-col bg-[#f9fafb] no-scrollbar overflow-hidden">
    <!-- 顶部提示条 -->
    <div
      v-if="showToast"
      class="fixed top-16 left-1/2 bg-gray-800 text-white px-4 py-2 rounded-full z-50 shadow-lg"
      style="transform: translateX(-50%)"
    >
      {{ toastMessage }}
    </div>

    <!-- 顶部导航栏 - 固定在顶部 -->
    <div class="fixed-header">
      <!-- 返回按钮和标题 -->
      <div class="flex items-center justify-between pt-2 pb-3 px-4 bg-white border-b border-gray-100">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-bold flex-1 text-center">购物车</h1>
        <div class="w-8"></div>
      </div>
      <!-- 搜索框 -->
      <div class="px-3 pt-3 pb-2 bg-white border-b border-gray-100">
        <div class="relative">
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="搜索资产名称/型号"
            class="w-full h-9 pl-8 pr-8 rounded-full bg-gray-50 border border-gray-200 focus:outline-none focus:border-primary text-sm"
          >
          <i class="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 text-xs"></i>
          <!-- 添加重置按钮 -->
          <button
            v-if="searchKeyword"
            @click="searchKeyword = ''"
            class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            <i class="fas fa-times text-xs"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 顶部空白占位，与固定头部高度相同 -->
    <div class="header-placeholder"></div>

    <!-- 购物车内容区域 -->
    <div>
      <!-- 购物车为空时的提示 -->
      <div v-if="filteredCartItems.length === 0" class="flex flex-col items-center justify-center py-12">
        <i class="fas fa-shopping-cart text-6xl text-gray-300 mb-4"></i>
        <p class="text-gray-500">{{ cartItems.length === 0 ? '购物车还是空的' : '没有找到相关商品' }}</p>
      </div>

      <!-- 购物车列表 -->
      <List v-else :request-fn="fetchData" v-slot="{ list }">
        <div class="space-y-3 py-2">
          <!-- 商品列表 -->
          <div class="space-y-2">
              <div
                v-for="item in list"
                :key="item.id"
                class="bg-white rounded-xl p-4 relative mx-1"
              >
                <!-- 删除按钮 -->
                <button
                  @click="removeItem(item.id)"
                  class="absolute top-2 right-2 p-2 text-gray-400 hover:text-gray-600 delete-btn"
                >
                  <i class="fas fa-trash-alt"></i>
                </button>

                <div class="flex items-center space-x-3">
                  <!-- 商品选择框 -->
                  <div
                    class="w-5 h-5 rounded-full border-2 flex items-center justify-center cursor-pointer"
                    :class="[item.selected ? 'border-primary bg-primary' : 'border-gray-300']"
                    @click="toggleItemSelection(item.id)"
                  >
                    <i v-if="item.selected" class="fas fa-check text-xs text-white"></i>
                  </div>

                  <!-- 商品图片 -->
                  <div class="w-24 h-24 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                    <img
                      :src="baseUrl + item.images || 'https://img.freepik.com/free-photo/product-package-box-isolated_125540-1169.jpg'"
                      :alt="item.name"
                      class="w-full h-full object-contain"
                      @error="handleImageError"
                    >
                  </div>

                  <!-- 商品信息 -->
                  <div class="flex-1 min-w-0">
                    <div class="flex flex-col h-full">
                      <div class="flex-1">
                        <h3 class="text-base font-medium text-gray-900">{{ item.name }}</h3>
                        <div class="mt-1 space-y-1">
                          <div class="text-sm text-gray-500">资产编号：{{ item.no }}</div>
                          <div class="text-sm text-gray-500">规格型号：{{ item.model }}</div>
                        </div>
                      </div>
                      <div class="flex justify-end mt-2 quantity-control">
                        <div class="flex items-center space-x-3">
                          <button
                            @click="decreaseQuantity(item.id)"
                            class="w-7 h-7 rounded-full border border-gray-200 flex items-center justify-center"
                            :class="{ 'opacity-50': item.quantity <= 1 }"
                          >
                            <i class="fas fa-minus text-xs"></i>
                          </button>
                          <span class="min-w-[2ch] text-center">{{ item.quantity }}</span>
                          <button
                            @click="increaseQuantity(item.id)"
                            class="w-7 h-7 rounded-full border border-gray-200 flex items-center justify-center"
                          >
                            <i class="fas fa-plus text-xs"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
          </div>
        </div>
      </List>
    </div>

    <!-- 底部结算栏 -->
    <div v-if="cartItems.length > 0" class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 p-4">
      <div class="flex items-center justify-between max-w-screen-lg mx-auto">
        <div class="flex items-center space-x-3">
          <div
            class="w-5 h-5 rounded-full border-2 flex items-center justify-center cursor-pointer"
            :class="[allSelected ? 'border-primary bg-primary' : 'border-gray-300']"
            @click="allSelected = !allSelected"
          >
            <i v-if="allSelected" class="fas fa-check text-xs text-white"></i>
          </div>
          <span class="text-sm">全选</span>
        </div>
        <div class="flex items-center">
          <div class="mr-4">
            <span class="text-sm">合计：</span>
            <span class="text-lg font-medium text-primary">{{ totalSelectedQuantity }}件</span>
          </div>
          <div class="flex space-x-2">
            <button
              @click="applyItems"
              class="px-6 py-2 bg-primary text-white rounded-lg text-sm"
              :class="{ 'opacity-50': selectedCount === 0 }"
            >
              申领
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认删除对话框 -->
    <div v-if="showConfirmDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-2xl w-[80%] max-w-sm p-6">
        <h3 class="text-lg font-medium text-center mb-4">确认删除</h3>
        <p class="text-gray-600 text-center mb-6">
          确定要删除该商品吗？
        </p>
        <div class="flex space-x-3">
          <button
            @click="cancelDelete"
            class="flex-1 py-2.5 rounded-full border border-gray-200 text-gray-600"
          >
            取消
          </button>
          <button
            @click="confirmDelete"
            class="flex-1 py-2.5 rounded-full bg-red-500 text-white"
          >
            删除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.page-container {
  padding: 60px 16px 80px;
}
.cart-page {
  min-height: 100vh;
  background: #f9fafb;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  width: 100%;
}

.header-placeholder {
  height: 56px; /* 只需要考虑顶部导航栏的高度 */
  width: 100%;
}

.fixed-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  z-index: 50;
}

.content-area {
  padding-top: 56px;
  padding-bottom: 80px;
}

.primary {
  --tw-text-opacity: 1;
  color: rgb(24 144 255 / var(--tw-text-opacity));
}

.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(24 144 255 / var(--tw-bg-opacity));
}

.border-primary {
  --tw-border-opacity: 1;
  border-color: rgb(24 144 255 / var(--tw-border-opacity));
}

/* 隐藏滚动条 */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;     /* Firefox */
  overflow-y: scroll;
  overflow-x: hidden;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
  width: 0;
}

/* 添加一个通用的滚动条隐藏样式 */
* {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

*::-webkit-scrollbar {
  display: none;
  width: 0;
}

.page-container {
  background-color: #f9fafb;
}
</style>
