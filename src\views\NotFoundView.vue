<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <div class="error-code">404</div>
      <div class="error-title">页面未找到</div>
      <p class="error-message">您访问的页面不存在或已被移除</p>
      <button @click="goBack" class="back-button">
        <i class="fas fa-arrow-left mr-2"></i>返回上一页
      </button>
      <button @click="goHome" class="home-button">
        <i class="fas fa-home mr-2"></i>返回首页
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  router.back()
}

const goHome = () => {
  router.push('/')
}
</script>

<style scoped>
.not-found-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f7f8fa;
  padding: 1rem;
}

.not-found-content {
  text-align: center;
  max-width: 400px;
  background-color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.error-code {
  font-size: 5rem;
  font-weight: bold;
  color: #3b82f6;
  line-height: 1;
  margin-bottom: 1rem;
}

.error-title {
  font-size: 1.5rem;
  font-weight: medium;
  margin-bottom: 0.5rem;
  color: #111827;
}

.error-message {
  color: #6b7280;
  margin-bottom: 1.5rem;
}

.back-button, .home-button {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  font-weight: medium;
  margin-bottom: 0.75rem;
  transition: all 0.2s;
}

.back-button {
  background-color: #3b82f6;
  color: white;
  border: none;
}

.home-button {
  background-color: white;
  color: #3b82f6;
  border: 1px solid #3b82f6;
}

.back-button:hover {
  background-color: #2563eb;
}

.home-button:hover {
  background-color: #f0f9ff;
}
</style> 