<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 设置项
const settings = ref([
  {
    id: 'account',
    title: '账号与安全',
    items: [
      // { id: 'profile', name: '个人资料', icon: 'fa-user', link: '/profile-edit' },
      { id: 'profile', name: '个人资料', icon: 'fa-lock', link: '/profile-edit' },
      // { id: 'privacy', name: '隐私设置', icon: 'fa-shield-alt', link: '/privacy' }
    ]
  },
  // {
  //   id: 'notification',
  //   title: '通知设置',
  //   items: [
  //     { id: 'push', name: '推送通知', icon: 'fa-bell', toggle: true, value: true },
  //     { id: 'email', name: '邮件通知', icon: 'fa-envelope', toggle: true, value: false },
  //     { id: 'sms', name: '短信通知', icon: 'fa-sms', toggle: true, value: true }
  //   ]
  // },
  // {
  //   id: 'general',
  //   title: '通用设置',
  //   items: [
  //     { id: 'language', name: '语言', icon: 'fa-language', value: '简体中文', link: '/language' },
  //     { id: 'theme', name: '主题', icon: 'fa-palette', value: '浅色', link: '/theme' },
  //     { id: 'font', name: '字体大小', icon: 'fa-text-height', value: '默认', link: '/font-size' }
  //   ]
  // },
  // {
  //   id: 'about',
  //   title: '关于',
  //   items: [
  //     // { id: 'feedback', name: '意见反馈', icon: 'fa-comment-alt', link: '/feedback' },
  //     // { id: 'rate', name: '给我们评分', icon: 'fa-star', link: '/rate' },
  //     // { id: 'terms', name: '用户协议', icon: 'fa-file-alt', link: '/terms' },
  //     // { id: 'privacy-policy', name: '隐私政策', icon: 'fa-user-shield', link: '/privacy-policy' },
  //     { id: 'version', name: '版本信息', icon: 'fa-info-circle', value: 'v1.0.0' }
  //   ]
  // }
])

// 切换开关
const toggleSetting = (sectionId: string, itemId: string) => {
  const section = settings.value.find(s => s.id === sectionId)
  if (section) {
    const item = section.items.find(i => i.id === itemId)
    if (item && 'toggle' in item) {
      item.value = !item.value
    }
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 处理菜单项点击
const handleItemClick = (item: any) => {
  if ('link' in item) {
    router.push(item.link)
  }
}

// // 退出登录
// const logout = () => {
//   // 实际应用中应该调用API
//   alert('已退出登录')
//   router.push('/')
// }
</script>

<template>
  <div class="page-container pb-24">
    <!-- 顶部导航 -->
    <div class="flex items-center justify-between mb-6 relative">
      <button @click="goBack" class="absolute left-0 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
        <i class="fas fa-arrow-left text-gray-500"></i>
      </button>
      <h1 class="text-xl font-bold w-full text-center">设置</h1>
    </div>

    <!-- 设置列表 -->
    <div class="space-y-6 overflow-x-hidden">
      <div v-for="section in settings" :key="section.id">
        <h2 class="text-sm text-gray-500 mb-2 px-1">{{ section.title }}</h2>
        <div class="card p-0 divide-y">
          <div
            v-for="item in section.items"
            :key="item.id"
            class="flex items-center justify-between p-4"
            :class="{ 'cursor-pointer': 'link' in item }"
            @click="'link' in item && handleItemClick(item)"
          >
            <div class="flex items-center">
              <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                <i :class="['fas', item.icon, 'text-gray-500']"></i>
              </div>
              <span>{{ item.name }}</span>
            </div>

            <!-- 开关 -->
            <div v-if="'toggle' in item" class="relative">
              <div
                class="w-12 h-6 rounded-full cursor-pointer transition-colors duration-300"
                :class="item.value ? 'bg-primary' : 'bg-gray-300'"
                @click="toggleSetting(section.id, item.id)"
              >
                <div
                  class="absolute top-1 w-4 h-4 rounded-full bg-white transition-transform duration-300"
                  :class="item.value ? 'left-7' : 'left-1'"
                ></div>
              </div>
            </div>

            <!-- 值 -->
            <div v-else-if="'value' in item" class="flex items-center">
              <span class="text-sm text-gray-500 mr-1">{{ item.value }}</span>
              <i v-if="'link' in item" class="fas fa-chevron-right text-gray-300"></i>
            </div>

            <!-- 箭头 -->
            <div v-else-if="'link' in item">
              <i class="fas fa-chevron-right text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 退出登录按钮 -->
  </div>
</template>

<style scoped>
.page-container {
  padding-top: 1rem;
  height: 100vh;
  overflow-y: auto;
}

/* 隐藏滚动条但保持可滚动 */
.page-container::-webkit-scrollbar {
  display: none;
}

/* Firefox */
.page-container {
  scrollbar-width: none;
}
</style>