import { defHttp } from "../axios";

export const getDefault = (url: string, params?: any, headers?: any) => defHttp.get(
  {
    url,
    params,
    headers: {
      ignoreCancelToken: true,
      ...headers
    },
  }
);

export const postDefault = (url: string, data?: any, headers?: any) => defHttp.post(
  {
    url,
    data,
    headers: {
      ignoreCancelToken: true,
      ...headers
    },
  }
);

export const getDict = (dictionaryName: string) => getDefault(`/system/v1/sysDictionaryDetailByDictionaryName?nopaging=true&dictionaryName=${dictionaryName}`)
