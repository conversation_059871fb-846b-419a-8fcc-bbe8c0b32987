import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

// 定义购物车项的类型
export interface CartItem {
  brand: string
  createdAt: string
  createdBy: string
  id: number
  name: string
  model: string
  quantity: number
  images: string
  no: string
  selected: boolean
  unitPrice: number
}

export const useCartStore = defineStore('cart', () => {
  // 购物车数据
  const cartItems = ref<any[]>([])
  
  // 从 localStorage 加载购物车数据
  const loadCartFromStorage = () => {
    console.log('加载购物车数据');
    
    const savedCart = localStorage.getItem('cartItems')
    if (savedCart) {
      try {
        cartItems.value = JSON.parse(savedCart)
      } catch (e) {
        console.error('解析购物车数据失败:', e)
        cartItems.value = []
      }
    }
  }
  
  // 保存购物车数据到 localStorage
  const saveCartToStorage = () => {
    localStorage.setItem('cartItems', JSON.stringify(cartItems.value))
  }
  
  // 计算购物车中的商品总数
  const cartCount = computed(() => {
    return cartItems.value.reduce((total, item) => total + item.quantity, 0)
  })
  
  // 添加商品到购物车
  const addToCart = (product: any, quantity: number = 1) => {
    console.log('添加购物车的', product);
    
    const existingItem = cartItems.value.find(item => item.id === product.id)
    
    if (existingItem) {
      // 更新现有商品的数量
      existingItem.quantity = quantity
    } else {
      // 添加新商品
      cartItems.value.push({
        ...product,
        materialId: product.id,
        quantity: quantity,
        selected: false
        // id: product.id,
        // remark: product.remark,
        // name: product.name,
        // model: product.model,
        // quantity: quantity,
        // images: product.images,
        // no: product.no,
        // selected: false,
        // unitPrice: product.unitPrice,
        // specs: {
        //   color: '默认颜色',
        //   size: product.model
        // }
      })
    }
    
    // 保存到 localStorage
    saveCartToStorage()
  }
  
  // 从购物车中移除商品
  const removeFromCart = (productId: number) => {
    cartItems.value = cartItems.value.filter(item => item.id !== productId)
    saveCartToStorage()
  }
  
  // 更新购物车中商品的数量
  const updateQuantity = (productId: number, quantity: number) => {
    const item = cartItems.value.find(item => item.id === productId)
    if (item) {
      item.quantity = quantity
      saveCartToStorage()
    }
  }
  
  // 清空购物车
  const clearCart = () => {
    cartItems.value = []
    saveCartToStorage()
  }
  
  // 初始化时从 localStorage 加载数据
  loadCartFromStorage()
  
  return {
    cartItems,
    cartCount,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    loadCartFromStorage,
    saveCartToStorage
  }
}) 