<template>
  <div class="page-container min-h-screen bg-gray-50 overflow-y-auto">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="router.back()" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-bold">我的资产</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <List :request-fn="fetchData" v-slot="{ list }">
        <div class="assets-list">
          <div v-for="asset in list" :key="asset.id" class="asset-item">
            <div class="asset-info">
              <div class="asset-details">
                <div class="asset-header">
                  <h3>{{ asset.name }}</h3>
                  <span class="asset-status" :class="{ 'status-scrapped': asset.status == '3' }">{{ getStatusText(asset.status) }}</span>
                </div>
                <div class="asset-meta">
                  <p class="asset-id"><span class="dot">·</span> 资产编号: {{ asset.no }}</p>
                  <p class="asset-spec"><span class="dot">·</span> 规格型号: {{ asset.model }}</p>
                  <p class="asset-quantity"><span class="dot">·</span> 数量: {{ asset.quantity }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </List>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { myMaterial } from '@/api/equipment'
import List from '@/components/list.vue'
import { dictionary } from '@/api/equipment'
const router = useRouter()

interface Asset {
  id: string
  name: string
  icon: string
  status: string
  spec: string
  quantity: number
}
const statusList = ref([])
const getStatus = () => {
  dictionary({ dictionaryName: 'Assetstatus' }).then(res => {
    console.log('Assetstatus', res);
    statusList.value = res.items
  })
}
getStatus()
const fetchData = async (page: number, pageSize: number) => {
  const params = {
    page,
    pageSize,
    orderBy: JSON.stringify(["-updated_at", "-created_at"])
  }
  let res = await myMaterial(params)
  return {
    list: res.items,
    total: res.total
  };
}
const getStatusText = (value) => {
  console.log('vaaaaaaaaaaaa', value, statusList.value.find(item => item.value == value).label);
  
  let str = value && statusList.value.length ? statusList.value.find(item => item.value == value).label : ''
  return str
}
</script>

<style scoped>
.page-container {
  padding: 0;
  min-height: 100vh;
  background-color: #f5f7fa;
  overflow: hidden;
}

.content-area {
  padding-top: 56px;
  height: 100vh;
  overflow-y: auto;
  padding: 72px 16px 16px;
}

/* 隐藏滚动条 */
.content-area::-webkit-scrollbar {
  display: none;
}

/* Firefox */
.content-area {
  scrollbar-width: none;
}

.assets-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.asset-item {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.asset-info {
  display: flex;
}

.asset-details {
  flex: 1;
}

.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.asset-details h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.asset-status {
  font-size: 12px;
  color: #1890ff;
  background-color: #e6f7ff;
  padding: 2px 8px;
  border-radius: 10px;
}

.status-scrapped {
  color: #ff4d4f;
  background-color: #fff1f0;
}

.asset-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.asset-id, .asset-spec, .asset-quantity {
  margin: 0;
  color: #666;
  font-size: 13px;
}

.dot {
  font-size: 26px;
  line-height: 13px;
  vertical-align: middle;
  display: inline-block;
  transform: translateY(-2px);
}
</style> 