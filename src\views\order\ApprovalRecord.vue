<script setup lang="ts">
import { ref } from 'vue'

interface ApprovalNode {
  type: '抄送人' | '审批人' | '连续多节审批人' | '指定审批人'
  status: 'completed' | 'pending'
  users: {
    name: string
    approvalStatus?: '已抄送' | '已同意' | '已驳回'
    approvalTime?: string
    rejectReason?: string
  }[]
}

const approvalNodes = ref<ApprovalNode[]>([
  {
    type: '抄送人',
    status: 'completed',
    users: [
      { name: '张三三', approvalStatus: '已抄送', approvalTime: '2024-03-20 10:30:25' },
      { name: '李四', approvalStatus: '已抄送', approvalTime: '2024-03-20 10:31:00' },
      { name: '王五', approvalStatus: '已抄送', approvalTime: '2024-03-20 10:32:15' },
      { name: '赵六', approvalStatus: '已抄送', approvalTime: '2024-03-20 10:33:40' },
      { name: '钱七', approvalStatus: '已抄送', approvalTime: '2024-03-20 10:34:20' }
    ]
  },
  {
    type: '连续多节审批人',
    status: 'completed',
    users: [
      { name: '周八', approvalStatus: '已同意', approvalTime: '2024-03-20 11:20:30' },
      { name: '赵十二', approvalStatus: '已驳回', approvalTime: '2024-03-20 14:25:10', rejectReason: '预算超出部门限额，请调整预算后重新提交' }
    ]
  },
  {
    type: '指定审批人',
    status: 'pending',
    users: [
      { name: '吴九' }
    ]
  },
  {
    type: '抄送人',
    status: 'pending',
    users: [
      { name: '郑十' },
      { name: '孙十一' }
    ]
  }
])

// 获取用户名的第一个字作为头像显示
const getFirstChar = (name: string) => name.charAt(0)

// 根据名字生成固定的背景色
const getColorByName = (name: string) => {
  const colors = ['#4299E1', '#48BB78', '#ED8936', '#667EEA', '#ED64A6', '#38B2AC', '#9F7AEA', '#F56565']
  const index = name.charCodeAt(0) % colors.length
  return colors[index]
}
</script>

<template>
  <div class="approval-flow px-4 pr-2 pt-6">
    <h2 class="text-lg mb-6">审批记录</h2>
    <div class="flow-nodes">
      <div v-for="(node, index) in approvalNodes" :key="index" class="flow-node relative">
        <!-- 连接线 -->
        <div 
          v-if="index < approvalNodes.length - 1" 
          class="absolute w-0.5"
          :class="[node.status === 'completed' ? 'bg-blue-500' : 'bg-gray-200']"
          style="left: 11px; top: 32px; height: calc(100% - 36px)"
        ></div>
        
        <!-- 节点内容 -->
        <div class="flex items-start pb-8">
          <!-- 节点序号 -->
          <div class="relative">
            <div 
              class="w-6 h-6 rounded-full text-white flex items-center justify-center text-xs font-normal"
              :class="[node.status === 'completed' ? 'bg-blue-500' : 'bg-gray-400']"
            >
              {{ index + 1 }}
            </div>
          </div>
          
          <!-- 节点信息 -->
          <div class="ml-3 flex-1">
            <div class="text-gray-600 mb-2 text-sm">{{ node.type }}</div>
            <div class="flex flex-col gap-2">
              <div v-for="(user, userIndex) in node.users" :key="userIndex">
                <div class="flex items-center justify-between px-3 py-1.5 relative">
                  <div class="flex items-center">
                    <div 
                      class="w-7 h-7 flex-shrink-0 flex items-center justify-center text-white text-sm font-bold relative"
                      :style="{ backgroundColor: getColorByName(user.name) }"
                    >
                      {{ getFirstChar(user.name) }}
                      <!-- 状态标记 -->
                      <div 
                        v-if="node.status === 'completed'"
                        class="absolute -top-1 -right-1 w-3.5 h-3.5 rounded-full flex items-center justify-center ring-2 ring-white"
                        :class="[user.approvalStatus === '已驳回' ? 'bg-red-500' : 'bg-green-500']"
                      >
                        <template v-if="user.approvalStatus === '已驳回'">
                          <svg class="w-2.5 h-2.5 text-white" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 2a1 1 0 0 1 1 1v6a1 1 0 1 1-2 0V3a1 1 0 0 1 1-1zm0 10a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
                          </svg>
                        </template>
                        <template v-else>
                          <svg class="w-2.5 h-2.5 text-white" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                        </template>
                      </div>
                    </div>
                    <span class="ml-2.5 text-sm">{{ user.name }}</span>
                  </div>
                  <div v-if="user.approvalStatus" class="flex items-center">
                    <span class="text-sm" :class="{
                      'text-blue-500': user.approvalStatus === '已抄送',
                      'text-green-500': user.approvalStatus === '已同意',
                      'text-red-500': user.approvalStatus === '已驳回'
                    }">{{ user.approvalStatus }}</span>
                    <span class="ml-2 text-gray-400 text-sm">{{ user.approvalTime }}</span>
                  </div>
                </div>
                <!-- 驳回理由 -->
                <div v-if="user.approvalStatus === '已驳回' && user.rejectReason" class="mt-1 ml-3 text-sm text-gray-900">
                  {{ user.rejectReason }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.flow-nodes {
  position: relative;
}

.flow-node {
  position: relative;
}
</style> 