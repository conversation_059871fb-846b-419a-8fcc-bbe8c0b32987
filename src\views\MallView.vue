<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useCartStore } from '@/stores/cart'
import { hpMaterial } from '@/api/material'
import List from '@/components/List.vue'
const router = useRouter()
const cartStore = useCartStore()
const baseUrl = import.meta.env.VITE_BASE_URL
// 用于触发购物车数量更新的响应式变量
const cartUpdateTrigger = ref(0)
const listRef = ref(null)
// 计算购物车数量
const cartCount = computed(() => {
  // 使用触发器变量来强制计算属性重新计算
  cartUpdateTrigger.value
  return cartStore.cartCount
})

// 获取商品数量的计算属性
const getItemQuantity = (productId: number) => {
  const item = cartStore.cartItems.find(item => item.id === productId)
  return item ? item.quantity : 0
}

// 跳转到购物车
const goToCart = () => {
  router.push('/cart')
}

// 搜索关键词
const searchKeyword = ref('')

// 重置搜索
const resetSearch = () => {
  searchKeyword.value = ''
}

// 过滤后的商品列表
// const filteredProducts = computed(() => {
//   if (!searchKeyword.value) return products.value
//   const keyword = searchKeyword.value.toLowerCase()
//   return products.value.filter(product =>
//     product.name.toLowerCase().includes(keyword) ||
//     product.model.toLowerCase().includes(keyword)
//   )
// })

// 商品数据
const products = ref([])

const fetchData = async (page: number, pageSize: number) => {
  if (page == 1) {
    products.value = []
  }
  const params = {
    page,
    pageSize,
    query: JSON.stringify([{ status: '0'}, { name__contains: searchKeyword.value }]),
    orderBy: JSON.stringify(["-updated_at", "-created_at"])
  }
  let res = await hpMaterial(params)
  if (res.items && res.items.length) {
    res.items.map(item => {
      products.value.push(item)
    })
  }
  return {
    // list: filteredProducts.value,
    // total: filteredProducts.value.length
    list: res.items,
    total: res.total
  }
}
watch(
  () => searchKeyword.value,
  (newValue, oldValue) => {
    fetchData(1, 10)
  },
  { deep: true }
)
// 跟踪每个商品是否显示数字框
const showQuantityControls = ref<{ [key: number]: boolean }>({})

// 添加到购物车
const addToCart = (product: any, quantity: number = 1) => {
  cartStore.addToCart(product, quantity)
  // 更新购物车触发器变量，强制重新计算角标数字
  cartUpdateTrigger.value++
}

// 显示数量控制
const showQuantityControl = (productId: number) => {
  showQuantityControls.value[productId] = true
  const product = products.value.find(p => p.id === productId)
  if (product) {
    addToCart(product, 1)
  }
}

// 增加数量
const increaseQuantity = (productId: number) => {
  // 获取商品信息
  const product = products.value.find(p => p.id === productId)
  if (product) {
    const currentQuantity = getItemQuantity(productId)
    // 检查是否已达到库存上限
    if (currentQuantity < product.quantity) {
      cartStore.updateQuantity(productId, currentQuantity + 1)
      // 更新购物车触发器变量，强制重新计算角标数字
      cartUpdateTrigger.value++
    } else {
      // 已达到库存上限，显示提示
      showToast({
        message: `已达到库存上限: ${product.quantity}`,
        position: 'top',
        duration: 1500
      })
    }
  }
}

// 减少数量
const decreaseQuantity = (productId: number) => {
  const currentQuantity = getItemQuantity(productId)

  if (currentQuantity > 1) {
    cartStore.updateQuantity(productId, currentQuantity - 1)
    // 更新购物车触发器变量，强制重新计算角标数字
    cartUpdateTrigger.value++
  } else {
    // 当数量为1时，隐藏数字框
    showQuantityControls.value[productId] = false
    // 从购物车中移除该商品
    cartStore.removeFromCart(productId)
    // 更新购物车触发器变量，强制重新计算角标数字
    cartUpdateTrigger.value++
  }
}

// 处理数量变化
const handleQuantityChange = (productId: number, value: string) => {
  const num = parseInt(value)
  if (!isNaN(num) && num > 0) {
    // 获取商品信息
    const product = products.value.find(p => p.id === productId)
    if (product) {
      // 限制数量不超过库存
      if (num > product.quantity) {
        // 如果输入的数量超过库存，显示提示
        showToast({
          message: `数量不能超过库存: ${product.quantity}`,
          position: 'top',
          duration: 1500
        })
      }
      cartStore.updateQuantity(productId, Math.min(num, product.quantity))
      // 更新购物车触发器变量，强制重新计算角标数字
      cartUpdateTrigger.value++
    }
  }
}

// 处理输入事件
const handleInputChange = (productId: number, e: Event) => {
  const target = e.target as HTMLInputElement
  handleQuantityChange(productId, target.value)
}

// 处理输入框失焦
const handleBlur = (productId: number) => {
  // 获取商品信息
  const product = products.value.find(p => p.id === productId)
  if (product) {
    const currentQuantity = getItemQuantity(productId)
    if (currentQuantity < 1) {
      cartStore.updateQuantity(productId, 1)
      // 更新购物车触发器变量，强制重新计算角标数字
      cartUpdateTrigger.value++
    } else if (currentQuantity > product.quantity) {
      // 如果数量超过库存，调整为库存数量
      cartStore.updateQuantity(productId, product.quantity)
      // 更新购物车触发器变量，强制重新计算角标数字
      cartUpdateTrigger.value++
    }
  }
}

// 处理图片加载错误
const handleImageError = (e: Event) => {
  const img = e.target as HTMLImageElement
  img.src = 'https://img.freepik.com/free-photo/product-package-box-isolated_125540-1169.jpg'
}

// fetchData(1, 10)
// 组件挂载时初始化数据
onMounted(() => {
  // cartStore.clearCart()
  // 初始化购物车数据显示
  cartStore.loadCartFromStorage()
  // 恢复已添加商品的UI显示状态
  cartStore.cartItems.forEach((item) => {
    const productId = item.id
    if (productId) {
      showQuantityControls.value[productId] = true
    }
  })
  // 触发角标更新
  cartUpdateTrigger.value++
})
</script>

<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <van-nav-bar fixed title="公物仓" />

    <!-- 固定搜索框 -->
    <div class="fixed-search-container">
      <div class="bg-white px-4 py-2 rounded-lg">
        <div class="flex items-center gap-3">
          <div class="relative flex-1">
            <input
              v-model="searchKeyword"
              type="text"
              placeholder="搜索装备"
              class="w-full px-4 py-2 pl-10 pr-8 bg-gray-50 rounded-full text-sm focus:outline-none"
            >
            <i class="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
            <i
              v-if="searchKeyword"
              class="fas fa-times absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 cursor-pointer"
              @click.stop="resetSearch"
            ></i>
          </div>
          <div
            class="p-2 hover:bg-gray-50 rounded-full cursor-pointer relative"
            @click="goToCart"
          >
            <i class="fas fa-shopping-cart text-gray-600"></i>
            <div
              v-if="cartCount > 0"
              class="absolute -top-0.5 -right-0.5 min-w-[14px] h-3.5 px-1 bg-red-500 rounded-full text-white text-[10px] flex items-center justify-center"
            >
              {{ cartCount }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <List ref="listRef" :request-fn="fetchData" v-slot="{ list }">
      <div class="content-container">
        <!-- 商品列表 -->
        <div class="space-y-2">
          <!-- 空结果显示 -->
          <!-- <div v-if="list.length === 0" class="flex flex-col items-center justify-center py-12">
            <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
            <p class="text-gray-500">{{ products.length === 0 ? '暂无商品' : '没有找到相关商品' }}</p>
          </div> -->

          <div
            v-for="product in list"
            :key="product.id"
            class="bg-white p-3 rounded-lg"
          >
            <div class="flex">
              <!-- 商品图片 -->
              <div class="w-24 h-24 bg-gray-100 rounded-lg mr-3 overflow-hidden flex items-center justify-center">
                <img
                  :src="baseUrl + product.images"
                  :alt="product.name"
                  class="w-full h-full object-contain"
                  @error="handleImageError"
                  :onerror="`this.onerror=null;this.src='https://img.freepik.com/free-photo/product-package-box-isolated_125540-1169.jpg'`"
                >
              </div>

              <!-- 商品信息 -->
              <div class="flex-1 min-w-0">
                <h3 class="text-base font-medium text-gray-900 mb-1.5 line-clamp-2">{{ product.name }}</h3>
                <div class="text-sm text-gray-500 mb-1">资产编号：{{ product.no }}</div>
                <div class="text-sm text-gray-500 mb-1">规格型号：{{ product.model }}</div>
                <div class="flex items-center justify-between pt-2">
                  <div class="text-sm">
                    <span :class="[product.quantity > 20 ? 'text-green-600' : 'text-orange-500']">
                      库存：{{ product.quantity }}
                    </span>
                  </div>
                  <!-- 加购按钮/数量控制 -->
                  <div class="flex flex-col justify-end ml-3">
                    <template v-if="!showQuantityControls[product.id]">
                      <button
                        @click.stop="showQuantityControl(product.id)"
                        class="w-5 h-5 flex items-center justify-center bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors"
                      >
                        <i class="fas fa-plus text-[10px]"></i>
                      </button>
                    </template>
                    <template v-else>
                      <div class="flex items-center gap-1">
                        <button
                          @click="decreaseQuantity(product.id)"
                          class="w-5 h-5 flex items-center justify-center bg-gray-100 text-gray-600 rounded hover:bg-gray-200 transition-colors"
                        >
                          <i class="fas fa-minus text-[10px]"></i>
                        </button>
                        <input
                          type="number"
                          :value="getItemQuantity(product.id)"
                          @input="e => handleInputChange(product.id, e)"
                          @blur="handleBlur(product.id)"
                          class="w-10 h-5 text-center text-sm border rounded bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          min="1"
                        >
                        <button
                          @click="increaseQuantity(product.id)"
                          class="w-5 h-5 flex items-center justify-center bg-gray-100 text-gray-600 rounded hover:bg-gray-200 transition-colors"
                        >
                          <i class="fas fa-plus text-[10px]"></i>
                        </button>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </List>
  </div>
</template>

<style scoped>
.page-container {
  width: 100%;
  height: 100%;
  padding: 48px 16px 20px;
}

.fixed-search-container {
  position: fixed;
  top: 48px; /* 导航栏高度 */
  left: 0;
  right: 0;
  z-index: 10;
  padding: 0 16px;
}

.content-container {
  margin-top: 60px; /* 为固定搜索框留出空间 */
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
  width: 0;
}

.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
  overflow-y: scroll;
  overflow-x: hidden;
}

/* 添加一个通用的滚动条隐藏样式 */
* {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

*::-webkit-scrollbar {
  display: none;
  width: 0;
}
</style>
