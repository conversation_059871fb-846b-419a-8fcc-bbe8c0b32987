<template>
  <div class="page-container bg-gray-50 min-h-screen flex flex-col">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-4">
        <button @click="goBack" class="w-9 h-9 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition">
          <i class="fas fa-arrow-left text-gray-600"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-800">{{ mode === 'add' ? '新增验收物品' : mode === 'view' ? '查看验收物品' : '编辑验收物品' }}</h1>
        <div class="w-9"></div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="flex-1 overflow-y-auto">
      <div class="content-area pt-16 px-2 pb-24">
        <div class="bg-white rounded-xl p-6 shadow-sm">
          <el-form 
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="90px"
            class="space-y-6"
          >
            <el-form-item prop="type" :rules="formRules.type" label="资产类型" key="1">
              <el-radio-group v-model="formData.type">
                <el-radio value="fixed">固定资产</el-radio>
                <el-radio value="intangible">无形资产</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item prop="name" :rules="formRules.name" label="物品名称">
              <el-input v-model="formData.name" placeholder="请输入物品名称" key="2"/>
            </el-form-item>

            <!-- 固定资产特有字段 -->
            <template v-if="formData.type === 'fixed'">
              <el-form-item prop="brand" :rules="formRules.brand" label="品牌" key="3">
                <el-input v-model="formData.brand" placeholder="请输入品牌" />
              </el-form-item>

              <el-form-item prop="model" :rules="formRules.model" label="规格型号" key="4">
                <el-input v-model="formData.model" placeholder="请输入规格型号" />
              </el-form-item>

              <el-form-item prop="address" :rules="formRules.address" label="存放地点" key="5">
                <el-input v-model="formData.address" placeholder="请输入存放地点" />
              </el-form-item>
            </template>

            <el-form-item prop="unitPrice" :rules="formRules.unitPrice" label="单价" key="6">
              <div class="flex items-center">
                <el-input-number v-model="formData.unitPrice" :min="0" placeholder="请输入单价" class="flex-1" controls-position="right" />
                <span class="ml-2">元</span>
              </div>
            </el-form-item>

            <el-form-item prop="quantity" :rules="formRules.quantity" label="数量" key="7">
              <el-input-number v-model="formData.quantity" :min="1" placeholder="请输入数量" class="w-full" controls-position="right" />
            </el-form-item>

            <el-form-item prop="unit" :rules="formRules.unit" label="单位" key="8">
              <el-input v-model="formData.unit" placeholder="请输入单位" />
            </el-form-item>

            <el-form-item prop="source" :rules="formRules.source" label="取得方式" key="9">
              <el-select v-model="formData.source" placeholder="请选择取得方式" class="w-full" @change="changeSource">
                <el-option v-for="item in gainingOptions" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-form-item>

            <el-form-item prop="departmentId" label="管理/使用部门" key="10">
              <!-- <el-select v-model="formData.departmentId" placeholder="请选择管理/使用部门" class="w-full">
                <el-option label="行政部" value="行政部" />
                <el-option label="IT部" value="IT部" />
                <el-option label="财务部" value="财务部" />
                <el-option label="人力资源部" value="人力资源部" />
              </el-select> -->
              <DepartmentPicker v-model="formData.departmentId" :department-name="formData.departmentName" :hide-title="true" :clearabled="true" @finish="handleDepartmentFinish" />
            </el-form-item>
            <!-- <p>请选择部门</p> -->
            <el-form-item prop="userId" label="管理/使用人" key="11" :rules="[{ required: formData.departmentId ? true : false, message: '请选择管理/使用人', trigger: 'blur' }]">
              <el-select v-model="formData.userId" placeholder="请选择管理/使用人" class="w-full" @change="userChange" clearable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.name" :value="item.userId"/>
              </el-select>
            </el-form-item>
            <el-form-item label="资产图片" key="12" :rules="formRules.images" prop="images">
              <ImageUpload v-model="formData.images" :editable="mode === 'view' ? false : true"/>
            </el-form-item>
            <el-form-item label="型号图片" key="14" :rules="formRules.modelImages" prop="modelImages">
              <ImageUpload v-model="formData.modelImages" :editable="mode === 'view' ? false : true"/>
            </el-form-item>
            <el-form-item label="附件" key="13">
              <NewFileUpload v-model="formData.checkFiles" :editable="mode === 'view' ? false : true"/>
              <p v-if="!formData.checkFiles.length && mode === 'view'">暂无附件</p>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div v-if="mode !== 'view'" class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-4 py-4 shadow-lg">
      <div class="max-w-700px mx-auto">
        <el-button type="primary" class="!h-12 w-full !text-base !font-medium" @click="handleSubmit" :loading="submitting">
          提交
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useAcceptanceStore } from '@/stores/acceptance'
import DepartmentPicker from '@/components/DepartmentPicker.vue'
import { hpWxworkUser, dictionary } from '@/api/equipment'
import NewFileUpload from '@/components/newFileUpload.vue'
import ImageUpload from '@/components/ImageUpload.vue'
import { nanoid } from 'nanoid';

const router = useRouter()
const route = useRoute()
const formRef = ref<FormInstance>()
const submitting = ref(false)
const store = useAcceptanceStore()
const userOptions = ref([])
const gainingOptions = ref([])
const mode = ref('add')
const fromMode = ref(route.query.fromMode as string || 'add')
// 表单数据
interface FormData {
  randId: string
  checkFiles: string[]
  images: string[]
  modelImages: string[]
  name: string
  quantity: number
  unit: string
  type: 'fixed' | 'intangible'
  brand?: string
  model?: string
  unitPrice: number
  totalPrice: number
  source: string
  sourceName: string
  departmentId: number
  departmentName: string
  userId: string
  userName: string
  address?: string
  photos: string[]
}

const formData = ref<FormData>({
  randId: '',
  checkFiles: [],
  images: [],
  modelImages: [],
  name: '',
  quantity: 1,
  unit: '',
  type: 'fixed',
  unitPrice: undefined as unknown as number,
  totalPrice: 0,
  source: '',
  departmentId: 0,
  departmentName: '',
  userId: '',
  photos: [],
  sourceName: '',
  userName: '',
  brand: '',
  model: ''
})
const getUser = () => {
  if (!formData.value.departmentId) { 
    userOptions.value = []
  }
  let params = {
    nopaging: true
  }
  if (formData.value.departmentId) {
    params.query = JSON.stringify([{ departmentId: formData.value.departmentId.toString() }])
  }
  hpWxworkUser(params).then(res => {
    userOptions.value = res.items
  })
}
const getData = () => {
  dictionary({ dictionaryName: 'check_accept_order_source' }).then(res => {
    gainingOptions.value = res.items
  })
}
const changeSource = (data) => {
  formData.value.sourceName = gainingOptions.value.find(item => item.value == data).label
}
const userChange = (data) => {
  if (data) {
    formData.value.userName = userOptions.value.find(item => item.userId == data).name
  }
}
// 表单验证规则
const formRules: FormRules = {
  name: [{ required: true, message: '请输入物品名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
  brand: [{ required: true, message: '请输入品牌', trigger: 'blur' }],
  model: [{ required: true, message: '请输入规格型号', trigger: 'blur' }],
  unitPrice: [{ required: true, message: '请输入单价', trigger: 'blur' }],
  quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
  source: [{ required: true, message: '请选择取得方式', trigger: 'change' }],
  images: [{ required: true, message: '请选择图片', trigger: 'change' }],
  modelImages: [{ required: true, message: '请选择图片', trigger: 'change' }],
  // departmentId: [{ required: true, message: '请选择管理部门', trigger: 'change' }],
  // userId: [{ required: true, message: '请输入管理人', trigger: 'blur' }],
  address: [{ required: true, message: '请输入存放地点', trigger: 'blur' }]
}
const handleDepartmentFinish = (data) => {
  formData.value.departmentName = data.displayText
  formData.value.userId = ''
  formData.value.userName = ''
  getUser()
}
watch(() => formData.value.departmentId, (newVal) => {
  if (!newVal) {
    formData.value.userId = ''
    formData.value.userName = ''
    userOptions.value = []
  }
})
const goBack = () => {
  router.push({
    path: '/acceptance-registration',
    query: {
      mode: fromMode.value
    }
  })
}
// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      // submitting.value = true
      // 计算总金额
      calculateTotalAmount()
      let randID = nanoid()
      let data = {}
      if (formData.value.type === 'fixed') {
        data = {
          randID: formData.value.randID ? formData.value.randID : randID,
          checkFiles: formData.value.checkFiles && formData.value.checkFiles.length ? formData.value.checkFiles.join(',') : '',
          images: formData.value.images && formData.value.images.length ? formData.value.images.join(',') : '',
          modelImages: formData.value.modelImages && formData.value.modelImages.length ? formData.value.modelImages.join(',') : '',
          brand: formData.value.brand,
          departmentId: formData.value.departmentId,
          departmentName: formData.value.departmentId ? formData.value.departmentName : '',
          address: formData.value.address,
          model: formData.value.model,
          name: formData.value.name,
          quantity: formData.value.quantity,
          source: formData.value.source,
          sourceName: formData.value.sourceName,
          totalPrice: formData.value.totalPrice,
          type: formData.value.type,
          unit: formData.value.unit,
          unitPrice: formData.value.unitPrice,
          userId: formData.value.userId,
          userName: formData.value.userId ? formData.value.userName : ''
        }
      } else {
        data = {
          randID: formData.value.randID ? formData.value.randID : randID,
          checkFiles: formData.value.checkFiles && formData.value.checkFiles.length ? formData.value.checkFiles.join(',') : '',
          images: formData.value.images && formData.value.images.length ? formData.value.images.join(',') : '',
          modelImages: formData.value.modelImages && formData.value.modelImages.length ? formData.value.modelImages.join(',') : '',
          departmentId: formData.value.departmentId,
          departmentName: formData.value.departmentId ? formData.value.departmentName : '',
          name: formData.value.name,
          quantity: formData.value.quantity,
          source: formData.value.source,
          totalPrice: formData.value.totalPrice,
          type: formData.value.type,
          unit: formData.value.unit,
          unitPrice: formData.value.unitPrice,
          userId: formData.value.userId,
          userName: formData.value.userId ? formData.value.userName : ''
        }
      }
      if (!formData.value.departmentId || !formData.value.userId) {
        data.status = 0
      }
      // 将数据保存到 store
      const itemData = { ...data };
      // 直接添加到store的物品列表
      if (mode.value === 'edit') {
        store.updateItem(itemData)
      } else {
        store.addItem(itemData);
      }
      
      // 返回上一页
      ElMessage.success('添加物品成功')
      if (fromMode.value === 'edit') {
        router.push({
          path: '/acceptance-registration',
          query: {
            mode: 'edit'
          }
        })
      } else {
        router.push('/acceptance-registration')
      }
    }
  })
}
getData()
getUser()
// 计算总金额
const calculateTotalAmount = () => {
  formData.value.totalPrice = formData.value.unitPrice * formData.value.quantity
}

// 监听单价和数量变化
watch([() => formData.value.unitPrice, () => formData.value.quantity], () => {
  calculateTotalAmount()
})
onMounted(() => {
  if (route.query.data) {
    mode.value = route.query.mode as string
    let data = JSON.parse(route.query.data)
    data.images = data.images ? data.images.split(',') : []
    data.modelImages = data.modelImages ? data.modelImages.split(',') : []
    data.checkFiles = data.checkFiles ? data.checkFiles.split(',') : []
    formData.value = { ...data }
  }
})
</script>

<style scoped>
.content-area {
  min-height: calc(100vh - 5rem);
  max-width: 700px;
  margin: 0 auto;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  flex-wrap: nowrap;
  align-items: flex-start;
}

:deep(.el-form-item__label) {
  @apply text-gray-700 font-medium whitespace-nowrap;
  font-size: 14px;
  padding: 0 8px 0 0;
  line-height: 32px;
  height: 32px;
}

:deep(.el-form-item__content) {
  flex: 1;
  min-width: 0;
  display: block;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__inner) {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

:deep(.el-input__wrapper:hover),
:deep(.el-textarea__inner:hover) {
  border-color: #d1d5db;
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-textarea__inner:focus) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

:deep(.el-radio) {
  margin-right: 20px;
}

:deep(.el-radio__label) {
  font-size: 14px;
  padding-left: 8px;
}

.upload-area {
  width: 100%;
}

:deep(.el-upload) {
  width: auto;
}

:deep(.el-upload-list--picture) {
  margin-top: 8px;

  .el-upload-list__item {
    padding: 8px;
    border-radius: 6px;
    border: 1px solid var(--el-border-color);
    background-color: var(--el-fill-color-blank);
    transition: all 0.3s;
    margin-bottom: 8px;
    position: relative;
    
    &:hover {
      border-color: var(--el-color-primary);
      background-color: var(--el-fill-color-light);
    }

    .el-upload-list__item-thumbnail {
      object-fit: cover;
      border-radius: 4px;
    }

    .el-upload-list__item-actions {
      display: none;
    }

    .el-upload-list__item-delete {
      position: absolute;
      top: 2px;
      right: 2px;
      font-size: 18px;
      color: #fff;
      opacity: 1;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      padding: 4px;
      transform: none;
      cursor: pointer;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      transition: all 0.3s;

      &:hover {
        background-color: rgba(0, 0, 0, 0.7);
      }
    }
  }
}

:deep(.el-upload-list__item-status-label) {
  display: none;
}

:deep(.el-button) {
  height: 32px;
  padding: 0 16px;
  font-size: 14px;
}

.page-container {
  /* height: 100vh;
  overflow: hidden; */
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}

/* 隐藏滚动条但保持滚动功能 */
.flex-1.overflow-y-auto {
  scrollbar-width: none;  /* Firefox */
  -ms-overflow-style: none;  /* IE and Edge */
  &::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }
}
</style> 