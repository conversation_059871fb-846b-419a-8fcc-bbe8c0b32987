<template>
  <div class="order-list">
    
    <List :request-fn="fetchData" v-slot="{ list }">
      <div class="order-cards">
        <div v-for="order in list" :key="order.id" class="order-card" @click="wwView(order)">
          <div class="order-header">
            <span class="order-name">{{ order.openSpName }}</span>
            <div class="order-arrow">
              <el-icon class="arrow-icon"><ArrowRight /></el-icon>
            </div>
          </div>
          <div class="order-info">
            <div class="info-item">
              <span class="label">申请人：</span>
              <span>{{ order.applyUserName }}</span>
            </div>
            <div class="info-item">
              <span class="label">申请时间：</span>
              <span>{{ dayjs(order.applyTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
            </div>
          </div>
        </div>
      </div>
    </List>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import List from '@/components/List.vue'
import { approvalList } from '@/api/approval'
import { ArrowRight } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import * as ww from '@wecom/jssdk'

const props = defineProps(['orderList', 'status'])
const orderList = ref(props.orderList)
console.log('进入组件', orderList.value);

const router = useRouter()
const wwView = (data:Order) => {
  const id = data.thirdNo.split('-')[1]
  const url = import.meta.env.VITE_URL
  let link
  let path
  if (data.spType == 'SLSQ') {
    // 申领
    path = '/apply-form-detail'
    link = `${url}#/apply-form-detail?id=${id}`
  } else if (data.spType == 'BFSQ') {
    // 报废
    path = '/scrap-application-detail'
    link = `${url}#/scrap-application-detail?id=${id}`
  } else if (data.spType == 'YSSQ') {
    // 预算
    path = '/budget-application-detail'
    link = `${url}#/budget-application-detail?id=${id}`
  } else if (data.spType == 'DBSQ') {
    // 调拨
    path = '/transfer-application-detail'
    link = `${url}#/transfer-application-detail?id=${id}`
  } else if (data.spType == 'THSQ') {
    // 退还
    path = '/return-application-detail'
    link = `${url}#/return-application-detail?id=${id}`
  } else {
    console.log('未知的单据类型:', data.spType)
  }
  console.log('link', link);
  if (import.meta.env.VITE_ENV === 'dev'){
    router.push({
      path,
      query: {
        id
      }
    })
    return
  }
  ww.thirdPartyOpenPage({
      oaType: ww.OAType.view_approval,
      templateId: data.openTemplateId,
      thirdNo: data.thirdNo,
      extData: {
        fieldList: [
          {
            title: '申请类型',
            type: ww.OaExtDataType.text,
            value: data.openSpName,
          },
          {
            title: '申请部门',
            type: ww.OaExtDataType.text,
            value: data.applyUserParty,
          },
          {
            title: '申请人',
            type: ww.OaExtDataType.text,
            value: data.applyUserName,
          },
          {
            title: '申请时间',
            type: ww.OaExtDataType.text,
            value: dayjs(data.applyTime).format('YYYY-MM-DD HH:mm:ss'),
          },
          {
            type: ww.OaExtDataType.link,
            title: '详细内容',
            value: link
          }
        ]
      },
      success: () => {
        console.log( Date.now(), '查看成功')
      },
      fail: () => {
        console.log( Date.now(), '查看失败')
      },
      complete: () => {
        console.log( Date.now(), '查看完成')
      }
    })
}

const fetchData = async (page: number, pageSize: number) => {
  const params = {
    page,
    pageSize,
    query: JSON.stringify({ open_sp_status: [1] }),
    orderBy: JSON.stringify(["-updated_at", "-created_at"])
  }
  let res = await approvalList(params)
  return {
    list: res.items,
    total: res.total
  };

}
</script>

<style scoped>
.order-list {
  padding: 16px 16px 20px;
  background-color: #f5f7fa;
  height: 100%;
}

.order-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-card {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  position: relative;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.order-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.order-info {
  color: #606266;
  font-size: 14px;
}

.info-item {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.info-item .label {
  color: #909399;
  margin-right: 8px;
}

.order-arrow {
  position: absolute;
  right: 16px;
  top: 16px;
  color: #c0c4cc;
  display: flex;
  align-items: center;
}

.arrow-icon {
  font-size: 16px;
}
</style> 