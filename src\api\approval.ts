import { defHttp } from "../axios";

export const getApprovalList = (url: string) => defHttp.get(
  {
    url,
    headers: {
      ignoreCancelToken: true,

    },
  }
);

export const agreeApprovalList = (id: string, data: { approveRecordQueueId?: string; id?: string; opinion?: string; remark?: string; sign?: string; processResult: 'Approved' | 'Rejected' }) => defHttp.post(
  {
    url: `/approve/v1/appApproveRecord/${id}/approval`,
    data,
    headers: {
      ignoreCancelToken: true,
    },
  }
);

export const cancelApprovalList = (id: string, data: { id?: string; }) => defHttp.post(
  {
    url: `/approve/v1/appApproveRecord/${id}/cancel`,
    data,
    headers: {
      ignoreCancelToken: true,
    },
  }
);


// 创建预算申请
export const createBudgetOrderApproval = (data: unknown) => defHttp.post(
  {
    url: '/hp/v1/hpBudgetOrder',
    data,
    headers: {
      ignoreCancelToken: true,
    },
  }
);

// 调拨申请
export const hpTransferOrder = (data) => defHttp.post(
  {
    url: `/hp/v1/hpTransferOrder`,
    data
  }
);

// 报废申请
export const hpDiscardOrder = (data) => defHttp.post(
  {
    url: `/hp/v1/hpDiscardOrder`,
    data
  }
);

// 退还申请
export const hpReturnOrder = (data) => defHttp.post(
  {
    url: `/hp/v1/hpReturnOrder`,
    data
  }
);
interface Data {
  nopaging?: boolean,
  page?: number;
  pageSize?: number;
  query?: string;
  orderBy?: string;
}
// 获取审批单列表
export const approvalList = (data?:Data) => defHttp.get(
  {
    url: `/workwx/v1/approval/list`,
    params: data
  }
);

// 我提交的
export const myList = (data?:Data) => defHttp.get(
  {
    url: `/workwx/v1/approval/my/list`,
    params: data
  }
);
// 抄送我的
export const copyList = (data?:Data) => defHttp.get(
  {
    url: `/workwx/v1/approval/cc/list`,
    params: data
  }
);
// 查看退还详情
export const returnOrderDetail = (id?:string) => defHttp.get(
  {
    url: `/hp/v1/hpReturnOrder/${id}`
  }
);

// 查看调拨详情
export const transferOrderDetail = (id?:string) => defHttp.get(
  {
    url: `/hp/v1/hpTransferOrder/${id}`
  }
);

// 查看预算详情
export const budgetOrderDetail = (id?:string) => defHttp.get(
  {
    url: `/hp/v1/hpBudgetOrder/${id}`
  }
);

// 查看报废详情
export const discardOrderDetail = (id?:string) => defHttp.get(
  {
    url: `/hp/v1/hpDiscardOrder/${id}`
  }
);

// 查看申领详情
export const claimOrderDetail = (id?:string) => defHttp.get(
  {
    url: `/hp/v1/hpClaimOrder/${id}`
  }
);