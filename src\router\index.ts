import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'
import { getUserInfo } from '@/utils/userInfo'
const router = createRouter({
  history: createWebHashHistory('/9523/'),
  routes: [
    // {
    //   path: '/',
    //   redirect: '/tasks'
    // },
    {
      path: '/',
      name: 'tasks',
      component: () => import('../views/TasksView.vue'),
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/login.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    {
      path: '/mall',
      name: 'mall',
      component: () => import('../views/MallView.vue'),
    },
    {
      path: '/cart',
      name: 'cart',
      component: () => import('../views/CartView.vue'),
    },
    {
      path: '/apps',
      name: 'apps',
      component: () => import('../views/AppsView.vue'),
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
    },
    // 任务详情页
    {
      path: '/task/:id',
      name: 'taskDetail',
      component: () => import('../views/TaskDetailView.vue'),
    },
    // 商品详情页
    {
      path: '/product/:id',
      name: 'product-detail',
      component: () => import('../views/ProductDetailView.vue'),
    },
    // 设置页面
    {
      path: '/settings',
      name: 'settings',
      component: () => import('../views/SettingsView.vue'),
    },
    // 个人信息
    {
      path: '/profile-edit',
      name: 'profile-edit',
      component: () => import('../views/ProfileEditView.vue'),
    },
    // 应用列表页面
    {
      path: '/applications',
      name: 'applications',
      component: () => import('../views/ApplicationsView.vue'),
    },
    // 预算申请页面
    {
      path: '/budget-application',
      name: 'budgetApplication',
      component: () => import('../views/BudgetApplicationView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 借用申请页面
    {
      path: '/borrow-form',
      name: 'borrowForm',
      component: () => import('../views/BorrowFormView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    {
      path: '/approval-flow',
      name: 'approvalFlow',
      component: () => import('../views/order/ApprovalFlow.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 资产申领页面
    {
      path: '/apply-form',
      name: 'applyForm',
      component: () => import('../views/ApplyFormView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 预算统计页面
    {
      path: '/budget-statistics',
      name: 'budgetStatistics',
      component: () => import('../views/BudgetStatisticsView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 预购登记页面
    {
      path: '/preorder-registration',
      name: 'preorderRegistration',
      component: () => import('../views/PreorderRegistrationView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 预购登记记录页面
    {
      path: '/preorder-records',
      name: 'preorderRecords',
      component: () => import('../views/PreorderRecordsView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 维修登记记录页面
    {
      path: '/repair-records',
      name: 'repairRecords',
      component: () => import('../views/RepairRecordsView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 维修登记页面
    {
      path: '/repair-registration',
      name: 'repairRegistration',
      component: () => import('../views/RepairRegistrationView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 单据列表页面
    {
      path: '/order-list',
      name: 'orderList',
      component: () => import('../views/order/OrderList.vue'),
      meta: {
        title: '单据列表'
      }
    },
    // 审批记录页面
    {
      path: '/approval-record',
      name: 'approvalRecord',
      component: () => import('../views/order/ApprovalRecord.vue'),
      meta: {
        title: '审批记录',
        hideBottomNav: true
      }
    },
    // 无状态单据列表页面
    {
      path: '/order-list-no-status',
      name: 'orderListNoStatus',
      component: () => import('../views/order/OrderListNoStatus.vue'),
      meta: {
        title: '单据列表',
        hideBottomNav: true
      }
    },
    {
      path: '/my-approval',
      name: 'myApproval',
      component: () => import('../views/MyApproval.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    {
      path: '/my-copy',
      name: 'myCopy',
      component: () => import('../views/MyCopy.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    {
      path: '/my-submit',
      name: 'mySubmit',
      component: () => import('../views/MySubmit.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    {
      path: '/my-assets',
      name: 'myAssets',
      component: () => import('../views/MyAssetsView.vue'),
      meta: {
        title: '我的资产',
        hideBottomNav: true
      }
    },
    {
      path: '/my-inventory',
      name: 'myInventory',
      component: () => import('../views/InventoryPlan.vue'),
      meta: {
        title: '我的盘点',
        hideBottomNav: true
      }
    },
    // 验收登记记录页面
    {
      path: '/acceptance-records',
      name: 'acceptanceRecords',
      component: () => import('../views/AcceptanceRecordsView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 验收登记页面
    {
      path: '/acceptance-registration',
      name: 'acceptanceRegistration',
      component: () => import('../views/AcceptanceRegistrationView.vue'),
      meta: {
        title: '验收登记',
        hideBottomNav: true
      }
    },
    // 验收物品页面
    {
      path: '/acceptance-item',
      name: 'acceptanceItem',
      component: () => import('../views/AcceptanceItemView.vue'),
      meta: {
        title: '新增验收物品',
        hideBottomNav: true
      }
    },
    // 调拨申请页面
    {
      path: '/transfer-application',
      name: 'transferApplication',
      component: () => import('../views/TransferApplicationView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 调拨申请审核页面
    {
      path: '/transfer-application-review',
      name: 'transferApplicationReview',
      component: () => import('../views/TransferApplicationReviewView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 调拨申请详情页面
    {
      path: '/transfer-application-detail',
      name: 'transferApplicationDetail',
      component: () => import('../views/TransferApplicationDetailView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 报废申请审核页面
    {
      path: '/scrap-application-review',
      name: 'scrapApplicationReview',
      component: () => import('../views/ScrapApplicationReviewView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 报废申请详情页面
    {
      path: '/scrap-application-detail',
      name: 'scrapApplicationDetail',
      component: () => import('../views/ScrapApplicationDetailView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 资产申领审核页面
    {
      path: '/apply-form-review',
      name: 'applyFormReview',
      component: () => import('../views/ApplyFormReviewView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 资产申领详情页面
    {
      path: '/apply-form-detail',
      name: 'applyFormDetail',
      component: () => import('../views/ApplyFormDetail.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 资产选择页面
    {
      path: '/asset-selection',
      name: 'AssetSelection',
      component: () => import('../views/order/AssetSelectionView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    {
      path: '/asset-selector-transfer',
      name: 'assetSelectorTransfer',
      component: () => import('../views/order/AssetSelectorForTransfer.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    {
      path: '/asset-selector-scrap',
      name: 'assetSelectorScrap',
      component: () => import('../views/order/AssetSelectorForScrap.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    {
      path: '/inventory-plan',
      name: 'inventoryPlan',
      component: () => import('../views/InventoryPlan.vue')
    },
    {
      path: '/inventory-plan',
      name: 'inventoryPlanDetail',
      component: () => import('../views/InventoryPlanDetail.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 报废申请页面
    {
      path: '/scrap-application',
      name: 'scrapApplication',
      component: () => import('../views/ScrapApplicationView.vue'),
      meta: {
        hideBottomNav: true
      }
    },
    // 审批结论页面
    {
      path: '/approval-conclusion/:id',
      name: 'approvalConclusion',
      component: () => import('../views/order/ApprovalConclusion.vue'),
      meta: {
        title: '审批结论',
        hideBottomNav: true
      }
    },
    {
      path: '/budget-application-review',
      name: 'BudgetApplicationReview',
      component: () => import('@/views/BudgetApplicationReviewView.vue'),
      meta: {
        title: '预算申请审核',
        hideBottomNav: true
      }
    },
    // 预算申请单详情页面
    {
      path: '/budget-application-detail',
      name: 'budgetApplicationDetail',
      component: () => import('@/views/BudgetApplicationDetailView.vue'),
      meta: {
        title: '预算申请详情',
        hideBottomNav: true
      }
    },
    // 退还申请页面
    {
      path: '/return-application',
      name: 'returnApplication',
      component: () => import('@/views/ReturnApplicationView.vue'),
      meta: {
        title: '资产退还申请',
        hideBottomNav: true
      }
    },
    // 退还审批页面
    {
      path: '/return-application-review/:id',
      name: 'returnApplicationReview',
      component: () => import('@/views/ReturnApplicationReviewView.vue'),
      meta: {
        title: '资产退还审核',
        hideBottomNav: true
      }
    },
    // 退还单据详情页面
    {
      path: '/return-application-detail',
      name: 'returnApplicationDetail',
      component: () => import('@/views/ReturnApplicationDetailView.vue'),
      meta: {
        title: '退还单据详情',
        hideBottomNav: true
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('../views/NotFoundView.vue')
    }
  ],
  scrollBehavior(to, from, savedPosition) {
    // 始终滚动到页面顶部
    return { top: 0 }
  }
})
router.beforeEach((to, from, next) => {
  if (to.name !== 'Login' && !getUserInfo()) next({ name: 'Login' })
  else next()
  // next()
})
export default router
