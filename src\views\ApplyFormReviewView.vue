<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-medium">资产申领审核</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <!-- 申请表单 -->
    <div class="form-content">
      <form class="space-y-6">
        <!-- 基本信息 -->
        <BasicInfoCard
          :applicant="formData.applicant"
          :department="formData.department"
          :apply-time="formData.applyTime"
        />

        <!-- 事由 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              申领事由
            </label>
            <div class="w-full p-2 bg-gray-50 rounded-md min-h-[100px]">
              {{ formData.purpose }}
            </div>
          </div>
        </div>

        <!-- 申领明细 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <div class="flex justify-between items-center mb-4">
              <label class="block text-sm font-medium text-gray-700">
                申领明细
              </label>
            </div>
            
            <div v-if="formData.applyItems.length === 0" class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg">
              <i class="fas fa-inbox text-3xl mb-2"></i>
              <p>暂无申领物品</p>
            </div>
            
            <div v-else class="space-y-3">
              <div 
                v-for="item in formData.applyItems" 
                :key="item.id" 
                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-900 mb-2">{{ item.name }}</div>
                  <div class="text-xs text-gray-500 mb-1.5 flex items-center">
                    <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                    <span>资产编号：{{ item.code }}</span>
                  </div>
                  <div class="text-xs text-gray-500 mb-1.5 flex items-center">
                    <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                    <span>规格型号：{{ item.model }}</span>
                  </div>
                  <div class="text-xs text-gray-500 flex items-center">
                    <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                    <span>数量：1件</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 备注 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              备注
            </label>
            <div class="w-full p-2 bg-gray-50 rounded-md">
              {{ formData.remark || '无' }}
            </div>
          </div>
        </div>

        <!-- 附件列表 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <div class="flex justify-between items-center mb-4">
              <label class="text-sm font-medium text-gray-700">附件列表</label>
            </div>

            <div v-if="formData.attachments.length === 0" class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg">
              <i class="fas fa-cloud-upload-alt text-3xl mb-2"></i>
              <p>暂无附件</p>
            </div>

            <div v-else class="space-y-3">
              <div 
                v-for="(file, index) in formData.attachments" 
                :key="index"
                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div class="flex-1 min-w-0">
                  <div class="flex items-center">
                    <i class="fas fa-file text-gray-400 mr-2"></i>
                    <span class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</span>
                  </div>
                  <div class="text-xs text-gray-500 mt-1">{{ formatFileSize(file.size) }}</div>
                </div>
                <a 
                  href="#"
                  class="text-blue-600 hover:text-blue-700"
                  @click.prevent="downloadFile(file)"
                >
                  <i class="fas fa-download"></i>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- 审批流程 -->
        <div class="bg-white rounded-lg shadow-sm">
          <ApprovalFlow />
        </div>
      </form>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
      <div class="flex space-x-4">
        <button 
          type="button"
          @click="handleReject"
          class="flex-1 px-4 py-2 border border-red-600 rounded-md text-red-600 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          :disabled="isSubmitting"
        >
          {{ isSubmitting ? '处理中...' : '驳回' }}
        </button>
        <button 
          type="button"
          @click="handleApprove"
          class="flex-1 px-4 py-2 border border-transparent rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          :disabled="isSubmitting"
        >
          {{ isSubmitting ? '处理中...' : '通过' }}
        </button>
      </div>
    </div>

    <!-- 审批结论弹窗 -->
    <el-dialog
      v-model="showApprovalConclusion"
      :title="approvalType === 'approve' ? '审批通过' : '审批驳回'"
      width="95%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      class="approval-dialog"
      destroy-on-close
      top="10vh"
    >
      <ApprovalConclusion
        :type="approvalType"
        @confirm="handleApprovalConfirm"
        @cancel="handleApprovalCancel"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElDialog } from 'element-plus'
import 'element-plus/es/components/dialog/style/css'
import ApprovalFlow from '@/views/order/ApprovalFlow.vue'
import ApprovalConclusion from '@/views/order/ApprovalConclusion.vue'
import BasicInfoCard from '@/views/order/BasicInfoCard.vue'

const router = useRouter()
const route = useRoute()

// 表单数据接口定义
interface ApplyItem {
  id: number;
  name: string;
  model: string;
  image: string;
  quantity: number;
  warehouse: string;
  code: string;
}

interface Attachment {
  name: string;
  url: string;
  size: number;
  type: string;
}

interface FormData {
  applicant: string;
  department: string;
  applyTime: string;
  purpose: string;
  remark: string;
  applyItems: ApplyItem[];
  attachments: Attachment[];
}

// 表单数据
const formData = ref<FormData>({
  applicant: '张三',
  department: '技术部',
  applyTime: '2024-03-14 10:00',
  purpose: '',
  remark: '',
  applyItems: [],
  attachments: []
})

// 审批状态
const isSubmitting = ref(false)
const showApprovalConclusion = ref(false)
const approvalType = ref<'approve' | 'reject'>('approve')

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${(bytes / Math.pow(k, i)).toFixed(1)} ${sizes[i]}`
}

// 下载文件
const downloadFile = (file: Attachment) => {
  // TODO: 实现文件下载逻辑
  ElMessage.info('文件下载功能开发中')
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 处理通过
const handleApprove = () => {
  if (isSubmitting.value) return
  approvalType.value = 'approve'
  showApprovalConclusion.value = true
}

// 处理驳回
const handleReject = () => {
  if (isSubmitting.value) return
  approvalType.value = 'reject'
  showApprovalConclusion.value = true
}

// 处理审批结论确认
const handleApprovalConfirm = async (form: { comment: string; signature: string }) => {
  try {
    isSubmitting.value = true
    // TODO: 实现审批提交逻辑
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success(approvalType.value === 'approve' ? '审批通过！' : '已驳回申请！')
    showApprovalConclusion.value = false
    router.back()
  } catch (error) {
    console.error('审批失败:', error)
    ElMessage.error('审批失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

// 处理审批结论取消
const handleApprovalCancel = () => {
  showApprovalConclusion.value = false
}

// 获取申请单详情
const getApplyFormDetail = async (id: string) => {
  try {
    // TODO: 实现获取申请单详情的API调用
    // 这里使用模拟数据
    formData.value = {
      applicant: '张三',
      department: '技术部',
      applyTime: '2024-03-14 10:00',
      purpose: '由于部门业务扩展，新增5名员工，需要配备办公设备。同时现有部分设备老化，需要更新换代。',
      remark: '请尽快处理，新员工下周入职。优先处理新员工设备，现有设备更新可以分批进行。',
      applyItems: [
        {
          id: 1,
          name: 'ThinkPad X1 Carbon',
          model: '2023款 32GB',
          image: '/images/laptop.jpg',
          quantity: 5,
          warehouse: '主仓库',
          code: 'NB202403001'
        },
        {
          id: 2,
          name: '显示器',
          model: 'Dell P2415Q 4K',
          image: '/images/monitor.jpg',
          quantity: 5,
          warehouse: '主仓库',
          code: 'DS202403001'
        }
      ],
      attachments: [
        {
          name: '部门扩编计划.pdf',
          url: '/files/部门扩编计划.pdf',
          size: 1024 * 1024 * 2.5,
          type: 'application/pdf'
        },
        {
          name: '设备采购预算.xlsx',
          url: '/files/设备采购预算.xlsx',
          size: 1024 * 512,
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        },
        {
          name: '新员工入职名单.pdf',
          url: '/files/新员工入职名单.pdf',
          size: 1024 * 256,
          type: 'application/pdf'
        }
      ]
    }
  } catch (error) {
    console.error('获取申请单详情失败:', error)
    ElMessage.error('获取申请单详情失败')
  }
}

onMounted(async () => {
  await getApplyFormDetail('')
})
</script>

<style scoped>
.page-container {
  background-color: rgb(249, 250, 251);
  min-height: 100vh;
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.page-container::-webkit-scrollbar {
  display: none;
}

.form-content {
  padding: 64px 8px 96px;
  min-height: calc(100vh - 64px);
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.form-content::-webkit-scrollbar {
  display: none;
}

form {
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

form::-webkit-scrollbar {
  display: none;
}

:deep(.approval-dialog) {
  border-radius: 8px;
  
  .el-dialog__header {
    margin: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
  }
  
  .el-dialog__title {
    font-size: 16px;
    font-weight: 500;
    color: #1f2937;
  }
  
  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
  }

  @media screen and (min-width: 768px) {
    width: 600px !important;
    margin: 0 auto;
  }
}
</style> 