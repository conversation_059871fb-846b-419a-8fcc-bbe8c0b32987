import './assets/main.css'
import '@fortawesome/fontawesome-free/css/all.css'
import 'element-plus/dist/index.css'
import vant from 'vant'
import 'vant/lib/index.css'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { MotionPlugin } from '@vueuse/motion'
import ElementPlus from 'element-plus'

import App from './App.vue'
import router from './router'
import VConsole from 'vconsole'
new VConsole()
const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(MotionPlugin)
app.use(ElementPlus)
app.use(vant)

app.mount('#app')
