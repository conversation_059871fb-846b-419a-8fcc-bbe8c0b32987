import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface LoginCredentials {
  username: string
  password: string
}

export const useUserStore = defineStore('user', () => {
  const isAuthenticated = ref(false)
  const username = ref('')

  const login = async (credentials: LoginCredentials) => {
    // TODO: 实现实际的登录API调用
    // 这里暂时模拟登录过程
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (credentials.username && credentials.password) {
          isAuthenticated.value = true
          username.value = credentials.username
          resolve(true)
        } else {
          reject(new Error('用户名或密码错误'))
        }
      }, 1000)
    })
  }

  const logout = () => {
    isAuthenticated.value = false
    username.value = ''
  }

  return {
    isAuthenticated,
    username,
    login,
    logout
  }
}) 