# 创建年份选择器组件

<template>
  <div :class="hideTitle ? '' : 'form-group'">
    <label v-if="!hideTitle" class="label" :id="`${id}-label`"
      ><span class="text-red-500" aria-hidden="true">*</span>{{ title }}</label
    >
    <div class="flex-1">
      <div
        class="w-full px-4 py-2 border border-gray-300 rounded-lg text-base bg-white"
        @click="showPicker = true"
        role="button"
        tabindex="0"
        :aria-labelledby="`${id}-label`"
        :aria-expanded="showPicker"
        @keydown.enter="showPicker = true"
        @keydown.space.prevent="showPicker = true"
      >
        <span v-if="modelValue">{{ modelValue }}</span>
        <span v-else class="text-gray-400">{{ placeholder }}</span>
      </div>
    </div>
  </div>
  <!-- 年份选择弹窗 -->
  <van-popup
    v-model:show="showPicker"
    round
    position="bottom"
    :aria-labelledby="`${id}-label`"
    @closed="onPopupClosed"
  >
    <van-date-picker
      title="选择日期"
      :min-date="new Date()"
      :formatter="datePickerFormatter"
      @confirm="onConfirm"
      @cancel="showPicker = false"
    />
  </van-popup>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { PickerOption } from 'vant'

// 生成唯一ID
const id = `year-picker-${Math.random().toString(36).substr(2, 9)}`

// 定义组件的属性
withDefaults(
  defineProps<{
    modelValue?: string
    placeholder?: string
    title?: string
    hideTitle?: boolean
  }>(),
  {
    modelValue: undefined,
    placeholder: '请选择年份',
    title: '请选择年份',
    hideTitle: false,
  },
)

// 定义组件的事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}>()

// 内部状态
const showPicker = ref(false)

// 处理确认选择
const onConfirm = (value: { selectedValues: string[] }) => {
  const dateStr = value.selectedValues[0] + '-' + value.selectedValues[1] + '-' + value.selectedValues[2]
  emit('update:modelValue', dateStr)
  emit('change', dateStr)
  showPicker.value = false
}

const datePickerFormatter = (type: string, option: PickerOption): PickerOption => {
  if (type === 'year') {
    option.text = option.text + '年'
  }
  if (type === 'month') {
    option.text = option.text + '月'
  }
  if (type === 'day') {
    option.text = option.text + '日'
  }
  return option
}

// 处理弹窗关闭后的清理
const onPopupClosed = () => {
  // 将焦点返回到触发器元素
  const triggerElement = document.querySelector(`[aria-labelledby="${id}-label"]`) as HTMLElement
  if (triggerElement) {
    triggerElement.focus()
  }
  showPicker.value = false
}
</script>

<style scoped>
.form-group {
  margin-bottom: 1rem;
}
.form-group.horizontal {
  display: flex;
  align-items: center;
}
.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #374151;
}

.form-group.horizontal .label {
  width: 7rem;
  margin-bottom: 0;
}
</style>
