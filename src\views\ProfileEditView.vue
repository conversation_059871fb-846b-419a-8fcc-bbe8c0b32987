<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 表单数据
const form = ref({
  phone: ''
})

// 错误信息
const errors = ref({
  phone: ''
})

// 返回上一页
const goBack = () => {
  router.back()
}

// 验证手机号格式
const isValidPhone = (phone: string) => {
  return /^1[3-9]\d{9}$/.test(phone)
}

// 验证表单
const validateForm = () => {
  let isValid = true
  errors.value.phone = ''

  if (!form.value.phone) {
    errors.value.phone = '请输入手机号'
    isValid = false
  } else if (!isValidPhone(form.value.phone)) {
    errors.value.phone = '请输入正确的手机号'
    isValid = false
  }

  return isValid
}

// 提交表单
const handleSubmit = () => {
  if (!validateForm()) return

  // TODO: 调用修改手机号API
  alert('手机号修改成功')
  router.back()
}
</script>

<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <div class="flex items-center justify-between mb-6 relative">
      <button @click="goBack" class="absolute left-0 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
        <i class="fas fa-arrow-left text-gray-500"></i>
      </button>
      <h1 class="text-xl font-bold w-full text-center">个人资料</h1>
    </div>

    <!-- 表单 -->
    <div class="space-y-6">
      <!-- 手机号 -->
      <div class="space-y-2">
        <label class="text-sm text-gray-600">手机号</label>
        <div class="relative">
          <input
            v-model="form.phone"
            type="tel"
            class="w-full px-4 py-3 rounded-lg bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary/50"
            placeholder="请输入手机号"
            maxlength="11"
          >
        </div>
        <p v-if="errors.phone" class="text-xs text-red-500">{{ errors.phone }}</p>
      </div>
    </div>

    <!-- 提交按钮 -->
    <button
      @click="handleSubmit"
      class="w-full mt-6 py-3 rounded-lg bg-primary text-white font-medium hover:bg-primary/90 transition-colors"
    >
      确认修改
    </button>
  </div>
</template>

<style scoped>
.page-container {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
</style>