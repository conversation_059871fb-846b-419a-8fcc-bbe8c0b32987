import { defHttp } from "../axios";
import { postDefault } from "./default";

export const onGetTokenRequest = (code: string) => defHttp.post(
  {
    url: '/system/v1/sysUser/loginWithWorkWechat',
    data: {
      code
    },
    headers: {
      ignoreCancelToken: true,
    },
  }
);
// 获取企业微信url
// export const getWxUrl = (data: { redirect_uri: string }) => defHttp.post(
//   {
//     url: '/user/v1/work_wx_auth_url',
//     data,
//     headers: {
//       ignoreCancelToken: true,
//     }
//   }
// );
// 获取企业微信重定向地址
export const getWxUrl = (data: { redirect_uri: string }) => defHttp.get(
  {
    url: '/system/v1/sysUser/wxwork/authUrl',
    params: data,
    headers: {
      ignoreCancelToken: true,
    }
  }
);
// 企业微信登录1
export const getCode = (data: { code: string }) => defHttp.post(
  {
    url: '/user/v1/work_wechat_login',
    data,
    headers: {
      ignoreCancelToken: true,
    }
  }
);

// 企业微信登录2
// export const loginWithWorkWechat = (data: { code: string,platform: string }) => defHttp.post(
//   {
//     url: '/system/v1/sysUser/loginWithWorkWechat',
//     data,
//     headers: {
//       ignoreCancelToken: true,
//     }
//   }
// );
export const wxLogin = (data: { code: string,platform: string }) => defHttp.post(
  {
    url: '/system/v1/sysUser/wxwork/login',
    data,
    headers: {
      ignoreCancelToken: true,
    }
  }
);

// 登录绑定
export const bindWxwork = (data: { code: string,platform: string }) => defHttp.post(
  {
    url: '/system/v1/sysUser/bindWxwork',
    data,
    headers: {
      ignoreCancelToken: true,
    }
  }
);

export const onGetUserInfo = () => postDefault('/system/v1/sysUser/getUserInfo', {
  applicationCode: "system"
})
