<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import { useCartStore } from '@/stores/cart'
import { hpClaimOrder } from '@/api/equipment'
import * as ww from '@wecom/jssdk'
const router = useRouter()
const route = useRoute()
const cartStore = useCartStore()
const cartItems = computed(() => cartStore.cartItems)
import NewFileUpload from '@/components/newFileUpload.vue'
// 表单数据
interface ApplyItem {
  id: number;
  name: string;
  model: string;
  images: string;
  quantity: number;
  selected: boolean;
  // warehouse: string; // 添加仓库字段
}

const formData = ref({
  details: [] as any, // 申领物品列表
  reason: '', // 事由
  remark: '', // 备注
  files: [], // 添加附件数组
  status: 0
})
// 初始化表单
const initApplyItems = () => {
  if (!cartItems.value) {
    console.log('购物车为空')
    formData.value.details = []
    return
  }

  try {
    // const items = JSON.parse(cartItems)
    // 只获取勾选的商品
    const selectedItems = cartItems.value.filter((item: any) => item.selected)
    if (selectedItems.length === 0) {
      console.log('没有勾选的商品')
      formData.value.details = []
      return
    }
    // 转换为申领明细
    formData.value.details = selectedItems
    console.log('最终的申领明细:', formData.value.details)
  } catch (error) {
    console.error('解析购物车数据失败:', error)
    formData.value.details = []
  }
}
// 计算总数量
const totalQuantity = computed(() => {
  return formData.value.details.reduce((sum, item) => sum + item.quantity, 0)
})

// 返回上一页
const goBack = () => {
  router.back()
}

// 按仓库分组商品
// const groupedApplyItems = computed(() => {
//   const groups = new Map<string, ApplyItem[]>();
//   formData.value.details.forEach(item => {
//     if (!groups.has(item.warehouse)) {
//       groups.set(item.warehouse, []);
//     }
//     groups.get(item.warehouse)?.push(item);
//   });
//   return groups;
// });

// 提示框状态
const showSuccessModal = ref(false)

// 提交表单
const submitForm = async () => {
  // 这里添加表单验证逻辑
  if (!formData.value.reason) {
    showToast('请填写事由')
    return
  }
  if (formData.value.details.length === 0) {
    showToast('请选择要申领的物品')
    return
  }
  
  try {
    const submitData = {
      files: formData.value.files && formData.value.files.length ? formData.value.files.join(',') : '',
      details: JSON.parse(JSON.stringify(formData.value.details)),
      reason: formData.value.reason,
      remark: formData.value.remark,
      status: formData.value.status
    }
    console.log('提交的表单数据：', submitData)
    const startTime = Date.now()
    hpClaimOrder(submitData).then(async res => {
      console.log('提交啊啊啊', res);
      if (res && res.details && res.details.length) {
        res.details.map(item => {
          const cartItem = cartItems.value.find(cart => cart.id === item.materialId)
          console.log('cartItem', cartItem);
          
          if (!cartItem) return
          cartStore.removeFromCart(item.materialId)
        })
      }
      // showToast('提交成功')
      // router.back()
 // 计算已经过去的时间
 const elapsedTime = Date.now() - startTime
    // 如果耗时小于200ms，则等待剩余时间
    if (elapsedTime < 200) {
      await new Promise(resolve => setTimeout(resolve, 200 - elapsedTime))
    }

    // 显示成功提示
    // showSuccessModal.value = true
    // 开始时间
    const url = import.meta.env.VITE_URL
    let link = `${url}#/apply-form-detail?id=${res.id}`
    let templateId = import.meta.env.VITE_ENV === 'prod' ? 'bbab44b03af1f09ecda39218f0600859_278605655' : '00aacf56c99ad770316691a482069720_198144139'
    console.log( Date.now(), '开始')
    ww.thirdPartyOpenPage({
      oaType: ww.OAType.create_approval,
      templateId,
      thirdNo: 'SLSQ-' + res.id,
      extData: {
        fieldList: [
          {
            title: '申请类型',
            type: ww.OaExtDataType.text,
            value: '申领申请',
          },
          {
              'title': '申请时间',
              'type': ww.OaExtDataType.text,
              'value': res.createdAt,
          },
          {
            'title': '申领事由',
            'type': ww.OaExtDataType.text,
            'value': submitData.reason,
          },
          {
            type: ww.OaExtDataType.link,
            title: '详细内容',
            value: link
          }
        ]
      },
      success: () => {
        console.log( Date.now(), '提交成功')
        // router.push('/apps')
      },
      fail: () => {
        console.log( Date.now(), '提交失败')
      },
      complete: () => {
        console.log( Date.now(), '提交完成')
      }
    })
    }).catch(err => {
      showToast('提交失败'+ err)
    })
  } catch (error) {
    console.error('提交失败：', error)
    showToast('提交失败，请重试')
  }
}

// 在组件挂载时初始化
onMounted(() => {
  initApplyItems()
})
</script>

<template>
  <div class="page-container min-h-screen bg-gray-50 overflow-y-auto">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-bold">资产申领</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="pt-14 pb-4">
      <div class="bg-white p-2.5 space-y-6 mb-4">
        <!-- 事由 -->
        <div class="form-group">
          <label class="label"><span class="text-red-500">*</span>事由</label>
          <textarea
            v-model="formData.reason"
            class="input min-h-[100px]"
            placeholder="请详细说明申领事由"
          ></textarea>
        </div>

        <!-- 申领明细 -->
        <div class="form-group">
          <!-- <label class="label">申领明细{{ formData.details }} {{ formData.details.length > 0 }}</label> -->
          <div v-if="formData.details.length > 0" class="space-y-4">
              <!-- 仓库分类 -->
              <div class="bg-white rounded-lg overflow-hidden border border-gray-100">
                <div class="bg-gray-50 px-4 py-2.5 flex items-center justify-between">
                  <h3 class="text-sm font-medium text-gray-700">申领明细</h3>
                  <span class="text-xs text-gray-500">{{ formData.details.length }}件物品</span>
                </div>
                <!-- 商品列表 -->
                <div class="divide-y divide-gray-100">
                  <div 
                    v-for="(item, index) in formData.details"
                    :key="index"
                    class="p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div class="flex items-start space-x-4">
                      <!-- 商品图片 -->
                      <div class="flex-shrink-0">
                        <img 
                          :src="item.images" 
                          :alt="item.name"
                          class="w-20 h-20 object-cover rounded-lg border border-gray-200 bg-white"
                        >
                      </div>
                      <!-- 商品信息 -->
                      <div class="flex-1 min-w-0 py-1">
                        <div class="text-base font-medium text-gray-900 mb-2">{{ item.name || '商品名称' }}</div>
                        <div class="text-sm text-gray-500">型号：{{ item.model }}</div>
                        <div class="text-sm text-gray-500 mt-1">数量：{{ item.quantity }}件</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            <!-- 总数量显示 -->
            <div class="flex justify-between items-center px-2 text-sm text-gray-500">
              <span>总计</span>
              <span>{{ totalQuantity }}件物品</span>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-else class="text-center py-8 bg-gray-50 rounded-lg">
            <i class="fas fa-box-open text-gray-300 text-3xl mb-3"></i>
            <div class="text-sm text-gray-500">暂无申领物品，请先在购物车中选择要申领的物品</div>
          </div>
        </div>

        <!-- 备注 -->
        <div class="form-group">
          <label class="label">备注</label>
          <textarea
            v-model="formData.remark"
            class="input"
            placeholder="请输入备注信息（选填）"
          ></textarea>
        </div>

        <!-- 附件上传 -->
        <div class="form-group">
          <label class="label">附件</label>
          <NewFileUpload v-model="formData.files" class="mt-2"/>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-2.5 py-3">
      <button
        @click="submitForm"
        class="w-full py-2.5 px-4 bg-primary text-white rounded-lg"
      >
        下一步
      </button>
    </div>

    <!-- 成功提示弹窗 -->
    <div
      v-if="showSuccessModal"
      class="fixed inset-0 flex items-center justify-center z-50"
    >
      <!-- 背景遮罩 -->
      <div 
        class="absolute inset-0 bg-black transition-opacity duration-300"
        :class="showSuccessModal ? 'bg-opacity-60' : 'bg-opacity-0'"
      ></div>
      
      <!-- 弹窗内容 -->
      <div 
        class="bg-white rounded-2xl p-8 relative z-10 max-w-sm w-full mx-4 transform transition-all duration-500 ease-out"
        :class="showSuccessModal ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-4 scale-95'"
      >
        <div class="text-center">
          <!-- 成功图标 -->
          <div class="w-20 h-20 mx-auto mb-6 relative">
            <div class="absolute inset-0 bg-green-100 rounded-full animate-ripple"></div>
            <div class="absolute inset-0 flex items-center justify-center">
              <i class="fas fa-check text-3xl text-green-500 animate-success-icon"></i>
            </div>
          </div>
          
          <!-- 文本内容 -->
          <h3 class="text-xl font-semibold text-gray-900 mb-3">提交成功</h3>
          <p class="text-gray-600 mb-6">您的申领申请已成功提交，我们会尽快处理</p>
          
          <!-- 进度条 -->
          <div class="w-full bg-gray-100 rounded-full h-1 mb-3 overflow-hidden">
            <div class="h-full bg-green-500 animate-progress"></div>
          </div>
          <div class="text-sm text-gray-500">即将返回上一页...</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.form-group {
  @apply space-y-1;
}

.label {
  @apply text-sm text-gray-600 block;
}

.input {
  @apply w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-primary;
  -webkit-appearance: none;
  appearance: none;
}

@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes success-icon {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes progress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

.animate-ripple {
  animation: ripple 2s ease-out infinite;
}

.animate-success-icon {
  animation: success-icon 0.5s ease-out forwards;
}

.animate-progress {
  animation: progress 3s linear forwards;
}

/* 过渡效果 */
.transition-opacity {
  transition-property: opacity;
}

.transition-all {
  transition-property: all;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.translate-y-0 {
  transform: translateY(0);
}

.translate-y-4 {
  transform: translateY(1rem);
}

.scale-95 {
  transform: scale(0.95);
}

.scale-100 {
  transform: scale(1);
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.bg-opacity-0 {
  --tw-bg-opacity: 0;
}

.bg-opacity-60 {
  --tw-bg-opacity: 0.6;
}
</style> 