<template>
  <div class="order-list">
    <List :request-fn="fetchData" v-slot="{ list }" :key="key">
      <div class="order-cards">
        <div v-for="order in list" :key="order.id" class="order-card" @click="wwView(order)">
          <div class="order-header">
            <div class="order-title">
              <span class="order-name">{{ order.openSpName }}</span>
              <el-tag :type="getStatusType(order.openSpStatus)" size="small" class="status-tag">{{ getStatusText(order.openSpStatus) }}</el-tag>
            </div>
            <div class="order-arrow">
              <el-icon class="arrow-icon"><ArrowRight /></el-icon>
            </div>
          </div>
          <div class="order-info">
            <div class="info-item">
              <span class="label">申请人：</span>
              <span>{{ order.applyUserName }}</span>
            </div>
            <div class="info-item">
              <span class="label">申请时间：</span>
              <span>{{ dayjs(order.applyTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
            </div>
          </div>
        </div>
      </div>
    </List>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import dayjs from 'dayjs'
import * as ww from '@wecom/jssdk'
import { useRouter } from 'vue-router'
import { ArrowRight } from '@element-plus/icons-vue'
import { approvalList, myList, copyList } from '@/api/approval'
import List from '@/components/List.vue'

let key
const props = defineProps<{
  status?: string
  type?: string
  titleType: string
}>()
console.log('propssss', props.status);
watch(() => props.status, () => {
  key = new Date()
  console.log('open_sp_status', props.status);
  fetchData(1, 10)
})
watch(() => props.type, () => {
  key = new Date()
  console.log('sp_type', props.type);
  fetchData(1, 10)
})
const router = useRouter()

interface Order {
  id: string
  thirdNo: string
  spType: string
  openSpName?: string
  openTemplateId: string
  openSpStatus?: number
  applyTime?: string
  applyUserName?: string
  applyUserId?: string
  applyUserParty?: string
  applyUserImage?: string
  agentId?: string
  toUserName?: string
  fromUserName?: string
  createTime?: string
  msgType?: string
  event?: string
}
const fetchData = async (page: number, pageSize: number) => {

  let query = {
    open_sp_status: Array.isArray(props.status) ? props.status : [props.status]
  }
  if (props.type) {
    query.open_sp_name = props.type
  }
  console.log('queryyy', query);
  
  const params = {
    page,
    pageSize,
    query: JSON.stringify(query),
    orderBy: JSON.stringify(["-updated_at", "-created_at"])
  }
  let api = props.titleType == 'approval' ? approvalList : props.titleType == 'copy' ? copyList : props.titleType == 'my' ? myList : ''
  let res = await api(params)
  return {
    list: res.items,
    total: res.total
  };

}

const getStatusType = (status: number)=> {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'danger'> = {
    1: 'warning',
    3: 'danger',
    2: 'success',
    4: 'info'
  }
  return statusMap[status]
}
const getStatusText = (status: number) => {
  const statusMap = {
    1: '审批中',
    2: '已通过',
    3: '已驳回',
    4: '已撤销'
  }
  return statusMap[status]
}
const wwView = (data:Order) => {
  const id = data.thirdNo.split('-')[1]
  const url = import.meta.env.VITE_URL
  let link
  let path
  if (data.spType == 'SLSQ') {
    // 申领
    path = '/apply-form-detail'
    link = `${url}#/apply-form-detail?id=${id}`
  } else if (data.spType == 'BFSQ') {
    // 报废
    path = '/scrap-application-detail'
    link = `${url}#/scrap-application-detail?id=${id}`
  } else if (data.spType == 'YSSQ') {
    // 预算
    path = '/budget-application-detail'
    link = `${url}#/budget-application-detail?id=${id}`
  } else if (data.spType == 'DBSQ') {
    // 调拨
    path = '/transfer-application-detail'
    link = `${url}#/transfer-application-detail?id=${id}`
  } else if (data.spType == 'THSQ') {
    // 退还
    path = '/return-application-detail'
    link = `${url}#/return-application-detail?id=${id}`
  } else {
    console.log('未知的单据类型:', data.spType)
  }
  console.log('link', link);
  
  if (import.meta.env.VITE_ENV === 'dev'){
    router.push({
      path,
      query: {
        id
      }
    })
    return
  }
  ww.thirdPartyOpenPage({
      oaType: ww.OAType.view_approval,
      templateId: data.openTemplateId,
      thirdNo: data.thirdNo,
      extData: {
        fieldList: [
          {
            title: '申请类型',
            type: ww.OaExtDataType.text,
            value: data.openSpName,
          },
          {
            title: '申请部门',
            type: ww.OaExtDataType.text,
            value: data.applyUserParty,
          },
          {
            title: '申请人',
            type: ww.OaExtDataType.text,
            value: data.applyUserName,
          },
          {
            title: '申请时间',
            type: ww.OaExtDataType.text,
            value: dayjs(data.applyTime).format('YYYY-MM-DD HH:mm:ss'),
          },
          {
            type: ww.OaExtDataType.link,
            title: '详细内容',
            value: link
          }
        ]
      },
      success: () => {
        console.log( Date.now(), '查看成功')
      },
      fail: () => {
        console.log( Date.now(), '查看失败')
      },
      complete: () => {
        console.log( Date.now(), '查看完成')
      }
    })
}
const handleView = (order: Order) => {
  if (order.spType === 'DBSQ') {
    router.push({
      path: '/transfer-application-detail',
      query: {
        id: order.id
      }
    })
  } else if (order.spType === 'BFSQ') {
    router.push({
      path: '/scrap-application-detail',
      query: {
        id: order.id
      }
    })
  } else if (order.spType === 'SLSQ') {
    router.push({
      path: '/apply-form-detail',
      query: {
        id: order.id
      }
    })
  } else if (order.spType === 'YSSQ') {
    router.push({
      path: '/budget-application-detail',
      query: {
        id: order.id
      }
    })
  } else if (order.spType === 'THSQ') {
    router.push({
      path: `/return-application-detail/${order.id}`
    })
  } else {
    console.log('未知的单据类型:', order.spType)
  }
}
</script>

<style scoped>
.order-list {
  padding: 16px;
  background-color: #f5f7fa;
  height: 100%;
}

.order-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-card {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  position: relative;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.order-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.order-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.status-tag {
  margin-left: 8px;
}

.order-info {
  color: #606266;
  font-size: 14px;
}

.info-item {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.info-item .label {
  color: #909399;
  margin-right: 8px;
}

.order-arrow {
  color: #c0c4cc;
  display: flex;
  align-items: center;
}

.arrow-icon {
  font-size: 16px;
}

:deep(.el-tag) {
  border-radius: 4px;
}
</style> 