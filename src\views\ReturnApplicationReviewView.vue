<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-medium">资产退还审核</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <!-- 申请表单 -->
    <div class="form-content">
      <form class="space-y-4">
        <!-- 基本信息 -->
        <BasicInfoCard
          :applicant="formData.applicant"
          :department="formData.department"
          :apply-time="formData.applyTime"
        />

        <!-- 事由 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-3">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              退还说明
            </label>
            <div class="w-full p-2 bg-gray-50 rounded-md min-h-[100px]">
              {{ formData.purpose }}
            </div>
          </div>
        </div>

        <!-- 退还明细 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-3">
            <div class="flex justify-between items-center mb-3">
              <label class="block text-sm font-medium text-gray-700">
                退还明细
              </label>
            </div>
            
            <div v-if="formData.returnItems.length === 0" class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg">
              <i class="fas fa-inbox text-3xl mb-2"></i>
              <p>暂无退还物品</p>
            </div>
            
            <div v-else class="space-y-2">
              <div 
                v-for="item in formData.returnItems" 
                :key="item.id" 
                class="flex justify-between items-center p-2 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-900 mb-1">{{ item.name }}</div>
                  <div class="text-xs text-gray-500 mb-1 flex items-center">
                    <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                    <span>资产编号：{{ item.code }}</span>
                  </div>
                  <div class="text-xs text-gray-500 flex items-center">
                    <div class="w-1 h-1 rounded-full bg-gray-300 mr-2"></div>
                    <span>规格型号：{{ item.model }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 资产状态 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-3">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              资产状态
            </label>
            <div class="px-3 py-2 bg-gray-50 rounded-md inline-block">
              <span v-if="formData.assetStatus === 'normal'" class="text-green-600">
                <i class="fas fa-check-circle mr-1.5"></i> 完好
              </span>
              <span v-else-if="formData.assetStatus === 'damaged'" class="text-orange-600">
                <i class="fas fa-tools mr-1.5"></i> 损坏
              </span>
              <span v-else-if="formData.assetStatus === 'lost'" class="text-red-600">
                <i class="fas fa-times-circle mr-1.5"></i> 丢失
              </span>
            </div>
          </div>
        </div>

        <!-- 备注 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-3">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              备注
            </label>
            <div class="w-full p-2 bg-gray-50 rounded-md">
              {{ formData.remark || '无' }}
            </div>
          </div>
        </div>

        <!-- 附件列表 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-3">
            <div class="flex justify-between items-center mb-3">
              <label class="text-sm font-medium text-gray-700">附件列表</label>
            </div>

            <div v-if="formData.attachments.length === 0" class="text-center py-6 text-gray-500 border-2 border-dashed rounded-lg">
              <i class="fas fa-cloud-upload-alt text-3xl mb-2"></i>
              <p>暂无附件</p>
            </div>

            <div v-else class="space-y-2">
              <div 
                v-for="(file, index) in formData.attachments" 
                :key="index"
                class="flex justify-between items-center p-2 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div class="flex-1 min-w-0">
                  <div class="flex items-center">
                    <i class="fas fa-file text-gray-400 mr-2"></i>
                    <span class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</span>
                  </div>
                  <div class="text-xs text-gray-500 mt-1">{{ formatFileSize(file.size) }}</div>
                </div>
                <a 
                  href="#"
                  class="text-blue-600 hover:text-blue-700"
                  @click.prevent="downloadFile(file)"
                >
                  <i class="fas fa-download"></i>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- 审批流程 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-3">
            <ApprovalFlow class="approval-flow-custom" />
          </div>
        </div>
      </form>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
      <div class="flex space-x-4">
        <button 
          type="button"
          @click="handleReject"
          class="flex-1 px-4 py-2 border border-red-600 rounded-md text-red-600 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          :disabled="isSubmitting"
        >
          {{ isSubmitting ? '处理中...' : '驳回' }}
        </button>
        <button 
          type="button"
          @click="handleApprove"
          class="flex-1 px-4 py-2 border border-transparent rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          :disabled="isSubmitting"
        >
          {{ isSubmitting ? '处理中...' : '通过' }}
        </button>
      </div>
    </div>

    <!-- 审批结论弹窗 -->
    <el-dialog
      v-model="showApprovalConclusion"
      :title="approvalType === 'approve' ? '审批通过' : '审批驳回'"
      width="95%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      class="approval-dialog"
      destroy-on-close
      top="10vh"
    >
      <ApprovalConclusion
        :type="approvalType"
        @confirm="handleApprovalConfirm"
        @cancel="handleApprovalCancel"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElDialog } from 'element-plus'
import 'element-plus/es/components/dialog/style/css'
import ApprovalFlow from '@/views/order/ApprovalFlow.vue'
import ApprovalConclusion from '@/views/order/ApprovalConclusion.vue'
import BasicInfoCard from '@/views/order/BasicInfoCard.vue'

const router = useRouter()
const route = useRoute()

// 表单数据接口定义
interface ReturnItem {
  id: number;
  name: string;
  model: string;
  image: string;
  code: string;
  warehouse: string;
}

interface Attachment {
  name: string;
  url: string;
  size: number;
  type: string;
}

// 模拟数据
const formData = ref({
  id: '202404010001',
  applicant: '张三',
  department: '研发部',
  applyTime: '2024-04-01 10:30:45',
  status: 'pending',
  purpose: '项目已完成，不再需要使用这些设备，现申请退还。',
  assetStatus: 'normal', // normal-完好，damaged-损坏，lost-丢失
  returnItems: [
    {
      id: 1,
      name: '笔记本电脑',
      model: 'MacBook Pro 2023',
      image: '/images/laptop.jpg',
      code: 'NB20230001',
      warehouse: '总部仓库'
    },
    {
      id: 2,
      name: '显示器',
      model: 'Dell P2720D 27英寸',
      image: '/images/monitor.jpg',
      code: 'MN20230015',
      warehouse: '总部仓库'
    }
  ] as ReturnItem[],
  remark: '所有设备已恢复出厂设置，随附原包装盒及配件。',
  attachments: [
    {
      name: '资产照片.jpg',
      url: '/files/demo-image.jpg',
      size: 2.5 * 1024 * 1024,
      type: 'image/jpeg'
    }
  ] as Attachment[]
})

// 审批状态
const isSubmitting = ref(false)
const showApprovalConclusion = ref(false)
const approvalType = ref<'approve' | 'reject'>('approve')

// 返回上一页
const goBack = () => {
  router.back()
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  }
}

// 下载文件
const downloadFile = (file: Attachment) => {
  // 这里实现文件下载逻辑
  console.log('下载文件：', file)
  // 模拟下载行为
  const link = document.createElement('a')
  link.href = file.url
  link.download = file.name
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 处理审批通过
const handleApprove = () => {
  approvalType.value = 'approve'
  showApprovalConclusion.value = true
}

// 处理审批驳回
const handleReject = () => {
  approvalType.value = 'reject'
  showApprovalConclusion.value = true
}

// 确认审批结论
const handleApprovalConfirm = async (form: { comment: string; signature: string }) => {
  try {
    isSubmitting.value = true
    
    // 模拟API调用
    console.log(`提交${approvalType.value === 'approve' ? '通过' : '驳回'}审批，意见：${form.comment}，签名：${form.signature}`)
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 关闭弹窗
    showApprovalConclusion.value = false
    
    // 显示成功消息
    ElMessage.success(`审批${approvalType.value === 'approve' ? '通过' : '驳回'}成功`)
    
    // 返回列表页面
    setTimeout(() => {
      router.replace('/my-approval')
    }, 1500)
  } catch (error) {
    console.error('审批失败：', error)
    ElMessage.error('审批失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

// 取消审批结论
const handleApprovalCancel = () => {
  showApprovalConclusion.value = false
}

// 获取申请详情
const fetchApplicationDetail = async () => {
  try {
    const id = route.params.id
    if (!id) {
      ElMessage.error('未找到申请ID')
      router.replace('/my-approval')
      return
    }
    
    console.log(`获取ID为 ${id} 的退还申请详情`)
    // 这里是模拟数据，实际项目中应该从API获取数据
    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 数据已在上面的formData中定义
  } catch (error) {
    console.error('获取申请详情失败：', error)
    ElMessage.error('获取申请详情失败')
    router.replace('/my-approval')
  }
}

onMounted(() => {
  fetchApplicationDetail()
})
</script>

<style scoped>
.page-container {
  padding-top: 56px;
  padding-bottom: 80px;
  background-color: #f7f8fa;
  min-height: 100vh;
}

.form-content {
  padding: 0.75rem 0.5rem;
}

/* 审批结论弹窗样式 */
:deep(.approval-dialog) {
  border-radius: 16px;
  overflow: hidden;
}

/* 审批流程自定义样式 */
:deep(.approval-flow-custom) {
  margin-left: 2px;
  padding-left: 5px;
  margin-top: -10px;
}
</style> 