<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 定义问题和选项的类型
interface Option {
  id: number;
  text: string;
}

interface Question {
  id: number;
  question: string;
  options: Option[];
  selectedOption: number | null;
}

// 获取任务ID
const taskId = computed(() => Number(route.params.id))

// 任务数据（实际应用中应该从API获取）
const task = ref({
  id: taskId.value,
  title: '完成健康问卷',
  description: '回答5个关于健康习惯的问题，帮助我们为您提供更个性化的健康建议。',
  reward: '20积分',
  status: 'pending',
  icon: 'fa-clipboard-list',
  color: 'bg-green-500',
  steps: [
    { id: 1, title: '填写基本信息', completed: false },
    { id: 2, title: '回答健康习惯问题', completed: false },
    { id: 3, title: '回答饮食习惯问题', completed: false },
    { id: 4, title: '回答运动习惯问题', completed: false },
    { id: 5, title: '回答睡眠习惯问题', completed: false }
  ],
  questions: [
    {
      id: 1,
      question: '您每天的平均睡眠时间是多少?',
      options: [
        { id: 1, text: '少于6小时' },
        { id: 2, text: '6-7小时' },
        { id: 3, text: '7-8小时' },
        { id: 4, text: '8小时以上' }
      ],
      selectedOption: null as number | null
    },
    {
      id: 2,
      question: '您每周进行中等强度运动的频率是?',
      options: [
        { id: 1, text: '从不' },
        { id: 2, text: '1-2次' },
        { id: 3, text: '3-4次' },
        { id: 4, text: '5次以上' }
      ],
      selectedOption: null as number | null
    },
    {
      id: 3,
      question: '您每天喝水的量大约是?',
      options: [
        { id: 1, text: '少于500ml' },
        { id: 2, text: '500-1000ml' },
        { id: 3, text: '1000-1500ml' },
        { id: 4, text: '1500ml以上' }
      ],
      selectedOption: null as number | null
    }
  ] as Question[]
})

// 当前问题索引
const currentQuestionIndex = ref(0)

// 当前问题
const currentQuestion = computed(() => task.value.questions[currentQuestionIndex.value])

// 选择选项
const selectOption = (questionId: number, optionId: number) => {
  const question = task.value.questions.find(q => q.id === questionId)
  if (question) {
    question.selectedOption = optionId
  }
}

// 下一个问题
const nextQuestion = () => {
  if (currentQuestionIndex.value < task.value.questions.length - 1) {
    currentQuestionIndex.value++
  } else {
    // 完成问卷
    completeTask()
  }
}

// 上一个问题
const prevQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
  }
}

// 完成任务
const completeTask = () => {
  // 在实际应用中，这里应该发送请求到服务器
  task.value.status = 'completed'
  
  // 显示完成状态
  isCompleted.value = true
}

// 是否已完成
const isCompleted = ref(false)

// 返回任务列表
const goBack = () => {
  router.push('/')
}

// 检查当前问题是否已回答
const isCurrentQuestionAnswered = computed(() => {
  return currentQuestion.value.selectedOption !== null
})
</script>

<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <div class="flex items-center mb-6">
      <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-3">
        <i class="fas fa-arrow-left text-gray-500"></i>
      </button>
      <h1 class="text-xl font-bold">任务详情</h1>
    </div>
    
    <!-- 任务信息 -->
    <div class="card mb-6">
      <div class="flex items-center mb-4">
        <div :class="['w-12 h-12 rounded-full flex items-center justify-center mr-4', task.color.replace('bg-', 'bg-') + ' text-white']">
          <i :class="['fas', task.icon, 'text-xl']"></i>
        </div>
        <div>
          <h2 class="text-lg font-bold">{{ task.title }}</h2>
          <span class="text-sm text-primary font-medium">奖励: {{ task.reward }}</span>
        </div>
      </div>
      <p class="text-gray-600 mb-4">{{ task.description }}</p>
    </div>
    
    <!-- 完成状态 -->
    <div v-if="isCompleted" class="card text-center py-8">
      <div class="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-4">
        <i class="fas fa-check-circle text-green-500 text-4xl"></i>
      </div>
      <h3 class="text-xl font-bold mb-2">问卷已完成</h3>
      <p class="text-gray-600 mb-6">感谢您完成健康问卷，您已获得20积分奖励</p>
      <button @click="goBack" class="btn btn-primary w-full">
        返回任务列表
      </button>
    </div>
    
    <!-- 问卷内容 -->
    <div v-else class="card">
      <!-- 进度指示器 -->
      <div class="flex justify-between items-center mb-6">
        <span class="text-sm text-gray-500">问题 {{ currentQuestionIndex + 1 }}/{{ task.questions.length }}</span>
        <div class="w-2/3 bg-gray-200 rounded-full h-2">
          <div 
            class="bg-primary h-2 rounded-full" 
            :style="{ width: `${((currentQuestionIndex + 1) / task.questions.length) * 100}%` }"
          ></div>
        </div>
      </div>
      
      <!-- 问题 -->
      <h3 class="text-lg font-bold mb-4">{{ currentQuestion.question }}</h3>
      
      <!-- 选项 -->
      <div class="space-y-3 mb-8">
        <div 
          v-for="option in currentQuestion.options" 
          :key="option.id"
          class="border rounded-lg p-3 cursor-pointer"
          :class="{ 'border-primary bg-primary/5': currentQuestion.selectedOption === option.id, 'border-gray-200': currentQuestion.selectedOption !== option.id }"
          @click="selectOption(currentQuestion.id, option.id)"
        >
          <div class="flex items-center">
            <div 
              class="w-5 h-5 rounded-full border flex items-center justify-center mr-3"
              :class="{ 'border-primary': currentQuestion.selectedOption === option.id, 'border-gray-300': currentQuestion.selectedOption !== option.id }"
            >
              <div 
                v-if="currentQuestion.selectedOption === option.id" 
                class="w-3 h-3 rounded-full bg-primary"
              ></div>
            </div>
            <span>{{ option.text }}</span>
          </div>
        </div>
      </div>
      
      <!-- 导航按钮 -->
      <div class="flex space-x-3">
        <button 
          v-if="currentQuestionIndex > 0"
          @click="prevQuestion" 
          class="btn flex-1 bg-gray-100 text-gray-700"
        >
          上一题
        </button>
        <button 
          @click="nextQuestion" 
          class="btn btn-primary flex-1"
          :disabled="!isCurrentQuestionAnswered"
          :class="{ 'opacity-50 cursor-not-allowed': !isCurrentQuestionAnswered }"
        >
          {{ currentQuestionIndex === task.questions.length - 1 ? '提交' : '下一题' }}
        </button>
      </div>
    </div>
  </div>
</template> 