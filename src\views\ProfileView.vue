<template>
  <div class="page-container pb-24">
    <!-- 用户信息卡片 -->
    <div class="card p-0 overflow-hidden mb-6">
      <!-- 背景图 -->
      <div class="h-24 bg-gradient-to-r from-primary to-secondary"></div>
      
      <!-- 用户信息 -->
      <div class="px-4 pb-4 relative">
        <div class="flex justify-between items-start">
          <!-- 头像 -->
          <div class="flex items-end -mt-10">
            <div class="w-20 h-20 rounded-full border-4 border-sky-200 overflow-hidden">
              <img src="@/assets/images/user.png" alt="用户头像" class="w-full h-full object-cover">
            </div>
            <div class="ml-3 mt-10">
              <h2 class="text-lg font-bold">{{ user.name }}</h2>
              <!-- <div class="flex items-center">
                <span class="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">{{ user.level }}</span>
              </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 菜单列表 -->
    <div class="card p-0 divide-y">
      <div 
        v-for="(item, index) in menuItems" 
        :key="item.id"
        class="flex items-center justify-between p-4 cursor-pointer"
        @click="handleMenuClick(item.link)"
      >
        <div class="flex items-center">
          <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-3">
            <i :class="['fas', item.icon, 'text-gray-500']"></i>
          </div>
          <span>{{ item.name }}</span>
        </div>
        <i class="fas fa-chevron-right text-gray-300"></i>
      </div>
    </div>
    
    <!-- 版本信息 -->
    <p class="text-center text-xs text-gray-400 mt-6">版本 1.0.2</p>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { getUserInfo } from "@/utils/userInfo";

const router = useRouter()
console.log('getUserInfo',getUserInfo());
const userInfo = getUserInfo()
// 用户数据
const user = ref({
  name: userInfo ? userInfo.nickname : 'xxx'
})

// 菜单项
const menuItems = ref([
  { id: 1, name: '我的资产', icon: 'fa-shopping-bag', link: '/my-assets' },
  { id: 3, name: '我的盘点', icon: 'fa-chart-line', link: '/my-inventory' },
  { id: 7, name: '系统设置', icon: 'fa-cog', link: '/settings' }
])

// 跳转到对应页面
const handleMenuClick = (link: string) => {
  router.push(link)
}
</script> 