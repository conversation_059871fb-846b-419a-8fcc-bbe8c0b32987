<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-medium">预算申请单</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <!-- 基本信息 -->
      <div class="mb-6">
        <BasicInfoCard
          :applicant="formData.createdBy"
          :department="formData.createdByDepartmentName"
          :apply-time="formData.createdAt"
        />
      </div>

      <form class="space-y-6">
        <!-- 基本表单信息 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4 space-y-4">
            <!-- 预算所属部门 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">预算所属部门</label>
              <div class="w-full p-2 bg-gray-50 rounded-md">
                {{ formData.departmentName }}
              </div>
            </div>

            <!-- 所属预算年 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">所属预算年</label>
              <div class="w-full p-2 bg-gray-50 rounded-md">
                {{ formData.budgetYear }}年
              </div>
            </div>

            <!-- 用途说明 -->
            <!-- <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">用途说明</label>
              <div class="w-full p-2 bg-gray-50 rounded-md min-h-[100px]">
                {{ formData.purpose }}
              </div>
            </div> -->

            <!-- 备注 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
              <div class="w-full p-2 bg-gray-50 rounded-md">
                {{ formData.remark || '无' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 预算明细 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <div class="flex justify-between items-center mb-4">
              <label class="block text-sm font-medium text-gray-700">
                预算明细
              </label>
            </div>
            
            <div v-if="formData.details.length === 0" class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg">
              <i class="fas fa-inbox text-3xl mb-2"></i>
              <p>暂无预算明细</p>
            </div>
            
            <div v-else class="space-y-3">
              <div 
                v-for="(item, index) in formData.details" 
                :key="index" 
                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-900">{{ item.name }}</div>
                  <div class="text-xs text-gray-500 mt-1">
                    <span>类别：{{ item.categoryName }}</span>
                    <span class="mx-2">|</span>
                    <span>单价：¥{{ item.unitPrice }}</span>
                    <span class="mx-2">|</span>
                    <span>数量：{{ item.quantity }}</span>
                  </div>
                  <div class="text-sm text-gray-500 mt-1">
                    小计：<span class="font-medium text-primary">¥{{ item.totalPrice }}</span>
                  </div>
                  <div class="text-sm text-gray-500 mt-1">
                    用途说明：<span class="font-medium">{{ item.purpose }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 总金额 -->
            <div class="flex justify-end items-center space-x-3 pt-4 mt-4 border-t border-gray-200">
              <span class="text-gray-500">总金额：</span>
              <span class="text-2xl font-semibold text-primary">¥{{ formData.totalAmount }}</span>
            </div>
          </div>
        </div>

        <!-- 附件列表 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <div class="mb-4">
              <label class="text-sm font-medium text-gray-700">附件列表</label>
              <NewFileUpload v-if="formData.files && formData.files.length" v-model="formData.files" :editable="false" class="mt-2"/>
              <div v-else class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg mt-2">
                <i class="fas fa-cloud-upload-alt text-3xl mb-2"></i>
                <p>暂无附件</p>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, defineAsyncComponent } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import BasicInfoCard from '@/views/order/BasicInfoCard.vue'
import { budgetOrderDetail } from '@/api/approval'
import NewFileUpload from '@/components/newFileUpload.vue'
const router = useRouter()
const route = useRoute()

interface Attachment {
  name: string;
  size: number;
  file: File;
  status: 'uploading' | 'success' | 'error';
  progress?: number;
}

// 表单数据
const formData = ref({
  files: [],
  details: []
})

// 返回上一页
const goBack = () => {
  router.back()
}

// 监听路由参数变化，获取申请单详情
onMounted(async () => {
  const orderId = route.query.id
  if (orderId) {
    try {
      budgetOrderDetail(orderId).then(res => {
        res.files = res.hasOwnProperty('files') && res.files ? res.files.split(',') : []
        formData.value = res
      })
    } catch (error) {
      console.error('获取申请单详情失败:', error)
      ElMessage.error('获取申请单详情失败')
    }
  }
})

// 添加申请单状态
const orderStatus = ref('rejected') // 可能的值：'processing'(审批中), 'rejected'(已驳回), 'approved'(已通过), 'cancelled'(已撤销)
</script>

<style scoped>
.page-container {
  background-color: rgb(249, 250, 251);
  min-height: 100vh;
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
  overscroll-behavior: none;
}

.page-container::-webkit-scrollbar {
  display: none;
  width: 0 !important;
}

.form-content {
  padding: 64px 8px 24px;
  min-height: calc(100vh - 64px);
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
  overscroll-behavior: none;
}

.form-content::-webkit-scrollbar {
  display: none;
  width: 0 !important;
}

/* 全局样式，确保所有容器都不显示滚动条 */
:deep(*) {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

:deep(*::-webkit-scrollbar) {
  display: none;
  width: 0 !important;
}

.form-group {
  @apply space-y-1 relative;
}

.label {
  @apply text-sm text-gray-600 block;
}

.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(24 144 255 / var(--tw-bg-opacity));
}

.text-primary {
  --tw-text-opacity: 1;
  color: rgb(24 144 255 / var(--tw-text-opacity));
}
</style> 