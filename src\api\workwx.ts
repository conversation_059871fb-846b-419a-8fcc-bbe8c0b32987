import { defHttp } from "@/axios";

// 根据 url 生成应用签名
export const getJsSDKAgentSignature = (params: { url: string }) => defHttp.get(
  {
    url: '/workwx/v1/jsSDKSignature/baigong',
    params,
    headers: {
      ignoreCancelToken: true,
    },
  }
);

// 根据 url 生成企业签名
export const getJsSDKConfigSignature = (params: { url: string }) => defHttp.get(
  {
    url: '/workwx/v1/jsSDKConfigSignature/baigong',
    params,
    headers: {
      ignoreCancelToken: true,
    },
  }
);

// 获取企业微信注册信息
export const getRegisterInfo = () => defHttp.get(
  {
    url: '/workwx/v1/registerInfo/baigong',
    headers: {
      ignoreCancelToken: true,
    },
  }
);

// 获取企业微信部门列表
export const getHpWxworkDepartment = () => defHttp.get(
  {
    url: '/hp/v1/hpWxworkDepartment',
    params: {
      nopaging: true,
    },
    headers: {
      ignoreCancelToken: true,
    },
  }
);
