<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-medium">调拨申请单</h1>
        <div class="w-8"></div>
      </div>
    </div>

    <!-- 申请表单 -->
    <div class="form-content">
      <form class="space-y-6">
        <!-- 基本信息 -->
        <BasicInfoCard
          :applicant="formData.createdBy"
          :department="formData.createdByDepartmentName"
          :apply-time="formData.createdAt"
        />

        <!-- 调入部门 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4 space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                接收部门
              </label>
              <div class="w-full p-2 bg-gray-50 rounded-md">
                {{ formData.departmentName }}
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                接收人员
              </label>
              <div class="w-full p-2 bg-gray-50 rounded-md">
                {{ formData.userName }}
              </div>
            </div>
          </div>
        </div>

        <!-- 调拨资产列表 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <div class="flex justify-between items-center mb-4">
              <label class="block text-sm font-medium text-gray-700">
                调拨资产
              </label>
            </div>
            
            <div v-if="!formData.details || formData.details.length === 0" class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg">
              <i class="fas fa-inbox text-3xl mb-2"></i>
              <p>暂未选择资产</p>
            </div>
            
            <div v-else class="space-y-3">
              <div 
                v-for="asset in formData.details" 
                :key="asset.id" 
                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-900">{{ asset.name }}</div>
                  <div class="text-xs text-gray-500">资产编号：{{ asset.no }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 调拨原因 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              调拨原因
            </label>
            <div class="w-full p-2 bg-gray-50 rounded-md min-h-[100px]">
              {{ formData.reason }}
            </div>
          </div>
        </div>

        <!-- 附件列表 -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-4">
            <div class="mb-4">
              <label class="text-sm font-medium text-gray-700">附件列表</label>
              <NewFileUpload v-if="formData.files && formData.files.length" v-model="formData.files" :editable="false" class="mt-2"/>
              <div v-else class="text-center py-8 text-gray-500 border-2 border-dashed rounded-lg mt-2">
                <i class="fas fa-cloud-upload-alt text-3xl mb-2"></i>
                <p>暂无附件</p>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineAsyncComponent } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import BasicInfoCard from '@/views/order/BasicInfoCard.vue'
import { transferOrderDetail } from '@/api/approval'
import NewFileUpload from '@/components/newFileUpload.vue'
const router = useRouter()
const route = useRoute()

interface Attachment {
  name: string;
  size: number;
  file: File;
  status: 'uploading' | 'success' | 'error';
  progress?: number;
}


// 表单数据
const formData = ref({
  files: [],
  details: []
})


// 监听路由参数变化，获取申请单详情
onMounted(async () => {
  const orderId = route.query.id
  if (orderId) {
    try {
      transferOrderDetail(orderId).then(res => {
        console.log('调拨详情', res);
        res.files = res.hasOwnProperty('files') && res.files ? res.files.split(',') : []
        formData.value = res
      })
    } catch (error) {
      console.error('获取申请单详情失败:', error)
      ElMessage.error('获取申请单详情失败')
    }
  }
})


// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style>
/* 全局样式 */
html, body {
  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none;
  }
  /* Firefox */
  scrollbar-width: none;
  /* IE and Edge */
  -ms-overflow-style: none;
}
</style>

<style scoped>
.page-container {
  background-color: rgb(249, 250, 251);
  min-height: 100vh;
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.page-container::-webkit-scrollbar {
  display: none;
}

.form-content {
  padding: 64px 8px 8px;
  min-height: calc(100vh - 64px);
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.form-content::-webkit-scrollbar {
  display: none;
}
</style> 