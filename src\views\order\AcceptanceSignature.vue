<template>
  <div class="acceptance-signature">
    <el-form :model="form" label-width="70px" class="signature-form">
      <el-form-item label="验收类型" class="conclusion-item">
        <el-radio-group v-model="form.type">
          <el-radio label="1">经办人</el-radio>
          <el-radio label="2">验收人</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="验收说明" class="comment-item">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          :autosize="{ minRows: 3, maxRows: 3 }"
          :placeholder="form.type === '1' ? '请输入经办说明（选填）' : '请输入验收说明（选填）'"
          resize="none"
        />
      </el-form-item>
      
      <el-form-item label="签名" class="signature-item">
        <div class="signature-container">
          <div class="signature-box">
            <canvas ref="signaturePadCanvas" class="signature-pad"></canvas>
          </div>
          <div class="signature-actions">
            <el-button size="small" plain @click="clearSignature">
              <i class="fas fa-redo-alt mr-1"></i>
              重新签名
            </el-button>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <div class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleConfirm"
        :disabled="!isValid"
      >
        确认提交
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import SignaturePad from 'signature_pad'

const emit = defineEmits<{
  (e: 'confirm', form: { type: string; remark: string; signUrl: string; time: string }): void
  (e: 'cancel'): void
}>()

const signaturePadCanvas = ref<HTMLCanvasElement | null>(null)
const signaturePad = ref<SignaturePad | null>(null)
const hasSignature = ref(false)

const form = ref({
  type: '1',
  remark: '',
  signUrl: ''
})

// 重置表单和签名
const reset = () => {
  form.value.type = '1'
  form.value.remark = ''
  form.value.signUrl = ''
  hasSignature.value = false
  if (signaturePad.value) {
    signaturePad.value.clear()
  }
}

// 暴露方法给父组件
defineExpose({
  reset
})

// 初始化签名板
const initSignaturePad = () => {
  if (signaturePadCanvas.value) {
    const canvas = signaturePadCanvas.value
    const container = canvas.parentElement
    
    // 获取容器的实际尺寸
    const { width } = container?.getBoundingClientRect() || { width: 0 }
    
    // 设置canvas的尺寸，考虑设备像素比
    const scale = window.devicePixelRatio || 1
    canvas.width = width * scale
    canvas.height = 160 * scale
    
    // 设置canvas的样式尺寸
    canvas.style.width = `${width}px`
    canvas.style.height = '160px'
    
    // 调整canvas的缩放以匹配设备像素比
    const ctx = canvas.getContext('2d')
    if (ctx) {
      ctx.scale(scale, scale)
    }

    // 如果已经存在签名板实例，先销毁
    if (signaturePad.value) {
      signaturePad.value.off()
    }

    // 创建新的签名板实例
    signaturePad.value = new SignaturePad(canvas, {
      backgroundColor: 'rgb(255, 255, 255)',
      penColor: 'rgb(0, 0, 0)',
      minWidth: 0.5,
      maxWidth: 2,
      throttle: 16,
      velocityFilterWeight: 0.2
    })

    // 监听签名变化
    signaturePad.value.addEventListener('endStroke', () => {
      if (signaturePad.value && !signaturePad.value.isEmpty()) {
        hasSignature.value = true
        form.value.signUrl = signaturePad.value.toDataURL()
      }
    })

    // 阻止默认的触摸行为
    const preventDefaultTouch = (e: TouchEvent) => {
      e.preventDefault()
    }

    canvas.addEventListener('touchstart', preventDefaultTouch, { passive: false })
    canvas.addEventListener('touchmove', preventDefaultTouch, { passive: false })
    canvas.addEventListener('touchend', preventDefaultTouch, { passive: false })

    canvas.style.touchAction = 'none'
  }
}

// 组件挂载时初始化
onMounted(() => {
  setTimeout(() => {
    initSignaturePad()
  }, 100)
  
  window.addEventListener('resize', resizeCanvas)
})

// 处理canvas尺寸调整
const resizeCanvas = () => {
  if (signaturePadCanvas.value && signaturePad.value) {
    const canvas = signaturePadCanvas.value
    const container = canvas.parentElement
    const { width } = container?.getBoundingClientRect() || { width: 0 }
    const scale = window.devicePixelRatio || 1
    
    const data = signaturePad.value.toData()
    
    canvas.width = width * scale
    canvas.height = 160 * scale
    canvas.style.width = `${width}px`
    canvas.style.height = '160px'
    
    const ctx = canvas.getContext('2d')
    ctx?.scale(scale, scale)
    
    signaturePad.value.fromData(data)
  }
}

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', resizeCanvas)
  if (signaturePad.value) {
    signaturePad.value.off()
  }
})

// 表单验证
const isValid = computed(() => {
  if (!hasSignature.value) return false
  return true
})

// 处理确认
const handleConfirm = () => {
  if (!signaturePad.value || signaturePad.value.isEmpty()) {
    ElMessage.warning('请签名')
    return
  }

  const currentTime = new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })

  emit('confirm', {
    ...form.value,
    time: currentTime
  })
  reset() // 确认后也重置
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  reset() // 取消时也重置
}

// 清除签名
const clearSignature = () => {
  if (!signaturePad.value) return
  signaturePad.value.clear()
  hasSignature.value = false
  form.value.signUrl = ''
}
</script>

<style scoped>
.acceptance-signature {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
}

.signature-form {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;

  :deep(.el-form-item__content) {
    width: 100%;
    box-sizing: border-box;
  }

  :deep(.el-form-item__label) {
    padding-right: 8px;
    line-height: 32px;
    height: 32px;
  }

  :deep(.el-radio-group) {
    display: flex;
    align-items: center;
    height: 32px;
    gap: 16px;
  }

  :deep(.el-radio) {
    margin-right: 0;
    height: 32px;
    display: flex;
    align-items: center;
  }

  .conclusion-item {
    margin-bottom: 20px;
    width: 100%;

    :deep(.el-form-item__content) {
      display: flex;
      align-items: center;
    }
  }

  .comment-item {
    margin-bottom: 20px;
    width: 100%;
  }

  .signature-item {
    margin-bottom: 0;
    width: 100%;
  }
}

.signature-container {
  border: none;
  padding: 0;
  background: none;
  width: 100%;
  box-sizing: border-box;
}

.signature-box {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
  width: 100%;
  touch-action: none;
  box-sizing: border-box;
}

.signature-pad {
  width: 100%;
  height: 160px;
  background-color: #fff;
  cursor: crosshair;
  display: block;
  touch-action: none;
  box-sizing: border-box;
}

.signature-actions {
  display: flex;
  justify-content: flex-start;
  margin-top: 4px;
  width: 100%;
  box-sizing: border-box;

  .el-button {
    display: inline-flex;
    align-items: center;
    font-size: 13px;
    height: 28px;
    padding: 0 12px;
    color: #606266;
    border: none;
    
    i {
      margin-right: 4px;
      font-size: 12px;
    }

    &:hover {
      background: none;
      color: #409EFF;
    }
  }
}

.dialog-footer {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  background: #fff;
  text-align: right;
  width: 100%;
  box-sizing: border-box;
  
  .el-button + .el-button {
    margin-left: 8px;
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-textarea__inner) {
  resize: none;
  border-radius: 4px;
  padding: 8px;
  font-size: 14px;
  width: 100%;
  box-sizing: border-box;
  
  &::placeholder {
    color: #9ca3af;
  }
}

:deep(.el-input__wrapper) {
  width: 100%;
  box-sizing: border-box;
}
</style> 