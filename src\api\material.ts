import { defHttp } from "../axios";

/**
 * 获取装备列表
 * @param data 
 * @returns 
 */
interface IHpMaterial {
  page: number;
  pageSize: number;
//   query: ({
//     status: number;
//     name__contains?: undefined;
// } | {
//     name__contains: string;
//     status?: undefined;
// })[];
  query: string;
  orderBy: string;
}
// const params: {
//   page: number;
//   pageSize: number;
//   query: ({
//       status: number;
//       name__contains?: undefined;
//   } | {
//       name__contains: string;
//       status?: undefined;
//   })[];
//   orderBy: string[];
// }
// params: {
//   page: 1,
//   pageSize: 10,
//   query: [{ status: "0" }],
//   orderBy: ["-updated_at", "-created_at"]
// }
export const hpMaterial = (data: IHpMaterial) => defHttp.get(
  {
    url: '/hp/v1/hpMaterial',
    params: data
  }
);