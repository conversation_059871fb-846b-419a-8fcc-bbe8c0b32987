import { defineStore } from 'pinia'

export interface FormItem {
  randID: string
  name: string
  quantity: number
  unit: string
  assetType: 'fixed' | 'intangible'
  brand?: string
  model?: string
  unitPrice: number
  totalAmount: number
  acquisitionMethod: string
  managementDepartment: string
  manager: string
  location?: string
  photos: string[]
}

export const useAcceptanceStore = defineStore('acceptance', {
  state: () => ({
    newItem: null as FormItem | null,
    items: [] as FormItem[],
    theme: '',
    acceptanceRecords: []
  }),
  
  actions: {
    setNewItem(item: FormItem) {
      console.log('Store: 设置新物品', item)
      this.newItem = item
    },
    updateItem(item: FormItem) {
      console.log('Store: 更新物品', item)
      const index = this.items.findIndex(i => i.randID === item.randID)
      if (index !== -1) {
        this.items[index] = item
      }
      console.log('Store: 当前物品列表99999', this.items);
      
    },
    addItem(item: FormItem) {
      console.log('Store: 添加物品到列表', item)
      this.items.push(item)
      // 添加后清空newItem
      this.newItem = null
      console.log('Store: 当前物品列表', this.items)
    },
    
    removeItem(index: number) {
      this.items.splice(index, 1)
    },
    
    clearNewItem() {
      this.newItem = null
    }
  }
}) 