<template>
  <div class="page-container bg-gray-50 min-h-screen flex flex-col">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-4">
        <button @click="handleBack" class="w-9 h-9 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition">
          <i class="fas fa-arrow-left text-gray-600"></i>
        </button>
        <h1 class="text-lg font-semibold text-gray-800">添加验收物品</h1>
        <div class="w-9"></div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="flex-1 overflow-y-auto">
      <div class="content-area pt-16 px-2 pb-24">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
          class="bg-white rounded-lg shadow-sm p-4"
        >
          <!-- 基本信息 -->
          <div class="mb-6">
            <h3 class="text-base font-medium text-gray-900 mb-4">基本信息</h3>
            <el-form-item label="物品名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入物品名称" />
            </el-form-item>
            <el-form-item label="资产类型" prop="assetType">
              <el-radio-group v-model="formData.assetType">
                <el-radio label="fixed">固定资产</el-radio>
                <el-radio label="intangible">无形资产</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

          <!-- 固定资产信息 -->
          <template v-if="formData.assetType === 'fixed'">
            <div class="mb-6">
              <h3 class="text-base font-medium text-gray-900 mb-4">固定资产信息</h3>
              <el-form-item label="品牌" prop="brand">
                <el-input v-model="formData.brand" placeholder="请输入品牌" />
              </el-form-item>
              <el-form-item label="规格型号" prop="model">
                <el-input v-model="formData.model" placeholder="请输入规格型号" />
              </el-form-item>
              <el-form-item label="存放地点" prop="location">
                <el-input v-model="formData.location" placeholder="请输入存放地点" />
              </el-form-item>
            </div>
          </template>

          <!-- 价格信息 -->
          <div class="mb-6">
            <h3 class="text-base font-medium text-gray-900 mb-4">价格信息</h3>
            <el-form-item label="单价" prop="unitPrice">
              <el-input-number v-model="formData.unitPrice" :min="0" :precision="2" class="w-full" />
            </el-form-item>
            <el-form-item label="数量" prop="quantity">
              <el-input-number v-model="formData.quantity" :min="1" class="w-full" />
            </el-form-item>
            <div class="custom-form-item">
              <div class="custom-label">计量单位</div>
              <div class="el-form-item__content">
                <el-input v-model="formData.unit" placeholder="请输入计量单位" />
              </div>
            </div>
          </div>

          <!-- 管理信息 -->
          <div class="mb-6">
            <h3 class="text-base font-medium text-gray-900 mb-4">管理信息</h3>
            <el-form-item label="取得方式" prop="acquisitionMethod">
              <el-select v-model="formData.acquisitionMethod" placeholder="请选择取得方式" class="w-full">
                <el-option label="采购" value="采购" />
                <el-option label="调拨" value="调拨" />
                <el-option label="捐赠" value="捐赠" />
                <el-option label="自主研发" value="自主研发" />
              </el-select>
            </el-form-item>
            <el-form-item label="管理部门" prop="managementDepartment">
              <div
                class="w-full px-4 py-2 border border-gray-300 rounded-lg text-base bg-white cursor-pointer flex items-center justify-between department-selector"
                @click="openDepartmentPicker"
              >
                <span v-if="formData.managementDepartment">{{ formData.managementDepartment }}</span>
                <span v-else class="text-gray-400">请选择管理部门</span>
                <i class="fas fa-chevron-down text-gray-400"></i>
              </div>
              <el-button @click="openDepartmentPicker" class="mt-2" size="small">点击打开部门选择</el-button>
            </el-form-item>
            
            <el-form-item label="管理人" prop="manager">
              <el-select v-model="formData.manager" placeholder="请选择管理人" class="w-full">
                <el-option label="张三" value="张三" />
                <el-option label="李四" value="李四" />
                <el-option label="王五" value="王五" />
                <el-option label="赵六" value="赵六" />
              </el-select>
            </el-form-item>
          </div>

          <!-- 照片上传 -->
          <div class="mb-6">
            <h3 class="text-base font-medium text-gray-900 mb-4">照片上传</h3>
            <el-form-item label="物品照片">
              <el-upload
                action="#"
                list-type="picture-card"
                :auto-upload="false"
                :on-change="handlePhotoChange"
                :limit="5"
                multiple
              >
                <i class="fas fa-plus"></i>
              </el-upload>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-4 py-3">
      <el-button type="primary" class="w-full" @click="handleSubmit" :loading="submitting">
        确认添加
      </el-button>
      
      <!-- 在开发环境中显示的测试按钮 -->
      <div class="mt-2">
        <el-button type="info" @click="openDepartmentPicker" size="small" class="w-full">
          测试部门选择器
        </el-button>
      </div>
      
      <!-- 简单的Vant弹窗测试 -->
      <div class="mt-2">
        <el-button type="warning" @click="showVantPopup = true" size="small" class="w-full">
          测试简单弹窗
        </el-button>
      </div>
    </div>
    
    <!-- 简单的Vant弹窗 -->
    <van-popup 
      v-model:show="showVantPopup" 
      position="bottom" 
      round 
      style="height: 30%"
    >
      <div class="p-4">
        <h3 class="text-lg font-medium mb-2">简单弹窗测试</h3>
        <p>这是一个简单的Vant弹窗测试</p>
        <div class="mt-4">
          <button @click="showVantPopup = false" class="w-full bg-blue-500 text-white py-2 rounded">
            关闭
          </button>
        </div>
      </div>
    </van-popup>
    
    <!-- 部门选择弹窗 -->
    <van-popup
      v-model:show="showDepartmentPicker"
      position="bottom"
      round
      style="height: 60%"
    >
      <van-cascader
        v-model="departmentValue"
        title="请选择管理部门"
        :options="departments"
        @close="showDepartmentPicker = false"
        @finish="onDepartmentFinish"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useAcceptanceStore } from '@/stores/acceptance'
import { showToast } from 'vant'
// 不需要手动导入 Vant 组件，因为它们已经在 main.ts 中全局注册
import 'vant/lib/index.css'

const router = useRouter()
const store = useAcceptanceStore()
const formRef = ref<FormInstance>()
const submitting = ref(false)

// 部门选择相关
const showDepartmentPicker = ref(false)
const departmentValue = ref('')
const departmentText = ref('')

// 简单弹窗测试
const showVantPopup = ref(false)

// 部门数据结构
interface DepartmentOption {
  text: string;
  value: string;
  children?: DepartmentOption[];
}

interface FinishEvent {
  selectedOptions: DepartmentOption[];
}

const departments: DepartmentOption[] = [
  {
    text: '行政中心',
    value: 'admin',
    children: [
      {
        text: '行政部',
        value: '行政部',
      },
      {
        text: '人力资源部',
        value: '人力资源部',
      },
      {
        text: '财务部',
        value: '财务部',
      },
    ],
  },
  {
    text: '研发中心',
    value: 'rd',
    children: [
      {
        text: '前端组',
        value: '前端组',
      },
      {
        text: '后端组',
        value: '后端组',
      },
      {
        text: '测试组',
        value: '测试组',
      },
    ],
  },
  {
    text: '产品中心',
    value: 'product',
    children: [
      {
        text: '产品组',
        value: '产品组',
      },
      {
        text: 'UI设计组',
        value: 'UI设计组',
      },
    ],
  },
  {
    text: 'IT中心',
    value: 'it',
    children: [
      {
        text: 'IT部',
        value: 'IT部',
      },
      {
        text: '运维部',
        value: '运维部',
      },
    ],
  },
]

// 处理部门选择完成
const onDepartmentFinish = (data: { selectedOptions: DepartmentOption[] }) => {
  // 生成完整路径文本（例如：行政中心/行政部）
  departmentText.value = data.selectedOptions.map((option: DepartmentOption) => option.text).join('/')
  
  // 存储最后一级的值
  const lastOption = data.selectedOptions[data.selectedOptions.length - 1];
  departmentValue.value = lastOption.value
  
  // 更新表单数据
  formData.managementDepartment = lastOption.value
  
  // 关闭弹窗
  showDepartmentPicker.value = false
  
  // 显示选择成功的提示
  showToast('已选择: ' + departmentText.value);
}

// 手动打开部门选择器
const openDepartmentPicker = () => {
  showDepartmentPicker.value = !showDepartmentPicker.value;
  console.log('部门选择器状态切换为:', showDepartmentPicker.value);
}

// 弹窗关闭时的回调
const onPopupClosed = () => {
  console.log('弹窗已关闭');
}

// 部门选项分组（保留一个空数组，用于消除类型错误）
const departmentOptions = []

// 表单数据
interface FormData {
  name: string
  quantity: number
  unit: string
  assetType: 'fixed' | 'intangible'
  brand?: string
  model?: string
  unitPrice: number
  totalAmount: number
  acquisitionMethod: string
  managementDepartment: string
  manager: string
  location?: string
  photos: string[]
}

const formData = reactive<FormData>({
  name: '',
  quantity: 1,
  unit: '',
  assetType: 'fixed',
  brand: '',
  model: '',
  unitPrice: 0,
  totalAmount: 0,
  acquisitionMethod: '',
  managementDepartment: '',
  manager: '',
  location: '',
  photos: []
})

// 表单验证规则
const formRules: FormRules = {
  name: [{ required: true, message: '请输入物品名称', trigger: 'blur' }],
  assetType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
  brand: [{ required: true, message: '请输入品牌', trigger: 'blur' }],
  model: [{ required: true, message: '请输入规格型号', trigger: 'blur' }],
  unitPrice: [{ required: true, message: '请输入单价', trigger: 'blur' }],
  quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
  acquisitionMethod: [{ required: true, message: '请选择取得方式', trigger: 'change' }],
  managementDepartment: [{ required: true, message: '请选择管理部门', trigger: 'change' }],
  manager: [{ required: true, message: '请输入管理人', trigger: 'blur' }],
  location: [{ required: true, message: '请输入存放地点', trigger: 'blur' }]
}

// 处理照片上传
const handlePhotoChange = (file: { raw: File; name: string; size: number }) => {
  // TODO: 实现照片上传逻辑
  console.log('照片上传:', file)
}

// 计算总金额
const totalAmount = computed(() => {
  return formData.unitPrice * formData.quantity
})

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      submitting.value = true
      // 计算总金额
      formData.totalAmount = totalAmount.value
      
      // 将数据保存到 store 并直接添加到列表
      const itemData = { ...formData };
      console.log('提交的物品数据:', itemData);
      
      // 直接添加到物品列表
      store.addItem(itemData);
      
      // 返回上一页
      ElMessage.success('添加物品成功')
      setTimeout(() => {
        router.push('/acceptance-registration')
      }, 100)
    }
  })
}

// 返回验收登记页面
const handleBack = () => {
  if (store.newItem) {
    // 如果有未保存的数据，先清空
    store.clearNewItem()
  }
  router.push('/acceptance-registration')
}

// 在组件挂载后执行
onMounted(() => {
  console.log('组件已挂载，Vant组件应该可用');
  
  // 尝试初始化状态
  showDepartmentPicker.value = false;
  console.log('部门选择器状态已初始化为关闭');
  
  // 添加全局点击监听，确保部门选择器正常工作
  const departmentSelector = document.querySelector('.department-selector');
  if (departmentSelector) {
    departmentSelector.addEventListener('click', () => {
      console.log('通过DOM事件点击部门选择器');
      openDepartmentPicker();
    });
  }
  
  // 确保在一秒后尝试打开一次简单弹窗，测试Vant是否工作
  setTimeout(() => {
    console.log('尝试自动打开简单弹窗进行测试');
    showVantPopup.value = true;
    setTimeout(() => {
      showVantPopup.value = false;
    }, 2000);
  }, 1000);
})
</script>

<style scoped>
.content-area {
  min-height: calc(100vh - 5rem);
  max-width: 700px;
  margin: 0 auto;
}

/* 隐藏滚动条但保持滚动功能 */
.content-area::-webkit-scrollbar {
  display: none;
}

.page-container {
  height: 100vh;
  overflow: hidden;
}

/* 隐藏滚动条但保持滚动功能 */
.flex-1::-webkit-scrollbar {
  display: none;
}

.flex-1 {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  @apply text-gray-700 font-medium;
  font-size: 14px;
  padding: 0 8px 8px 0;
  line-height: 1.5;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__inner) {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

:deep(.el-input__wrapper:hover),
:deep(.el-textarea__inner:hover) {
  border-color: #d1d5db;
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-textarea__inner:focus) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

:deep(.el-radio) {
  margin-right: 20px;
}

:deep(.el-radio__label) {
  font-size: 14px;
  padding-left: 8px;
}

:deep(.el-upload--picture-card) {
  --el-upload-picture-card-size: 100px;
  border-radius: 8px;
  border: 1px dashed #d1d5db;
  background-color: #f9fafb;
}

:deep(.el-upload--picture-card:hover) {
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-select .el-input__wrapper) {
  padding: 0 12px;
}

:deep(.el-select-dropdown__item) {
  padding: 8px 12px;
  font-size: 14px;
}

:deep(.el-select-group__title) {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  padding: 8px 12px;
  background-color: #f5f7fa;
}

:deep(.el-select-group .el-select-dropdown__item) {
  padding-left: 24px;
}

/* 自定义计量单位字段样式 */
.custom-form-item {
  margin-bottom: 20px;
  display: flex;
}

.custom-label {
  width: 100px;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  padding: 0 8px 8px 0;
  line-height: 32px;
  text-align: right;
}

:deep(.el-form-item__content) {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}

:deep(.no-asterisk) {
  .el-form-item__label::before {
    display: none !important;
    content: '' !important;
  }
}

/* Vant组件样式覆盖 */
:deep(.van-cascader__header) {
  @apply text-gray-900;
  padding: 16px;
  font-size: 16px;
  font-weight: 500;
}

:deep(.van-cascader__options) {
  height: calc(100% - 48px);
}

:deep(.van-cascader__option) {
  @apply text-gray-700;
  padding: 14px 16px;
  font-size: 14px;
}

:deep(.van-cascader__option--selected) {
  @apply text-primary;
  font-weight: 500;
}

:deep(.van-cascader__option--active) {
  background-color: #f0f9ff;
}

:deep(.van-popup) {
  @apply max-h-[80vh];
}

:deep(.van-cascader__tabs) {
  height: 100%;
}

.text-primary {
  --tw-text-opacity: 1;
  color: rgb(24 144 255 / var(--tw-text-opacity));
}
</style> 