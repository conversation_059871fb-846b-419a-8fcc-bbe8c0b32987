<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 定义预算数据类型
interface BudgetData {
  department: string;
  totalAmount: number;
  usedAmount: number;
  remainingAmount: number;
  percentage: number;
  year: number;
}

// 生成年份选项（从2020年到当前年份）
const currentYear = new Date().getFullYear()
const yearOptions = Array.from({ length: currentYear - 2019 }, (_, i) => currentYear - i)
const selectedYear = ref(currentYear)

// 模拟预算数据
const allBudgetData = ref<BudgetData[]>([
  {
    department: '技术部',
    totalAmount: 1000000,
    usedAmount: 450000,
    remainingAmount: 550000,
    percentage: 45,
    year: currentYear
  },
  {
    department: '财务部',
    totalAmount: 500000,
    usedAmount: 200000,
    remainingAmount: 300000,
    percentage: 40,
    year: currentYear
  },
  {
    department: '人力资源部',
    totalAmount: 300000,
    usedAmount: 150000,
    remainingAmount: 150000,
    percentage: 50,
    year: currentYear
  },
  {
    department: '市场部',
    totalAmount: 800000,
    usedAmount: 600000,
    remainingAmount: 200000,
    percentage: 75,
    year: currentYear
  },
  {
    department: '销售部',
    totalAmount: 1200000,
    usedAmount: 900000,
    remainingAmount: 300000,
    percentage: 75,
    year: currentYear
  },
  // 添加去年的数据
  {
    department: '技术部',
    totalAmount: 800000,
    usedAmount: 750000,
    remainingAmount: 50000,
    percentage: 94,
    year: currentYear - 1
  },
  {
    department: '财务部',
    totalAmount: 400000,
    usedAmount: 380000,
    remainingAmount: 20000,
    percentage: 95,
    year: currentYear - 1
  }
])

// 根据选择的年份筛选数据
const budgetList = computed(() => {
  return allBudgetData.value.filter(item => item.year === selectedYear.value)
})

// 返回上一页
const goBack = () => {
  router.back()
}

// 格式化金额
const formatAmount = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}
</script>

<template>
  <div class="page-container min-h-screen bg-gray-50 overflow-hidden">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center px-4 py-3 relative">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center absolute left-4">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-bold flex-1 text-center">预算查询</h1>
        <select 
          v-model="selectedYear"
          class="px-2 py-1 text-sm border border-gray-200 rounded-lg focus:outline-none focus:border-blue-500 bg-transparent absolute right-4"
        >
          <option v-for="year in yearOptions" :key="year" :value="year">
            {{ year }}年
          </option>
        </select>
      </div>
    </div>

    <!-- 统计列表 -->
    <div class="pt-16 px-4 pb-6">
      <div v-if="budgetList.length > 0" class="space-y-4">
        <div v-for="item in budgetList" :key="item.department" class="bg-white rounded-lg p-4 shadow-sm">
          <div class="flex justify-between items-start mb-3">
            <h3 class="text-lg font-medium text-gray-900">{{ item.department }}</h3>
            <div class="text-sm text-gray-500">
              使用率：<span :class="[
                item.percentage >= 80 ? 'text-red-500' :
                item.percentage >= 60 ? 'text-orange-500' :
                'text-green-500'
              ]">{{ item.percentage }}%</span>
            </div>
          </div>
          
          <!-- 进度条 -->
          <div class="relative h-2 bg-gray-200 rounded-full mb-4">
            <div
              class="absolute left-0 top-0 h-full rounded-full transition-all duration-300"
              :class="[
                item.percentage >= 80 ? 'bg-red-500' :
                item.percentage >= 60 ? 'bg-orange-500' :
                'bg-green-500'
              ]"
              :style="{ width: `${item.percentage}%` }"
            ></div>
          </div>

          <div class="grid grid-cols-3 gap-4 text-center">
            <div>
              <div class="text-sm text-gray-500 mb-1">总预算</div>
              <div class="font-medium text-gray-900">{{ formatAmount(item.totalAmount) }}</div>
            </div>
            <div>
              <div class="text-sm text-gray-500 mb-1">已使用</div>
              <div class="font-medium text-gray-900">{{ formatAmount(item.usedAmount) }}</div>
            </div>
            <div>
              <div class="text-sm text-gray-500 mb-1">剩余预算</div>
              <div class="font-medium text-gray-900">{{ formatAmount(item.remainingAmount) }}</div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="bg-white rounded-lg p-8 shadow-sm text-center">
        <i class="fas fa-search text-gray-300 text-3xl mb-3"></i>
        <p class="text-gray-500">该年度暂无预算数据</p>
      </div>
    </div>
  </div>
</template>

<style>
/* 全局样式 */
html, body {
  overflow: hidden !important;
  -ms-overflow-style: none !important;
  scrollbar-width: none !important;
}

html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
}
</style>

<style scoped>
.page-container {
  height: 100vh;
  overflow-y: auto;
  -ms-overflow-style: none !important;
  scrollbar-width: none !important;
  position: relative;
}

.page-container::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
}

/* 确保内容区域可以正常滚动 */
.px-4 {
  overflow-y: auto;
  -ms-overflow-style: none !important;
  scrollbar-width: none !important;
}

.px-4::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
}
</style> 