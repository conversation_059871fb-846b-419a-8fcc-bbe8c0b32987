# DepartmentPicker 部门选择器组件

这是一个基于 Vant 组件库的部门选择器组件，用于在表单中选择部门信息。组件会自动从后端获取部门数据，并进行层级展示。

## 基本使用

```vue
<template>
  <DepartmentPicker v-model="departmentId" title="预算所属部门" />
</template>

<script setup>
import { ref } from 'vue'
import DepartmentPicker from '@/components/DepartmentPicker.vue'

const departmentId = ref('')
</script>
```

## 属性

| 属性名      | 类型                  | 默认值        | 说明                 |
|------------|----------------------|--------------|---------------------|
| modelValue | string/number        | ''           | 选中的部门ID，支持v-model |
| placeholder| string               | '请选择部门'    | 未选择时的占位文本       |
| title      | string               | '请选择部门'    | 选择器弹窗的标题         |
| fieldNames | object               | { text: 'name', value: 'departmentId', children: 'children' } | 自定义字段名映射 |

## 事件

| 事件名           | 参数                                                   | 说明                 |
|-----------------|-------------------------------------------------------|---------------------|
| update:modelValue| (value: string \| number)                            | 值更新时触发           |
| finish          | { value, displayText, selectedOptions }               | 选择完成时触发         |
| close           | -                                                     | 弹窗关闭时触发         |
| change          | (value: string \| number)                             | 值变化时触发           |

## 更多示例

### 自定义占位符和标题

```vue
<DepartmentPicker
  v-model="departmentId"
  placeholder="选择您的部门"
  title="部门选择"
/>
```

### 使用事件获取更多信息

```vue
<DepartmentPicker
  v-model="departmentId"
  @finish="onDepartmentSelected"
/>

<script setup>
const onDepartmentSelected = (data) => {
  console.log('选中的部门ID:', data.value)
  console.log('选中的部门名称:', data.displayText)
  console.log('选中的部门路径:', data.selectedOptions)
}
</script>
```

## 数据格式

组件会自动调用 `getHpWxworkDepartment` 接口获取部门数据，该接口返回的格式为：

```typescript
interface HpWxworkDepartment {
  id: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
  remark?: string;
  departmentId: number;
  name: string;
  parentId: number;
  children?: HpWxworkDepartment[];
  wxworkOrder: number;
  hasChildren?: boolean;
}
```

组件内部会将这个数据格式转换为 Vant Cascader 组件需要的格式：

```typescript
interface CascaderOption {
  text: string;   // 显示文本
  value: string | number;  // 值
  children?: CascaderOption[];  // 子项
}
```

## 注意事项

1. 组件需要 Vant 组件库的支持
2. 组件会自动处理部门数据的层级结构
3. 默认情况下，组件会在第一次打开时加载部门数据 