import CryptoJS from 'crypto-js';

// 密钥
const key = CryptoJS.enc.Utf8.parse("c67f89017a384969"); // 16字节密钥

// 加密函数
export function encrypt(plainText:string) {
    const iv = CryptoJS.lib.WordArray.random(16); // 生成随机初始向量
    const encrypted = CryptoJS.AES.encrypt(plainText, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    // 返回初始向量和密文的组合，使用 base64 编码
    return iv.toString() + ':' + encrypted.toString();
}


// 解密函数
export function decrypt(encryptedText:string) {
    const parts = encryptedText.split(':');
    const iv = CryptoJS.enc.Hex.parse(parts[0]); // 提取初始向量
    console.log({ iv })
    const encrypted = parts[1]; // 提取密文
    const decrypted = CryptoJS.AES.decrypt(encrypted, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    return decrypted.toString(CryptoJS.enc.Utf8); // 返回解密后的字符串
}

// const v = encrypt('hello world')
// const r = decrypt(v) // hello world
// console.log({v,r})
