<script setup lang="ts">
import { onMounted, ref } from 'vue'
import OrderListNoStatus from './order/OrderListNoStatus.vue'
import Check from '@/components/Check.vue'
import { approvalList } from '@/api/approval'
import { myAuditplan } from '@/api/equipment'

const activeTab = ref('approval')
const approvalTotal = ref(0)
const checkTotal = ref(0)
onMounted(() => {
  approvalList({query: JSON.stringify({ open_sp_status: [1]}), nopaging: true}).then(res => {
    approvalTotal.value = res.total
  })
  myAuditplan({nopaging: true, query: JSON.stringify([{status: '1'}])}).then(res => {
    checkTotal.value = res.total
  })
})
</script>

<template>
  <div class="">
    <van-nav-bar fixed title="任务中心" />
    <div class="fixed-tabs">
      <el-tabs v-model="activeTab" class="custom-tabs">
        <el-tab-pane :label="`审批（${approvalTotal}）`" name="approval"/>
        <el-tab-pane :label="`盘点（${checkTotal}）`" name="check" />
      </el-tabs>
    </div>
    <!-- 内容区域 -->
    <div class="pt-[88px]">
      <OrderListNoStatus v-if="activeTab === 'approval'" />
      <Check v-if="activeTab === 'check'" />
    </div>
  </div>
</template>

<style scoped>
.page-container {
  min-height: 100vh;
  height: 100vh;
  overflow: hidden;
}
.fixed-tabs {
  position: fixed;
  top: 46px;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: white;
}
.custom-tabs {
  :deep(.el-tabs__header) {
    margin: 0;
    background-color: white;
  }

  :deep(.el-tabs__nav-wrap) {
    padding: 0;
  }

  :deep(.el-tabs__nav) {
    width: 100%;
  }

  :deep(.el-tabs__item) {
    font-size: 14px;
    height: 44px;
    line-height: 44px;
    width: 50% !important;
    text-align: center;
  }

  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
  }

  :deep(.el-tabs__content) {
    display: none;
  }

  :deep(.el-tabs__active-bar) {
    width: 50% !important;
  }
}
.no-scrollbar::-webkit-scrollbar {
  display: none;
  width: 0;
}

.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
  overflow-y: scroll;
  overflow-x: hidden;
}

/* 添加一个通用的滚动条隐藏样式 */
* {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
</style>
