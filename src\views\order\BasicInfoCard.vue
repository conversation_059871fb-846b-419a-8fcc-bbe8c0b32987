<template>
  <div class="bg-white rounded-lg shadow-sm">
    <div class="p-4">
      <div class="flex justify-between items-center mb-4">
        <label class="block text-sm font-medium text-gray-700">基本信息</label>
      </div>
      <div class="space-y-3">
        <div class="flex text-sm">
          <span class="whitespace-nowrap">申请人：</span>
          <span class="text-gray-900 ml-2">{{ applicant }}</span>
        </div>
        <div class="flex text-sm">
          <span class="whitespace-nowrap">申请人部门：</span>
          <span class="text-gray-900 ml-2">{{ department }}</span>
        </div>
        <div class="flex text-sm">
          <span class="whitespace-nowrap">申请时间：</span>
          <span class="text-gray-900 ml-2">{{ applyTime }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  applicant: string
  department: string
  applyTime: string
}

defineProps<Props>()
</script>

<style scoped>
.bg-white {
  @apply border border-gray-100;
}
</style> 