<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { hpWxworkDepartment, hpMaterialType } from '@/api/equipment'
import { addOrderRecord, editOrderRecord, delOrderRecord } from '@/api/equipment'
import { showConfirmDialog } from 'vant';
import { showToast } from 'vant'
import ImageUpload from '@/components/ImageUpload.vue'
import NewFileUpload from '@/components/newFileUpload.vue'
const router = useRouter()
const route = useRoute()
const minDate = new Date()
const maxDate = new Date(2050, 12, 1)
const timeValue = ref([])
// 判断是否为编辑模式
const isEditMode = computed(() => route.query.mode === 'edit')
interface DepartmentOption {
  text: string;
  value: string;
  children?: DepartmentOption[];
}
interface FinishEvent {
  selectedOptions: DepartmentOption[];
}
interface FormData {
  departmentId: number;
  name: string;
  departmentName?: string;
  typeName?: string;
  type: string;
  quantity: string;
  unitPrice: string;
  totalPrice: string;
  purpose: string;
  useTime: string;
  files: Attachment[];
  images: Attachment[];
  remark: string;
}

// 表单数据
const formData = ref<FormData>({
  departmentId: 0, // 申请部门
  name: '', // 资产名称
  type: '', // 资产类型
  quantity: '', // 预购数量
  unitPrice: '', // 预计单价
  totalPrice: '0.00', // 总金额
  purpose: '', // 用途说明
  useTime: '', // 预计使用日期
  files: [], // 附件
  images: [], // 图片
  remark: '' // 备注
})

// 部门选择相关
const showDepartmentPicker = ref(false)
const showTypePicker = ref(false)
const showTimePicker = ref(false)
const typeName = ref('')
const departmentName = ref('')

// 部门数据结构
const departments = ref([])
const typeOptions = ref([])
const removeEmptyChildren = (arr) => {
  return arr.map(item => {
    // 浅拷贝对象（避免修改原对象）
    const newItem = { ...item };

    // 递归处理子节点
    if (newItem.children && Array.isArray(newItem.children)) {
      const processedChildren = removeEmptyChildren(newItem.children);
      if (processedChildren.length > 0) {
        newItem.children = processedChildren;
      } else {
        delete newItem.children; // 删除空children字段
      }
    }

    return newItem;
  });
}
const getAllData = () => {
  hpWxworkDepartment().then(res => {
    console.log('部门ressssss', res);
    departments.value = removeEmptyChildren(res.items)
    // departments.value = res.items
  })
  hpMaterialType().then(res => {
    console.log('类型', res);
    typeOptions.value = removeEmptyChildren(res.items)
  })

}
getAllData()
// 处理部门选择完成
const onDepartmentFinish = ({ selectedOptions }: FinishEvent) => {
  console.log('选择部门', selectedOptions);
  departmentName.value = selectedOptions.map((option) => option.name).join('/')
  showDepartmentPicker.value = false
}
const onTypeFinish = ({ selectedOptions }: FinishEvent) => {
  typeName.value = selectedOptions.map((option) => option.name).join('/')
  showTypePicker.value = false
}
// 初始化表单数据
const initFormData = () => {
  if (isEditMode.value && route.query.data) {
    try {
      const recordData = JSON.parse(route.query.data)
      formData.value = { ...recordData }
      departmentName.value = formData.value.departmentName
      typeName.value = formData.value.typeName
      formData.value.files = formData.value.files ? formData.value.files.split(',') : []
      formData.value.images = formData.value.images ? formData.value.images.split(',') : []
      console.log('编辑数据', formData.value);
      // formData.value = {
      //   ...formData.value,
      //   departmentId: recordData.departmentId,
      //   name: recordData.name,
      //   type: recordData.type,
      //   quantity: recordData.quantity.toString(),
      //   unitPrice: recordData.unitPrice.toString(),
      //   purpose: recordData.purpose,
      //   useTime: recordData.useTime,
      //   images: recordData.images || [],
      //   files: recordData.files || [],
      //   remark: recordData.remark || ''
      // }
    } catch (error) {
      console.error('解析编辑数据失败:', error)
    }
  }
}

// 在组件挂载时初始化数据
onMounted(() => {
  initFormData()
})

// 返回上一页
const goBack = () => {
  router.back()
}
const timeConfirm = () => {
  formData.value.useTime = timeValue.value.join('-')
  console.log('timetime', formData.value.useTime);
  showTimePicker.value = false
}

// 提示框状态
const showSuccessModal = ref(false)

// 监听数量和单价的变化，计算总金额
watch(
  [() => formData.value.quantity, () => formData.value.unitPrice],
  ([newQuantity, newPrice]) => {
    const quantity = Number(newQuantity) || 0
    const price = Number(newPrice) || 0
    formData.value.totalPrice = (quantity * price).toFixed(2)
  },
  { immediate: true }
)

// 提交状态
const isSubmitting = ref(false)
// 提交表单
const submitForm = async () => {
  // 表单验证
  if (!formData.value.departmentId) {
    ElMessage.error('请选择申请部门')
    return
  }
  if (!formData.value.name) {
    ElMessage.error('请输入资产名称')
    return
  }
  if (!formData.value.type) {
    ElMessage.error('请选择资产类型')
    return
  }
  if (!formData.value.quantity || Number(formData.value.quantity) <= 0) {
    ElMessage.error('请输入有效的预购数量')
    return
  }
  if (!formData.value.unitPrice || Number(formData.value.unitPrice) <= 0) {
    ElMessage.error('请输入有效的预计单价')
    return
  }
  if (!formData.value.purpose) {
    ElMessage.error('请填写用途说明')
    return
  }
  if (!formData.value.useTime) {
    ElMessage.error('请选择预计使用日期')
    return
  }

  try {
    const data = {
      ...formData.value,
      files: formData.value.files && formData.value.files.length ? formData.value.files.join(',') : '',
      images: formData.value.images && formData.value.images.length ? formData.value.images.join(',') : ''
    }
    console.log('提交的表单数据：', data)
    isSubmitting.value = true
    // // TODO: 这里添加提交到后端的逻辑
    let api = isEditMode.value ? editOrderRecord : addOrderRecord 
    api(data).then(res => {
      console.log('resss', res);
      showSuccessModal.value = true
      router.back()
    })

    // // 显示成功提示
    // showSuccessModal.value = true

    // // 3秒后自动返回
    // setTimeout(() => {
    //   showSuccessModal.value = false
    //   router.back()
    // }, 3000)
  } catch (error) {
    console.error('提交失败：', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    isSubmitting.value = false
    showSuccessModal.value = false
  }
}

// 删除记录
const handleDelete = async () => {
  showConfirmDialog({
    title: '确认删除',
    message: '确认要删除这条记录吗？',
    // confirm-button-text: '确定'
    confirmButtonColor: '#ef4444'
  })
    .then(() => {
      delOrderRecord(formData.value.id).then(res => {
        console.log('ressssss', res);
        showToast('删除成功')
        router.back()
      })
    })
    .catch(() => {
      // on cancel
    });
}
</script>

<template>
  <div class="page-container min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <div class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
      <div class="flex items-center justify-between px-4 py-3">
        <button @click="goBack" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-arrow-left text-gray-500"></i>
        </button>
        <h1 class="text-lg font-bold">{{ isEditMode ? '编辑预购' : '预购登记' }}</h1>
        <button
          v-if="isEditMode"
          @click="handleDelete"
          class="w-8 h-8 rounded-full text-gray-400 hover:text-red-500 hover:bg-red-50 transition-all duration-300 flex items-center justify-center"
        >
          <i class="fas fa-trash-alt"></i>
        </button>
        <div v-else class="w-8"></div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="pt-14 pb-24">
      <div class="bg-white p-6 space-y-6 mb-4 max-w-xl mx-auto">
        <div class="form-group horizontal">
          <label class="label"><span class="text-red-500">*</span>所属部门</label>
          <div class="flex-1">
            <div
              class="w-full px-4 py-2 border border-gray-300 rounded-lg text-base bg-white"
              @click="showDepartmentPicker = true"
            >
              <span v-if="departmentName">{{ departmentName }}</span>
              <span v-else class="text-gray-400">请选择部门</span>
            </div>
          </div>
        </div>
          <!-- 部门选择弹窗 -->
          <van-popup
            v-model:show="showDepartmentPicker"
            round
            position="bottom"
          >
            <van-cascader
              v-model="formData.departmentId"
              title="请选择所属部门"
              :options="departments"
              :field-names="{text: 'name', value: 'departmentId'}"
              @close="showDepartmentPicker = false"
              @finish="onDepartmentFinish"
            />
          </van-popup>
        <!-- 资产名称 -->
        <div class="form-group">
          <label class="label"><span class="text-red-500">*</span>资产名称</label>
          <input
            v-model="formData.name"
            type="text"
            class="input"
            placeholder="请输入资产名称"
          >
        </div>

        <!-- 资产类型 -->
        <div class="form-group">
          <label class="label"><span class="text-red-500">*</span>资产类型</label>
          <!-- <select
            v-model="formData.type"
            class="input"
          >
            <option value="">请选择资产类型</option>
            <option v-for="type in itemTypes" :key="type" :value="type">
              {{ type }}
            </option>
          </select> -->
          <div class="flex-1">
            <div
              class="w-full px-4 py-2 border border-gray-300 rounded-lg text-base bg-white"
              @click="showTypePicker = true"
            >
              <span v-if="typeName">{{ typeName }}</span>
              <span v-else class="text-gray-400">请选择资产类型</span>
            </div>
          </div>
        </div>
        <!-- 请选择资产类型弹窗 -->
        <van-popup
          v-model:show="showTypePicker"
          round
          position="bottom"
        >
          <van-cascader
            v-model="formData.type"
            title="请选择资产类型"
            :options="typeOptions"
            :field-names="{text: 'name', value: 'id'}"
            @close="showTypePicker = false"
            @finish="onTypeFinish"
          />
        </van-popup>
        <!-- 预购数量 -->
        <div class="form-group">
          <label class="label"><span class="text-red-500">*</span>预购数量</label>
          <input
            v-model="formData.quantity"
            type="number"
            class="input"
            placeholder="请输入预购数量"
            min="1"
          >
        </div>

        <!-- 预计单价 -->
        <div class="form-group">
          <label class="label"><span class="text-red-500">*</span>预计单价（元）</label>
          <input
            v-model="formData.unitPrice"
            type="number"
            class="input"
            placeholder="请输入预计单价"
            min="0.01"
            step="0.01"
          >
        </div>

        <!-- 总金额 -->
        <div class="form-group">
          <label class="label">总金额（元）</label>
          <div class="flex items-center">
            <span class="text-lg font-medium text-primary mr-1">¥</span>
            <span class="text-lg font-medium text-primary">{{ formData.totalPrice }}</span>
          </div>
        </div>

        <!-- 预计使用日期 -->
        <!-- <div class="form-group">
          <label class="label"><span class="text-red-500">*</span>预计使用日期</label>
          <div class="relative">
            <input
              v-model="formData.useTime"
              type="date"
              class="input pr-10"
              :min="new Date().toISOString().split('T')[0]"
              placeholder="请选择预计使用日期"
            >
            <i class="fas fa-calendar-alt absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"></i>
          </div>
        </div> -->
        <div class="form-group horizontal">
          <label class="label"><span class="text-red-500">*</span>预计使用日期</label>
          <div class="flex-1">
            <div
              class="w-full px-4 py-2 border border-gray-300 rounded-lg text-base bg-white"
              @click="showTimePicker = true"
            >
              <span v-if="formData.useTime && formData.useTime.length">{{ formData.useTime }}</span>
              <span v-else class="text-gray-400">请选择日期</span>
            </div>
          </div>
        </div>
        <van-popup
          v-model:show="showTimePicker"
          round
          position="bottom"
        >
          <van-date-picker
            v-model="timeValue"
            title="选择日期"
            :min-date="minDate"
            :max-date="maxDate"
            @confirm="timeConfirm"
            @cancel="showTimePicker = false;"
          />
        </van-popup>
        <!-- 用途说明 -->
        <div class="form-group">
          <label class="label"><span class="text-red-500">*</span>用途说明</label>
          <textarea
            v-model="formData.purpose"
            class="input min-h-[100px]"
            placeholder="请详细说明预购用途"
          ></textarea>
        </div>

        <!-- 备注 -->
        <div class="form-group">
          <label class="label">备注</label>
          <textarea
            v-model="formData.remark"
            class="input"
            placeholder="请输入备注信息（选填）"
          ></textarea>
        </div>

        <!-- 图片上传 -->
          <!-- <AttachmentUploader
            v-model="formData.images"
            :title="'图片'"
            :typeText="'上传图片'"
            :max-size="10 * 1024 * 1024"
            :accept="'.jpg,.jpeg,.png'"
          /> -->
          <div class="form-group">
            <label class="label">图片</label>
            <ImageUpload v-model="formData.images"/>
          </div>
        <!-- 附件上传 -->
          <!-- <AttachmentUploader
            v-model="formData.files"
            :title="'附件'"
            :typeText="'上传附件'"
            :max-size="10 * 1024 * 1024"
            :accept="'.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png'"
          /> -->
          <div class="form-group">
            <label class="label">附件</label>
            <NewFileUpload v-model="formData.files" />
          </div>
      </div>

      <!-- 提交按钮 -->
      <div class="fixed bottom-0 left-0 right-0 bg-white px-4 py-4 border-t border-gray-200">
        <button
          @click="submitForm"
          :disabled="isSubmitting"
          class="w-full py-3 bg-primary text-white rounded-lg text-sm font-medium relative overflow-hidden"
          :class="{ 'opacity-75 cursor-not-allowed': isSubmitting }"
        >
          <span :class="{ 'invisible': isSubmitting }">提交</span>
          <div
            v-if="isSubmitting"
            class="absolute inset-0 flex items-center justify-center"
          >
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="ml-2">提交中...</span>
          </div>
        </button>
      </div>
    </div>

    <!-- 成功提示弹窗 -->
    <div
      v-if="showSuccessModal"
      class="fixed inset-0 flex items-center justify-center z-50"
    >
      <!-- 背景遮罩 -->
      <div
        class="absolute inset-0 bg-black transition-opacity duration-300"
        :class="showSuccessModal ? 'bg-opacity-60' : 'bg-opacity-0'"
      ></div>

      <!-- 弹窗内容 -->
      <div
        class="bg-white rounded-2xl p-8 relative z-10 max-w-sm w-full mx-4 transform transition-all duration-500 ease-out"
        :class="showSuccessModal ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-4 scale-95'"
      >
        <div class="text-center">
          <!-- 成功图标 -->
          <div class="w-20 h-20 mx-auto mb-6 relative">
            <div class="absolute inset-0 bg-green-100 rounded-full animate-ripple"></div>
            <div class="absolute inset-0 flex items-center justify-center">
              <i class="fas fa-check text-3xl text-green-500 animate-success-icon"></i>
            </div>
          </div>

          <!-- 文本内容 -->
          <h3 class="text-xl font-semibold text-gray-900 mb-3">提交成功</h3>
          <p class="text-gray-600 mb-6">您的预购登记已成功提交</p>

          <!-- 进度条 -->
          <div class="w-full bg-gray-100 rounded-full h-1 mb-3 overflow-hidden">
            <div class="h-full bg-green-500 animate-progress"></div>
          </div>
          <div class="text-sm text-gray-500">即将返回上一页...</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 全局滚动条隐藏 */
::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

* {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
  overflow: -moz-scrollbars-none !important;
}

.page-container {
  @apply overflow-hidden;
}

.form-group {
  @apply space-y-1;
}

.label {
  @apply text-sm text-gray-600 block;
}

.label-inline {
  @apply text-sm text-gray-600;
}

.input {
  @apply w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-primary;
  -webkit-appearance: none;
  appearance: none;
}

@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes success-icon {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes progress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

.animate-ripple {
  animation: ripple 1.5s ease-out infinite;
}

.animate-success-icon {
  animation: success-icon 0.5s ease-out forwards;
}

.animate-progress {
  animation: progress 3s linear forwards;
}

/* 隐藏滚动条但保持可滚动 */
html::-webkit-scrollbar,
body::-webkit-scrollbar,
.page-container::-webkit-scrollbar,
.page-container *::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}

html,
body,
.page-container,
.page-container * {
  -ms-overflow-style: none !important;  /* IE and Edge */
  scrollbar-width: none !important;  /* Firefox */
}

/* 日期选择器样式 */
input[type="date"] {
  position: relative;
  background-color: transparent;
  cursor: pointer;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  color: transparent;
  cursor: pointer;
}

input[type="date"]::-webkit-datetime-edit {
  padding: 0;
}

input[type="date"]::-webkit-inner-spin-button {
  display: none;
}

/* 在未选择日期时显示占位符文本颜色 */
input[type="date"]:invalid {
  color: #9ca3af;
}
</style>
