# 创建部门选择器组件

<template>
  <div class="form-group flex-1" :style="{marginBottom: !hideTitle ? '1rem' : ''}">
    <label v-if="!hideTitle" class="label" :id="`${id}-label`"><span class="text-red-500" aria-hidden="true">*</span>{{ title }}</label>
    <div class="flex-1 relative">
      <div
        class="w-full px-4 py-2 border border-gray-300 rounded-lg text-base bg-white flex-1"
        @click="showPicker = true"
        role="button"
        tabindex="0"
        :aria-labelledby="`${id}-label`"
        :aria-expanded="showPicker"
        @keydown.enter="showPicker = true"
        @keydown.space.prevent="showPicker = true"
      >
        <span v-if="modelValue">{{ displayText || departmentName }}</span>
        <span v-else class="text-gray-400">{{ placeholder }}</span>
      </div>
      <img v-if="clearabled && modelValue" src="@/assets/images/clear.png" alt="" style="width: 24px;" class="absolute top-[9px] right-[4px]" @click="clearabledHandle">
    </div>
  </div>
  <!-- 部门选择弹窗 -->
  <van-popup
    v-model:show="showPicker"
    round
    position="bottom"
    :aria-labelledby="`${id}-label`"
    @closed="onPopupClosed"
  >
    <van-cascader
      v-model="cascaderValue"
      :title="title"
      :options="departmentOptions"
      @close="onClose"
      @finish="onFinish"
      role="dialog"
      :aria-label="title"
    />
  </van-popup>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { getHpWxworkDepartment } from '@/api/workwx'
import { showToast } from 'vant'

// 生成唯一ID
const id = `department-picker-${Math.random().toString(36).substr(2, 9)}`

// 定义部门数据类型
interface HpWxworkDepartment {
  id: string
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  updatedBy?: string
  remark?: string
  departmentId: number
  name: string
  parentId: number
  children?: HpWxworkDepartment[]
  wxworkOrder: number
  hasChildren?: boolean
}

// 级联选择器数据类型
interface CascaderOption {
  text: string
  value: number
  children?: CascaderOption[]
  [key: string]: unknown
}

// 级联选择器完成事件数据类型
interface FinishEvent {
  selectedOptions: CascaderOption[]
}

// 自定义字段名类型
interface FieldNames {
  text: string
  value: string
  children: string
}

// 定义组件选择完成后的返回数据
export interface FinishData {
  value: string | number
  displayText: string
  selectedOptions: CascaderOption[]
}

// 定义组件的属性
withDefaults(
  defineProps<{
    modelValue?: number
    placeholder?: string
    title?: string
    hideTitle: boolean
    departmentName?: string
    fieldNames?: FieldNames
    clearabled?: boolean
  }>(),
  {
    modelValue: undefined,
    departmentName: undefined,
    hideTitle: false,
    placeholder: '请选择部门',
    title: '请选择部门',
    clearabled: false,
    fieldNames: () => ({
      text: 'name',
      value: 'departmentId',
      children: 'children',
    }),
  },
)
// 定义组件的事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: string | number): void
  (e: 'finish', data: FinishData): void
  (e: 'close'): void
  (e: 'change', value: string | number): void
}>()

// 内部状态
const showPicker = ref(false)
const cascaderValue = ref<string | number>('')
const departmentOptions = ref<CascaderOption[]>([])
const isLoading = ref(false)
const selectedOptions = ref<CascaderOption[]>([])
const displayText = ref('')

// 处理数据加载
const loadDepartmentData = async () => {
  if (departmentOptions.value.length > 0) return

  isLoading.value = true
  try {
    const res = await getHpWxworkDepartment()
    // 将 API 返回的数据转换为级联选择器需要的格式
    let depts = removeEmptyChildren(res.items)
    if (depts.length == 1) {
      depts = depts[0].children || []
    }
    departmentOptions.value = transformDepartmentData(depts)
  } catch (error) {
    console.error('获取部门数据失败:', error)
    showToast('获取部门数据失败')
  } finally {
    isLoading.value = false
  }
}

// 转换部门数据为级联选择器格式
const transformDepartmentData = (departments: HpWxworkDepartment[]): CascaderOption[] => {
  return departments.map((dept) => {
    const option: CascaderOption = {
      text: dept.name,
      value: dept.departmentId,
    }

    if (dept.children && dept.children.length > 0) {
      option.children = transformDepartmentData(dept.children)
    }

    return option
  })
}

// 移除空的 children 数组
const removeEmptyChildren = (arr: HpWxworkDepartment[]): HpWxworkDepartment[] => {
  if (!arr || !Array.isArray(arr)) return []

  return arr.map((item) => {
    // 浅拷贝对象（避免修改原对象）
    const newItem = { ...item }

    // 递归处理子节点
    if (newItem.children && Array.isArray(newItem.children)) {
      const processedChildren = removeEmptyChildren(newItem.children)
      if (processedChildren.length > 0) {
        newItem.children = processedChildren
      } else {
        delete newItem.children // 删除空children字段
      }
    }

    return newItem
  })
}

// 处理弹窗关闭
const onClose = () => {
  showPicker.value = false
  emit('close')
}
const clearabledHandle = () => {
  emit('update:modelValue', 0)
  // emit('change', '')
}
// 处理选择完成
const onFinish = (data: FinishEvent) => {
  selectedOptions.value = data.selectedOptions
  // 更新显示文本（所有选中的名称，用/分隔）
  displayText.value = data.selectedOptions
    .map((option: CascaderOption) => option['text'])
    .join('/')

  // 获取最终选中的值（一般是最后一级的 ID）
  const finalValue = data.selectedOptions[data.selectedOptions.length - 1]['value'] as number
  // 更新 v-model
  emit('update:modelValue', finalValue)

  // 发出 finish 事件，包含完整的选中数据
  emit('finish', {
    value: finalValue,
    displayText: displayText.value,
    selectedOptions: data.selectedOptions,
  })

  showPicker.value = false
}

// 处理弹窗关闭后的清理
const onPopupClosed = () => {
  // 将焦点返回到触发器元素
  const triggerElement = document.querySelector(`[aria-labelledby="${id}-label"]`) as HTMLElement
  if (triggerElement) {
    triggerElement.focus()
  }
}

// 监听显示状态变化
watch(showPicker, (newVal) => {
  if (newVal) {
    loadDepartmentData()
  }
})

// 组件挂载时自动加载数据
onMounted(() => {
  loadDepartmentData()
})
</script>

<style scoped>
.department-picker {
  width: 100%;
}

.form-group.horizontal {
  display: flex;
  align-items: center;
}
.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #374151;
}

.form-group.horizontal .label {
  width: 7rem;
  margin-bottom: 0;
}
.form-group {
  /* margin-bottom: 1rem; */
}
</style>
