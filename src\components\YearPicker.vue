# 创建部门选择器组件

<template>
  <div class="form-group">
    <label class="label" :id="`${id}-label`"><span class="text-red-500" aria-hidden="true">*</span>{{ title }}</label>
    <div class="flex-1">
      <div
        class="w-full px-4 py-2 border border-gray-300 rounded-lg text-base bg-white"
        @click="showPicker = true"
        role="button"
        tabindex="0"
        :aria-labelledby="`${id}-label`"
        :aria-expanded="showPicker"
        @keydown.enter="showPicker = true"
        @keydown.space.prevent="showPicker = true"
      >
        <span v-if="modelValue">{{ modelValue }}年</span>
        <span v-else class="text-gray-400">{{ placeholder }}</span>
      </div>
    </div>
  </div>
  <!-- 年份选择弹窗 -->
  <van-popup
    v-model:show="showPicker"
    round
    position="bottom"
    :aria-labelledby="`${id}-label`"
    @closed="onPopupClosed"
  >
    <van-picker
      :title="title"
      :columns="yearColumns"
      @confirm="onConfirm"
      @cancel="onCancel"
      :default-index="defaultIndex"
    />
  </van-popup>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 生成唯一ID
const id = `year-picker-${Math.random().toString(36).substr(2, 9)}`

// 定义组件的属性
const props = withDefaults(
  defineProps<{
    modelValue?: string
    placeholder?: string
    title?: string
  }>(),
  {
    modelValue: undefined,
    placeholder: '请选择年份',
    title: '请选择年份',
  },
)

// 定义组件的事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}>()

// 内部状态
const showPicker = ref(false)

// 生成年份列表（最近10年）
const yearColumns = computed(() => {
  const currentYear = new Date().getFullYear()
  return Array.from({ length: 10 }, (_, index) => ({
    text: `${currentYear + index}年`,
    value: (currentYear + index).toString()
  }))
})

// 计算默认选中的索引
const defaultIndex = computed(() => {
  if (!props.modelValue) return 0
  const index = yearColumns.value.findIndex(item => item.value === props.modelValue)
  return index >= 0 ? index : 0
})

// 处理确认选择
const onConfirm = (value: { selectedValues: string[] }) => {
  emit('update:modelValue', value.selectedValues[0])
  emit('change', value.selectedValues[0])
  showPicker.value = false
}

// 处理取消选择
const onCancel = () => {
  showPicker.value = false
}

// 处理弹窗关闭后的清理
const onPopupClosed = () => {
  // 将焦点返回到触发器元素
  const triggerElement = document.querySelector(`[aria-labelledby="${id}-label"]`) as HTMLElement
  if (triggerElement) {
    triggerElement.focus()
  }
}
</script>

<style scoped>
.form-group {
  margin-bottom: 1rem;
}
.form-group.horizontal {
  display: flex;
  align-items: center;
}
.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #374151;
}

.form-group.horizontal .label {
  width: 7rem;
  margin-bottom: 0;
}
</style>
