import { defineConfig, loadEnv  } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
// const target = 'https://finance-api.119.net'
// const target = 'http://file.119.net:2308/basic-api'
// const target = 'https://finance.119.net/basic-api/'
// const target = 'http://ams.119.net/basic-api/'
// const target = 'http://192.168.20.253:8001/basic-api/'

// https://vitejs.dev/config/base: '/h5/', // 关键配置：设置静态资源基础路径
  // build: {
  //   outDir: 'dist/h5', // 指定输出目录为 dist/h5
  //   rollupOptions: {
  //     input: {
  //       main: resolve(__dirname, 'index.html') // 确保输入文件路径正确
  //     }
  //   }
  // }

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const target = env.VITE_BASE_URL
  console.log('打包环境===', target)
  return {
    base: '',
    publicDir: '/h5/',
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'), // 确保与 tsconfig.json 一致
      },
    },
    server: {
      allowedHosts: ['ams.119.net'],
      host: '0.0.0.0',
      proxy: {
        '/basic-api-demo': {
          target,
          changeOrigin: true,
          ws: true,
          rewrite: (path) => path.replace(new RegExp(`^/basic-api-demo`), ''),
          // only https
          // secure: false
        },
        '/basic-api': {
          target,
          changeOrigin: true,
          ws: true,
          rewrite: (path) => path.replace(new RegExp(`^/basic-api`), ''),
          // only https
          // secure: false
        },
        '/uploads': {
          target: `${target}/uploads`,
          changeOrigin: true,
          ws: true,
          rewrite: (path) => path.replace(new RegExp(`^/uploads`), ''),
        },
      },
    },
    plugins: [vue()],
  };
});
