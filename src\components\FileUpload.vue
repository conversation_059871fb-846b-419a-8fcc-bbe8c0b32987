<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

interface Attachment {
  name: string;
  url: string;
  size: number;
  type: string;
  progress?: number;
  status: 'uploading' | 'success' | 'error';
  errorMessage?: string;
}

interface Props {
  maxSize?: number;
  accept?: string[];
  multiple?: boolean;
  showPreview?: boolean;
  showDelete?: boolean;
  uploadUrl?: string;
  customUpload?: (file: File) => Promise<string>;
}

const props = withDefaults(defineProps<Props>(), {
  maxSize: 10 * 1024 * 1024, // 10MB
  accept: () => ['.jpg', '.jpeg', '.png', '.pdf', '.doc', '.docx', '.xls', '.xlsx'],
  multiple: true,
  showPreview: true,
  showDelete: true,
  uploadUrl: '/api/upload'
})

const emit = defineEmits<{
  (e: 'success', file: Attachment): void
  (e: 'error', file: Attachment, error: Error): void
  (e: 'remove', file: Attachment): void
  (e: 'update:modelValue', files: Attachment[]): void
}>()

// 文件列表
const attachments = ref<Attachment[]>([])

// 文件输入引用
const fileInput = ref<HTMLInputElement | null>(null)

// 触发文件选择
const triggerFileUpload = () => {
  fileInput.value?.click()
}

// 处理文件上传
const handleFileUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement
  if (!input.files?.length) return

  const files = Array.from(input.files)
  
  for (const file of files) {
    // 检查文件大小
    if (file.size > props.maxSize) {
      ElMessage.error(`文件 ${file.name} 超过大小限制 ${props.maxSize / 1024 / 1024}MB`)
      continue
    }

    // 检查文件类型
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
    if (!props.accept.includes(fileExtension)) {
      ElMessage.error(`文件 ${file.name} 格式不支持`)
      continue
    }

    // 创建附件对象
    const attachment: Attachment = {
      name: file.name,
      url: URL.createObjectURL(file),
      size: file.size,
      type: file.type,
      status: 'uploading',
      progress: 0
    }

    // 添加到附件列表
    attachments.value.push(attachment)

    try {
      // 如果提供了自定义上传函数，使用它
      if (props.customUpload) {
        const url = await props.customUpload(file)
        attachment.url = url
        attachment.status = 'success'
        attachment.progress = 100
        emit('success', attachment)
      } else {
        // 模拟上传进度
        const progress = setInterval(() => {
          const index = attachments.value.findIndex(item => item.name === file.name)
          if (index !== -1) {
            const currentProgress = attachments.value[index].progress || 0
            if (currentProgress < 100) {
              attachments.value[index].progress = currentProgress + 10
            } else {
              clearInterval(progress)
              attachments.value[index].status = 'success'
              emit('success', attachment)
            }
          }
        }, 300)

        // 注释掉实际的API调用
        /*
        const formData = new FormData()
        formData.append('file', file)

        const response = await axios.post(props.uploadUrl, formData, {
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              attachment.progress = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              )
            }
          }
        })

        attachment.status = 'success'
        attachment.progress = 100
        attachment.url = response.data.url || attachment.url
        emit('success', attachment)
        */
      }
    } catch (error) {
      attachment.status = 'error'
      attachment.errorMessage = error instanceof Error ? error.message : '上传失败'
      emit('error', attachment, error instanceof Error ? error : new Error('上传失败'))
    }
  }

  // 清空input，允许重复上传相同文件
  input.value = ''
}

// 预览附件
const previewAttachment = (attachment: Attachment) => {
  if (attachment.type.startsWith('image/')) {
    // 图片预览
    window.open(attachment.url, '_blank')
  } else {
    // 文档预览
    window.open(attachment.url, '_blank')
  }
}

// 删除附件
const removeAttachment = (index: number) => {
  const attachment = attachments.value[index]
  URL.revokeObjectURL(attachment.url)
  attachments.value.splice(index, 1)
  emit('remove', attachment)
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  }
}

// 获取接受的文件类型字符串
const acceptString = computed(() => {
  return props.accept.join(',')
})
</script>

<template>
  <div class="file-upload">
    <!-- 上传按钮 -->
    <div class="flex items-center space-x-3">
      <div class="relative shrink-0">
        <input
          ref="fileInput"
          type="file"
          :multiple="multiple"
          class="hidden"
          @change="handleFileUpload"
          :accept="acceptString"
        >
        <button
          type="button"
          @click="triggerFileUpload"
          class="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 bg-white hover:bg-gray-50"
        >
          <i class="fas fa-upload mr-2"></i>
          上传附件
        </button>
      </div>
    </div>
    <div class="text-xs text-gray-500 mt-1">
      支持以下格式：JPG、PNG、PDF、Word、Excel，单个文件不超过10MB
    </div>
    <!-- 附件列表 -->
    <div v-if="attachments.length > 0" class="mt-4 space-y-3">
      <div
        v-for="(file, index) in attachments"
        :key="index"
        class="relative p-3 bg-gray-50 rounded-lg"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2 flex-1 min-w-0">
            <!-- 文件信息 -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-2">
                <div 
                  class="text-sm text-gray-700 truncate cursor-pointer hover:text-primary"
                  @click="previewAttachment(file)"
                >{{ file.name }}</div>
                <span 
                  :class="[
                    'text-xs px-2 py-0.5 rounded',
                    file.status === 'success' ? 'bg-green-100 text-green-800' :
                    file.status === 'error' ? 'bg-red-100 text-red-800' :
                    'bg-blue-100 text-blue-800'
                  ]"
                >
                  {{ 
                    file.status === 'success' ? '已上传' :
                    file.status === 'error' ? '上传失败' :
                    '上传中'
                  }}
                </span>
              </div>
              <div class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</div>
              
              <!-- 错误信息 -->
              <div v-if="file.status === 'error'" class="text-xs text-red-500 mt-1">
                {{ file.errorMessage }}
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center space-x-2">
            <button
              v-if="showPreview && file.status === 'success'"
              @click="previewAttachment(file)"
              class="text-blue-600 hover:text-blue-800"
              title="预览"
            >
              <i class="fas fa-eye"></i>
            </button>
            <button
              v-if="showDelete"
              @click="removeAttachment(index)"
              class="text-gray-400 hover:text-red-500"
              title="删除"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <!-- 上传进度条 -->
        <div 
          v-if="file.status === 'uploading'"
          class="mt-2"
        >
          <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
            <div
              class="h-full bg-blue-500 transition-all duration-300"
              :style="{ width: `${file.progress}%` }"
            ></div>
          </div>
          <div class="text-xs text-gray-500 mt-1">
            {{ file.progress }}%
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.file-upload {
  /* @apply w-full; */
  width: 100%;
  box-sizing: border-box;
}

.text-primary {
  --tw-text-opacity: 1;
  color: rgb(24 144 255 / var(--tw-text-opacity));
}

.hover\:text-primary:hover {
  --tw-text-opacity: 1;
  color: rgb(24 144 255 / var(--tw-text-opacity));
}
</style> 